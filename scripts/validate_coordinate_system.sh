#!/bin/bash

# 坐标系验证脚本
# 用于快速验证坐标转换算法和配置

echo "=== 万亩数田坐标系验证工具 ==="
echo "时间: $(date)"
echo ""

# 检查项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
echo "项目根目录: $PROJECT_ROOT"

# 检查配置文件是否存在
CONFIG_FILE="$PROJECT_ROOT/app/src/main/assets/tile_sources_config.json"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

echo "✅ 配置文件存在: tile_sources_config.json"

# 检查配置文件格式
echo ""
echo "=== 检查配置文件格式 ==="

# 使用 jq 验证 JSON 格式 (如果安装了的话)
if command -v jq &> /dev/null; then
    if jq empty "$CONFIG_FILE" 2>/dev/null; then
        echo "✅ JSON 格式正确"
        
        # 提取瓦片源信息
        echo ""
        echo "=== 瓦片源配置检查 ==="
        
        jq -r '.tileSources[] | "ID: \(.id), 坐标系: \(.coordinateSystem // "未配置"), 需要转换: \(.needsConversion // false)"' "$CONFIG_FILE"
        
        # 检查吉林一号配置
        JILIN_COORD=$(jq -r '.tileSources[] | select(.id=="jilin1-satellite") | .coordinateSystem' "$CONFIG_FILE")
        JILIN_CONVERSION=$(jq -r '.tileSources[] | select(.id=="jilin1-satellite") | .needsConversion' "$CONFIG_FILE")
        
        if [ "$JILIN_COORD" = "EPSG:3857" ] && [ "$JILIN_CONVERSION" = "true" ]; then
            echo "✅ 吉林一号配置正确: EPSG:3857, 需要转换"
        else
            echo "❌ 吉林一号配置错误: 坐标系=$JILIN_COORD, 需要转换=$JILIN_CONVERSION"
        fi
        
        # 检查天地图配置
        TIANDITU_COORD=$(jq -r '.tileSources[] | select(.id=="tianditu-satellite") | .coordinateSystem' "$CONFIG_FILE")
        TIANDITU_CONVERSION=$(jq -r '.tileSources[] | select(.id=="tianditu-satellite") | .needsConversion' "$CONFIG_FILE")
        
        if [ "$TIANDITU_COORD" = "GCJ02" ] && [ "$TIANDITU_CONVERSION" = "false" ]; then
            echo "✅ 天地图配置正确: GCJ02, 无需转换"
        else
            echo "❌ 天地图配置错误: 坐标系=$TIANDITU_COORD, 需要转换=$TIANDITU_CONVERSION"
        fi
        
    else
        echo "❌ JSON 格式错误"
        jq empty "$CONFIG_FILE"
        exit 1
    fi
else
    echo "⚠️  jq 未安装，跳过 JSON 格式验证"
fi

# 检查 Kotlin 源文件是否存在
echo ""
echo "=== 检查源文件完整性 ==="

SOURCE_FILES=(
    "app/src/main/java/cn/agrolinking/wmst/map/model/TileSource.kt"
    "app/src/main/java/cn/agrolinking/wmst/map/coordinate/CoordinateConverter.kt"
    "app/src/main/java/cn/agrolinking/wmst/map/coordinate/CoordinateSystemDetector.kt"
    "app/src/main/java/cn/agrolinking/wmst/map/manager/TileSourceManager.kt"
    "app/src/main/java/cn/agrolinking/wmst/map/util/CoordinateSystemValidator.kt"
)

for file in "${SOURCE_FILES[@]}"; do
    if [ -f "$PROJECT_ROOT/$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (缺失)"
    fi
done

# 检查坐标系枚举定义
echo ""
echo "=== 检查坐标系枚举定义 ==="

TILE_SOURCE_FILE="$PROJECT_ROOT/app/src/main/java/cn/agrolinking/wmst/map/model/TileSource.kt"
if [ -f "$TILE_SOURCE_FILE" ]; then
    if grep -q "enum class CoordinateSystem" "$TILE_SOURCE_FILE"; then
        echo "✅ CoordinateSystem 枚举已定义"
        
        # 检查具体的坐标系常量
        if grep -q "EPSG_3857" "$TILE_SOURCE_FILE"; then
            echo "✅ EPSG:3857 常量已定义"
        else
            echo "❌ EPSG:3857 常量缺失"
        fi
        
        if grep -q "GCJ02" "$TILE_SOURCE_FILE"; then
            echo "✅ GCJ02 常量已定义"
        else
            echo "❌ GCJ02 常量缺失"
        fi
    else
        echo "❌ CoordinateSystem 枚举未定义"
    fi
fi

# 检查转换算法核心方法
echo ""
echo "=== 检查转换算法 ==="

CONVERTER_FILE="$PROJECT_ROOT/app/src/main/java/cn/agrolinking/wmst/map/coordinate/CoordinateConverter.kt"
if [ -f "$CONVERTER_FILE" ]; then
    
    REQUIRED_METHODS=(
        "fun convert"
        "epsg3857ToWgs84"
        "wgs84ToGcj02"
        "gcj02ToWgs84"
    )
    
    for method in "${REQUIRED_METHODS[@]}"; do
        if grep -q "$method" "$CONVERTER_FILE"; then
            echo "✅ $method 方法已实现"
        else
            echo "❌ $method 方法缺失"
        fi
    done
else
    echo "❌ CoordinateConverter.kt 文件缺失"
fi

# 简单的坐标转换数学验证
echo ""
echo "=== 数学验证 (理论计算) ==="

# 北京天安门已知偏移量验证
echo "测试点: 北京天安门"
echo "WGS84: (116.3974, 39.9093)"
echo "期望GCJ02: 约 (116.404, 39.913)"
echo "理论偏移: 约 400-600 米"

# 计算球面距离的简单验证
WGS84_LON=116.3974
WGS84_LAT=39.9093
GCJ02_LON=116.404
GCJ02_LAT=39.913

# 使用 bc 计算距离 (如果可用)
if command -v bc &> /dev/null; then
    # 简化的距离计算 (度转米的近似)
    LON_DIFF=$(echo "$GCJ02_LON - $WGS84_LON" | bc -l)
    LAT_DIFF=$(echo "$GCJ02_LAT - $WGS84_LAT" | bc -l)
    
    # 1 度约等于 111,000 米 (粗略估算)
    DISTANCE_M=$(echo "sqrt(($LON_DIFF * 85000)^2 + ($LAT_DIFF * 111000)^2)" | bc -l)
    echo "计算得到偏移距离: $(printf "%.0f" $DISTANCE_M) 米"
    
    if (( $(echo "$DISTANCE_M > 300 && $DISTANCE_M < 800" | bc -l) )); then
        echo "✅ 偏移距离在合理范围内 (300-800米)"
    else
        echo "⚠️  偏移距离可能异常"
    fi
else
    echo "⚠️  bc 未安装，跳过数学验证"
fi

# 总结
echo ""
echo "=== 验证总结 ==="
echo "📋 配置文件检查: 完成"
echo "📁 源文件检查: 完成" 
echo "🔧 算法检查: 完成"
echo "🧮 数学验证: 完成"
echo ""
echo "💡 建议:"
echo "1. 在应用中运行完整验证 (CoordinateSystemValidator)"
echo "2. 使用真实地图数据进行视觉验证"
echo "3. 在不同地区测试偏移效果"
echo ""
echo "📱 下一步: 在 Android 应用中打开验证对话框进行完整测试"
echo ""
echo "=== 验证完成 ===" 