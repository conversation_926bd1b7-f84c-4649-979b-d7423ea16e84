#!/bin/bash

# 🍎 Apple Silicon Mac Android模拟器启动脚本
# 适用于 macOS Sequoia (15.x) 和 Apple Silicon (M1/M2/M3/M4) 芯片
# 解决模拟器GUI崩溃问题，使用无头模式运行

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认AVD名称
DEFAULT_AVD="Medium_Phone_API_35"
AVD_NAME=${1:-$DEFAULT_AVD}

echo -e "${BLUE}🍎 Apple Silicon Mac Android模拟器启动脚本${NC}"
echo -e "${BLUE}================================================${NC}"

# 检查Android SDK环境
if [ -z "$ANDROID_HOME" ]; then
    echo -e "${RED}❌ 错误: ANDROID_HOME 环境变量未设置${NC}"
    echo -e "${YELLOW}请在 ~/.zshrc 或 ~/.bash_profile 中设置:${NC}"
    echo -e "${YELLOW}export ANDROID_HOME=\$HOME/Library/Android/sdk${NC}"
    exit 1
fi

# 检查模拟器可执行文件
EMULATOR_PATH="$ANDROID_HOME/emulator/emulator"
if [ ! -f "$EMULATOR_PATH" ]; then
    echo -e "${RED}❌ 错误: 找不到Android模拟器${NC}"
    echo -e "${YELLOW}路径: $EMULATOR_PATH${NC}"
    exit 1
fi

# 检查AVD是否存在
echo -e "${BLUE}🔍 检查可用的AVD...${NC}"
AVAILABLE_AVDS=$($EMULATOR_PATH -list-avds)
if [ -z "$AVAILABLE_AVDS" ]; then
    echo -e "${RED}❌ 错误: 没有找到可用的AVD${NC}"
    echo -e "${YELLOW}请先在Android Studio中创建AVD${NC}"
    exit 1
fi

echo -e "${GREEN}📱 可用的AVD:${NC}"
echo "$AVAILABLE_AVDS"

if ! echo "$AVAILABLE_AVDS" | grep -q "^$AVD_NAME$"; then
    echo -e "${RED}❌ 错误: AVD '$AVD_NAME' 不存在${NC}"
    echo -e "${YELLOW}使用方法: $0 [AVD名称]${NC}"
    echo -e "${YELLOW}例如: $0 Pixel_6_API_34${NC}"
    exit 1
fi

# 清理现有的模拟器进程
echo -e "${YELLOW}🧹 清理现有的模拟器进程...${NC}"
pkill -f "emulator.*$AVD_NAME" 2>/dev/null || true
sleep 2

# 重启ADB服务
echo -e "${YELLOW}🔄 重启ADB服务...${NC}"
adb kill-server >/dev/null 2>&1 || true
adb start-server >/dev/null 2>&1

# 启动无头模拟器
echo -e "${GREEN}🚀 启动无头模拟器: $AVD_NAME${NC}"
echo -e "${BLUE}使用参数: -gpu swiftshader_indirect -no-snapshot -no-audio -no-window${NC}"

# 启动模拟器（后台运行）
$EMULATOR_PATH -avd "$AVD_NAME" \
    -gpu swiftshader_indirect \
    -no-snapshot \
    -no-audio \
    -no-window \
    > /tmp/emulator.log 2>&1 &

EMULATOR_PID=$!
echo -e "${GREEN}✅ 模拟器进程已启动 (PID: $EMULATOR_PID)${NC}"

# 等待模拟器启动
echo -e "${YELLOW}⏳ 等待模拟器启动完成...${NC}"
WAIT_TIME=0
MAX_WAIT=60

while [ $WAIT_TIME -lt $MAX_WAIT ]; do
    if adb devices 2>/dev/null | grep -q "emulator.*device"; then
        break
    fi
    echo -n "."
    sleep 2
    WAIT_TIME=$((WAIT_TIME + 2))
done

echo ""

# 检查启动结果
if adb devices 2>/dev/null | grep -q "emulator.*device"; then
    echo -e "${GREEN}🎉 模拟器启动成功！${NC}"
    echo -e "${GREEN}📱 设备状态:${NC}"
    adb devices
    
    # 显示模拟器信息
    DEVICE_ID=$(adb devices | grep emulator | awk '{print $1}')
    if [ -n "$DEVICE_ID" ]; then
        echo -e "${BLUE}📊 设备信息:${NC}"
        echo -e "${BLUE}  设备ID: $DEVICE_ID${NC}"
        echo -e "${BLUE}  Android版本: $(adb shell getprop ro.build.version.release 2>/dev/null || echo '未知')${NC}"
        echo -e "${BLUE}  API级别: $(adb shell getprop ro.build.version.sdk 2>/dev/null || echo '未知')${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}🛠️  现在可以进行开发调试了:${NC}"
    echo -e "${YELLOW}  构建应用: ./gradlew clean assembleDebug${NC}"
    echo -e "${YELLOW}  安装应用: ./gradlew installDebug${NC}"
    echo -e "${YELLOW}  启动应用: adb shell am start -n cn.agrolinking.wmst/.ui.MainActivity${NC}"
    echo -e "${YELLOW}  查看日志: adb logcat -s 'WMST'${NC}"
    echo -e "${YELLOW}  截屏: adb shell screencap -p /sdcard/screenshot.png${NC}"
    echo ""
    echo -e "${BLUE}💡 提示: 无头模式下无法看到模拟器界面，但所有功能正常工作${NC}"
    
else
    echo -e "${RED}❌ 模拟器启动失败${NC}"
    echo -e "${YELLOW}📋 故障排除:${NC}"
    echo -e "${YELLOW}  1. 检查日志: tail -f /tmp/emulator.log${NC}"
    echo -e "${YELLOW}  2. 检查进程: ps aux | grep emulator${NC}"
    echo -e "${YELLOW}  3. 重新运行脚本: $0 $AVD_NAME${NC}"
    
    # 显示日志的最后几行
    if [ -f /tmp/emulator.log ]; then
        echo -e "${YELLOW}📝 最近的日志:${NC}"
        tail -10 /tmp/emulator.log
    fi
    
    exit 1
fi 