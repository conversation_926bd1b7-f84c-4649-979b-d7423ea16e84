plugins {
    id 'com.android.application' version '8.2.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.20' apply false
    id 'com.google.dagger.hilt.android' version '2.50' apply false
    id 'com.google.devtools.ksp' version '1.9.20-1.0.14' apply false
}

// 强制指定 Kotlin 版本，防止自动升级
allprojects {
    configurations.all {
        resolutionStrategy {
            force 'org.jetbrains.kotlin:kotlin-stdlib:1.9.20'
            force 'org.jetbrains.kotlin:kotlin-stdlib-common:1.9.20'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20'
        }
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}