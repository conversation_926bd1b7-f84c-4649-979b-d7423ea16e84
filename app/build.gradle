plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'dagger.hilt.android.plugin'
    id 'com.google.devtools.ksp'
}

android {
    compileSdk 34

    defaultConfig {
        applicationId "cn.agrolinking.wmst"
        minSdk 23
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        compose true
        buildConfig = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.4"
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
    namespace 'cn.agrolinking.wmst'
    
    lint {
        baseline = file("lint-baseline.xml")
    }
}

dependencies {
    def composeBom = platform('androidx.compose:compose-bom:2023.10.01')
    implementation composeBom
    androidTestImplementation composeBom
    implementation "androidx.compose.ui:ui"
    implementation "androidx.compose.material3:material3"
    implementation "androidx.compose.material:material-icons-extended"
    implementation "androidx.compose.ui:ui-tooling-preview"
    androidTestImplementation "androidx.compose.ui:ui-test-junit4"
    debugImplementation "androidx.compose.ui:ui-tooling"
    implementation "androidx.lifecycle:lifecycle-runtime-compose:2.7.0"

    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.robolectric:robolectric:4.11.1'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    //==================== Dependency Injection ====================
    def hilt_version = "2.50"
    implementation "com.google.dagger:hilt-android:$hilt_version"
    ksp "com.google.dagger:hilt-compiler:$hilt_version"
    implementation 'androidx.hilt:hilt-navigation-compose:1.1.0'

    //==================== Networking ====================
    def retrofit_version = '2.9.0'
    def okhttp_version = '4.12.0'
    implementation "com.squareup.retrofit2:retrofit:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-moshi:$retrofit_version"
    implementation "com.squareup.okhttp3:okhttp:$okhttp_version"
    implementation "com.squareup.okhttp3:logging-interceptor:$okhttp_version"

    //==================== Database ====================
    def room_version = '2.6.1'
    implementation "androidx.room:room-runtime:$room_version"
    ksp "androidx.room:room-compiler:$room_version"
    implementation "androidx.room:room-ktx:$room_version"

    //==================== Deserializer ====================
    def moshi_version = '1.14.0'
    ksp "com.squareup.moshi:moshi-kotlin-codegen:$moshi_version"
    implementation "com.squareup.moshi:moshi:$moshi_version"
    implementation "com.squareup.moshi:moshi-kotlin:$moshi_version"
    implementation "com.squareup.moshi:moshi-adapters:$moshi_version"

    //==================== Logging ====================
    implementation "com.jakewharton.timber:timber:5.0.1"

    //==================== Image Loading ====================
    implementation "io.coil-kt:coil-compose:2.5.0"

    //==================== Navigation ====================
    implementation "androidx.navigation:navigation-compose:2.7.6"

    //==================== MapLibre GL Native ====================
    implementation 'org.maplibre.gl:android-sdk:11.10.3'
    
    //==================== MapLibre Plugins ====================
    // 仅保留地块圈选功能必需的插件
    implementation 'org.maplibre.gl:android-plugin-scalebar-v9:3.0.2'        // ✅ 比例尺显示
    implementation 'org.maplibre.gl:android-plugin-annotation-v9:3.0.2'      // ✅ 现代注释管理器 (SymbolManager, FillManager 等)
    implementation 'org.maplibre.gl:android-plugin-offline-v9:3.0.2'         // ✅ 离线地图功能 (待验证是否与SDK内置功能重复)

    //==================== Location Services ====================
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    implementation 'androidx.lifecycle:lifecycle-service:2.7.0'

    //==================== Memory Leak Detection ====================
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.9.1'
}