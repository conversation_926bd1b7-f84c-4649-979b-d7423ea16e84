package cn.agrolinking.wmst.util

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import androidx.core.content.ContextCompat

/**
 * 权限处理工具类
 */
object PermissionUtils {
    
    /**
     * 检查位置权限是否已授权
     */
    fun hasLocationPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED ||
        ContextCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 获取位置权限数组
     */
    fun getLocationPermissions(): Array<String> {
        return arrayOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )
    }
}

/**
 * Compose中使用的位置权限处理Hook
 */
@Composable
fun rememberLocationPermissionState(
    onPermissionResult: (Boolean) -> Unit
): LocationPermissionState {
    val context = LocalContext.current
    
    var permissionGranted by remember {
        mutableStateOf(PermissionUtils.hasLocationPermission(context))
    }
    
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val granted = permissions.values.any { it }
        permissionGranted = granted
        onPermissionResult(granted)
    }
    
    return remember {
        LocationPermissionState(
            hasPermission = permissionGranted,
            launcher = launcher
        )
    }
}

/**
 * 位置权限状态
 */
data class LocationPermissionState(
    val hasPermission: Boolean,
    private val launcher: ManagedActivityResultLauncher<Array<String>, Map<String, Boolean>>
) {
    /**
     * 请求位置权限
     */
    fun requestPermission() {
        launcher.launch(PermissionUtils.getLocationPermissions())
    }
} 