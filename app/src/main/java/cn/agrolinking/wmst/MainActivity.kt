package cn.agrolinking.wmst

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Assignment
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Map
import androidx.compose.material.icons.filled.NoteAdd
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.foundation.layout.height
import cn.agrolinking.wmst.ui.screens.dashboard.DashboardScreen
import cn.agrolinking.wmst.ui.screens.map.MapScreen
import cn.agrolinking.wmst.ui.screens.profile.ProfileScreen
import cn.agrolinking.wmst.ui.screens.records.RecordsScreen
import cn.agrolinking.wmst.ui.screens.tasks.TasksScreen
import cn.agrolinking.wmst.ui.theme.WmstTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * 万亩数田App - 作业人员主界面
 * 包含5个主要功能模块：首页、工单、地图、记录、我的
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            WmstTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainScreen()
                }
            }
        }
    }
}

@Composable
fun MainScreen() {
    var selectedTab by remember { mutableIntStateOf(0) }
    
    val tabs = listOf(
        TabItem("首页", Icons.Default.Home),
        TabItem("工单", Icons.Default.Assignment),
        TabItem("地图", Icons.Default.Map),
        TabItem("记录", Icons.Default.NoteAdd),
        TabItem("我的", Icons.Default.Person)
    )
    
    Scaffold(
        bottomBar = {
            Box(
                modifier = Modifier.background(Color.Black.copy(alpha = 0.5f)) // 半透明黑色
            ) {
                NavigationBar(
                    containerColor = Color.Transparent, // 让NavigationBar本身透明
                    modifier = Modifier.height(56.dp) // 降低导航栏高度
                ) {
                    tabs.forEachIndexed { index, tab ->
                        NavigationBarItem(
                            icon = { }, // 移除图标
                            label = { 
                                Text(
                                    text = tab.title,
                                    fontSize = 16.sp, // 增大字体
                                    fontWeight = FontWeight.Bold,
                                    textAlign = TextAlign.Center
                                ) 
                            },
                            selected = selectedTab == index,
                            onClick = { selectedTab = index },
                            colors = NavigationBarItemDefaults.colors(
                                indicatorColor = Color.Transparent, // 移除选中指示器
                                selectedTextColor = Color(0xFFFF9800), // 选中文字橙色
                                unselectedTextColor = Color.White // 未选中文字白色
                            )
                        )
                    }
                }
            }
        }
    ) { _ ->
        // 移除padding让内容延伸到底部
        when (selectedTab) {
            0 -> DashboardScreen()
            1 -> TasksScreen()
            2 -> MapScreen()
            3 -> RecordsScreen()
            4 -> ProfileScreen()
        }
    }
}

data class TabItem(
    val title: String,
    val icon: ImageVector
)

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    WmstTheme {
        MainScreen()
    }
} 