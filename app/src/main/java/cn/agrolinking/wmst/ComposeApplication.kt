package cn.agrolinking.wmst

import android.app.Application
import android.util.Log
import dagger.hilt.android.HiltAndroidApp
import org.maplibre.android.MapLibre
import timber.log.Timber

@HiltAndroidApp
class ComposeApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        Log.d("AppLifecycle", "ComposeApplication.onCreate() called")
        
        // 根据官方文档，MapLibre SDK 必须在应用启动时进行全局初始化
        MapLibre.getInstance(this)

        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        }
    }

}