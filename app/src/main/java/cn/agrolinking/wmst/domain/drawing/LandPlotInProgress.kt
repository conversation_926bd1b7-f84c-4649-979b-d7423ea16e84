package cn.agrolinking.wmst.domain.drawing

import cn.agrolinking.wmst.domain.geojson.MultiPolygonGeometry
import cn.agrolinking.wmst.domain.geojson.LandPlotFeature
import cn.agrolinking.wmst.domain.geojson.LandPlotProperties
import org.maplibre.android.geometry.LatLng

/**
 * 地块绘制进行中的数据结构
 * 用于管理复杂几何体（多多边形+孔洞）的绘制状态
 */
data class LandPlotInProgress(
    val polygons: List<PolygonInProgress> = listOf(PolygonInProgress()),
    val activePolygonIndex: Int = 0,
    val activeRingIndex: Int = 0, // 0=外边界, 1+=孔洞
    val metadata: Map<String, Any> = emptyMap()
) {
    
    /**
     * 获取当前正在绘制的多边形
     */
    fun getCurrentPolygon(): PolygonInProgress? {
        return polygons.getOrNull(activePolygonIndex)
    }
    
    /**
     * 获取当前正在绘制的环（外边界或孔洞）
     */
    fun getCurrentRing(): List<LatLng> {
        val polygon = getCurrentPolygon() ?: return emptyList()
        return when (activeRingIndex) {
            0 -> polygon.exterior
            else -> polygon.holes.getOrNull(activeRingIndex - 1) ?: emptyList()
        }
    }
    
    /**
     * 添加顶点到当前绘制的环
     */
    fun addVertexToCurrentRing(vertex: LatLng): LandPlotInProgress {
        val currentPolygon = getCurrentPolygon() ?: return this
        
        val updatedPolygon = when (activeRingIndex) {
            0 -> {
                // 添加到外边界
                currentPolygon.copy(exterior = currentPolygon.exterior + vertex)
            }
            else -> {
                // 添加到孔洞
                val holeIndex = activeRingIndex - 1
                val updatedHoles = currentPolygon.holes.toMutableList()
                
                // 确保孔洞列表足够长
                while (updatedHoles.size <= holeIndex) {
                    updatedHoles.add(emptyList())
                }
                
                updatedHoles[holeIndex] = updatedHoles[holeIndex] + vertex
                currentPolygon.copy(holes = updatedHoles)
            }
        }
        
        val updatedPolygons = polygons.toMutableList()
        updatedPolygons[activePolygonIndex] = updatedPolygon
        
        return copy(polygons = updatedPolygons)
    }
    
    /**
     * 更新当前环的指定顶点
     */
    fun updateVertexInCurrentRing(vertexIndex: Int, newVertex: LatLng): LandPlotInProgress {
        val currentPolygon = getCurrentPolygon() ?: return this
        
        val updatedPolygon = when (activeRingIndex) {
            0 -> {
                // 更新外边界顶点
                val updatedExterior = currentPolygon.exterior.toMutableList()
                if (vertexIndex in updatedExterior.indices) {
                    updatedExterior[vertexIndex] = newVertex
                }
                currentPolygon.copy(exterior = updatedExterior)
            }
            else -> {
                // 更新孔洞顶点
                val holeIndex = activeRingIndex - 1
                val updatedHoles = currentPolygon.holes.toMutableList()
                
                if (holeIndex in updatedHoles.indices) {
                    val updatedHole = updatedHoles[holeIndex].toMutableList()
                    if (vertexIndex in updatedHole.indices) {
                        updatedHole[vertexIndex] = newVertex
                        updatedHoles[holeIndex] = updatedHole
                    }
                }
                
                currentPolygon.copy(holes = updatedHoles)
            }
        }
        
        val updatedPolygons = polygons.toMutableList()
        updatedPolygons[activePolygonIndex] = updatedPolygon
        
        return copy(polygons = updatedPolygons)
    }
    
    /**
     * 在指定位置插入顶点到当前环
     */
    fun insertVertexInCurrentRing(insertIndex: Int, newVertex: LatLng): LandPlotInProgress {
        val currentPolygon = getCurrentPolygon() ?: return this
        
        val updatedPolygon = when (activeRingIndex) {
            0 -> {
                // 插入到外边界
                val updatedExterior = currentPolygon.exterior.toMutableList()
                updatedExterior.add(insertIndex, newVertex)
                currentPolygon.copy(exterior = updatedExterior)
            }
            else -> {
                // 插入到孔洞
                val holeIndex = activeRingIndex - 1
                val updatedHoles = currentPolygon.holes.toMutableList()
                
                if (holeIndex in updatedHoles.indices) {
                    val updatedHole = updatedHoles[holeIndex].toMutableList()
                    updatedHole.add(insertIndex, newVertex)
                    updatedHoles[holeIndex] = updatedHole
                }
                
                currentPolygon.copy(holes = updatedHoles)
            }
        }
        
        val updatedPolygons = polygons.toMutableList()
        updatedPolygons[activePolygonIndex] = updatedPolygon
        
        return copy(polygons = updatedPolygons)
    }
    
    /**
     * 移除当前环的指定顶点
     */
    fun removeVertexFromCurrentRing(vertexIndex: Int): LandPlotInProgress {
        val currentPolygon = getCurrentPolygon() ?: return this
        
        val updatedPolygon = when (activeRingIndex) {
            0 -> {
                // 从外边界移除
                val updatedExterior = currentPolygon.exterior.toMutableList()
                if (vertexIndex in updatedExterior.indices) {
                    updatedExterior.removeAt(vertexIndex)
                }
                currentPolygon.copy(exterior = updatedExterior)
            }
            else -> {
                // 从孔洞移除
                val holeIndex = activeRingIndex - 1
                val updatedHoles = currentPolygon.holes.toMutableList()
                
                if (holeIndex in updatedHoles.indices) {
                    val updatedHole = updatedHoles[holeIndex].toMutableList()
                    if (vertexIndex in updatedHole.indices) {
                        updatedHole.removeAt(vertexIndex)
                        updatedHoles[holeIndex] = updatedHole
                    }
                }
                
                currentPolygon.copy(holes = updatedHoles)
            }
        }
        
        val updatedPolygons = polygons.toMutableList()
        updatedPolygons[activePolygonIndex] = updatedPolygon
        
        return copy(polygons = updatedPolygons)
    }
    
    /**
     * 添加新的多边形
     */
    fun addNewPolygon(): LandPlotInProgress {
        val newPolygon = PolygonInProgress(
            exterior = emptyList(),
            holes = emptyList()
        )
        
        return copy(
            polygons = polygons + newPolygon,
            activePolygonIndex = polygons.size,
            activeRingIndex = 0
        )
    }
    
    /**
     * 开始绘制当前多边形的孔洞
     */
    fun startDrawingHole(): LandPlotInProgress {
        val currentPolygon = getCurrentPolygon() ?: return this
        
        // 确保当前多边形有外边界
        if (currentPolygon.exterior.size < 3) {
            return this
        }
        
        // 添加新的空孔洞
        val updatedPolygon = currentPolygon.copy(
            holes = currentPolygon.holes + emptyList()
        )
        
        val updatedPolygons = polygons.toMutableList()
        updatedPolygons[activePolygonIndex] = updatedPolygon
        
        return copy(
            polygons = updatedPolygons,
            activeRingIndex = currentPolygon.holes.size + 1
        )
    }
    
    /**
     * 切换到指定的多边形和环
     */
    fun switchToPolygonRing(polygonIndex: Int, ringIndex: Int): LandPlotInProgress {
        if (polygonIndex !in polygons.indices) return this
        
        val polygon = polygons[polygonIndex]
        val maxRingIndex = polygon.holes.size // 0=外边界, 1+=孔洞
        
        if (ringIndex !in 0..maxRingIndex) return this
        
        return copy(
            activePolygonIndex = polygonIndex,
            activeRingIndex = ringIndex
        )
    }
    
    /**
     * 转换为GeoJSON几何体
     */
    fun toGeoJsonGeometry(): MultiPolygonGeometry {
        val coordinates = polygons.map { polygon ->
            val rings = mutableListOf<List<List<Double>>>()
            
            // 外边界
            if (polygon.exterior.isNotEmpty()) {
                val exteriorCoords = polygon.exterior.map { 
                    listOf(it.longitude, it.latitude)
                }.toMutableList()
                
                // 确保外边界闭合
                if (exteriorCoords.isNotEmpty() && 
                    !isCoordinateEqual(exteriorCoords.first(), exteriorCoords.last())) {
                    exteriorCoords.add(exteriorCoords.first())
                }
                
                rings.add(exteriorCoords)
            }
            
            // 孔洞
            polygon.holes.forEach { hole ->
                if (hole.isNotEmpty()) {
                    val holeCoords = hole.map { 
                        listOf(it.longitude, it.latitude)
                    }.toMutableList()
                    
                    // 确保孔洞闭合
                    if (holeCoords.isNotEmpty() && 
                        !isCoordinateEqual(holeCoords.first(), holeCoords.last())) {
                        holeCoords.add(holeCoords.first())
                    }
                    
                    rings.add(holeCoords)
                }
            }
            
            rings
        }.filter { it.isNotEmpty() } // 过滤掉空的多边形
        
        return MultiPolygonGeometry(coordinates = coordinates)
    }
    
    /**
     * 转换为完整的GeoJSON地块特征
     */
    fun toLandPlotFeature(
        name: String,
        area: Double,
        perimeter: Double = 0.0,
        properties: Map<String, Any> = emptyMap()
    ): LandPlotFeature {
        val plotProperties = LandPlotProperties(
            name = name,
            area = area,
            perimeter = perimeter,
            cropType = properties["cropType"] as? String,
            description = properties["description"] as? String,
            tags = (properties["tags"] as? List<*>)?.filterIsInstance<String>() ?: emptyList(),
            atlasId = properties["atlasId"] as? String
        )
        
        return LandPlotFeature(
            geometry = toGeoJsonGeometry(),
            properties = plotProperties
        )
    }
    
    /**
     * 检查是否有效的地块（至少一个多边形有完整的外边界）
     */
    fun isValidLandPlot(): Boolean {
        return polygons.any { polygon ->
            polygon.exterior.size >= 3
        }
    }
    
    /**
     * 获取所有顶点的统计信息
     */
    fun getStatistics(): DrawingStatistics {
        var totalVertices = 0
        var polygonCount = 0
        var holeCount = 0
        
        polygons.forEach { polygon ->
            if (polygon.exterior.isNotEmpty()) {
                polygonCount++
                totalVertices += polygon.exterior.size
                
                polygon.holes.forEach { hole ->
                    if (hole.isNotEmpty()) {
                        holeCount++
                        totalVertices += hole.size
                    }
                }
            }
        }
        
        return DrawingStatistics(
            totalVertices = totalVertices,
            polygonCount = polygonCount,
            holeCount = holeCount
        )
    }
    
    /**
     * 清空所有绘制数据
     */
    fun clear(): LandPlotInProgress {
        return LandPlotInProgress()
    }

    // ========== MapViewModel需要的额外方法 ==========

    /**
     * 添加顶点到当前环
     */
    fun addVertex(point: LatLng): LandPlotInProgress {
        return addVertexToCurrentRing(point)
    }

    /**
     * 移除当前环的最后一个顶点
     */
    fun removeLastVertex(): LandPlotInProgress {
        val currentRing = getCurrentRing()
        if (currentRing.isEmpty()) return this
        
        return removeVertexFromCurrentRing(currentRing.size - 1)
    }

    /**
     * 清空当前环
     */
    fun clearCurrentRing(): LandPlotInProgress {
        val currentPolygon = getCurrentPolygon() ?: return this
        
        val updatedPolygon = if (activeRingIndex == 0) {
            // 清空外边界
            currentPolygon.copy(exterior = emptyList())
        } else {
            // 清空指定孔洞
            val holeIndex = activeRingIndex - 1
            if (holeIndex in currentPolygon.holes.indices) {
                val updatedHoles = currentPolygon.holes.toMutableList()
                updatedHoles[holeIndex] = emptyList()
                currentPolygon.copy(holes = updatedHoles)
            } else {
                currentPolygon
            }
        }
        
        val updatedPolygons = polygons.toMutableList()
        updatedPolygons[activePolygonIndex] = updatedPolygon
        
        return copy(polygons = updatedPolygons)
    }

    /**
     * 完成当前环的绘制
     */
    fun completeCurrentRing(): LandPlotInProgress {
        // 当前环如果有足够的点就标记完成，准备下一个操作
        return this
    }

    /**
     * 开始绘制新孔洞
     */
    fun startNewHole(): LandPlotInProgress {
        return startDrawingHole()
    }

    /**
     * 开始绘制新多边形
     */
    fun startNewPolygon(): LandPlotInProgress {
        return addNewPolygon()
    }

    /**
     * 更新指定索引的顶点
     */
    fun updateVertex(index: Int, newPoint: LatLng): LandPlotInProgress {
        val currentRing = getCurrentRing()
        if (index !in currentRing.indices) return this
        
        val updatedRing = currentRing.toMutableList()
        updatedRing[index] = newPoint
        
        return updateCurrentRing(updatedRing)
    }

    /**
     * 在指定位置插入顶点
     */
    fun insertVertex(index: Int, point: LatLng): LandPlotInProgress {
        val currentRing = getCurrentRing()
        if (index !in 0..currentRing.size) return this
        
        val updatedRing = currentRing.toMutableList()
        updatedRing.add(index, point)
        
        return updateCurrentRing(updatedRing)
    }

    /**
     * 更新指定多边形指定环的顶点
     */
    fun updateVertexInPolygon(
        polygonIndex: Int,
        ringIndex: Int,
        vertexIndex: Int,
        newVertex: LatLng
    ): LandPlotInProgress {
        if (polygonIndex !in polygons.indices) return this
        
        val polygon = polygons[polygonIndex]
        val updatedPolygon = when (ringIndex) {
            0 -> {
                // 更新外边界
                if (vertexIndex in polygon.exterior.indices) {
                    val updatedExterior = polygon.exterior.toMutableList()
                    updatedExterior[vertexIndex] = newVertex
                    polygon.copy(exterior = updatedExterior)
                } else polygon
            }
            else -> {
                // 更新孔洞
                val holeIndex = ringIndex - 1
                if (holeIndex in polygon.holes.indices && vertexIndex in polygon.holes[holeIndex].indices) {
                    val updatedHoles = polygon.holes.toMutableList()
                    val updatedHole = updatedHoles[holeIndex].toMutableList()
                    updatedHole[vertexIndex] = newVertex
                    updatedHoles[holeIndex] = updatedHole
                    polygon.copy(holes = updatedHoles)
                } else polygon
            }
        }
        
        val updatedPolygons = polygons.toMutableList()
        updatedPolygons[polygonIndex] = updatedPolygon
        
        return copy(polygons = updatedPolygons)
    }

    /**
     * 在指定多边形指定环插入顶点
     */
    fun insertVertexInPolygon(
        polygonIndex: Int,
        ringIndex: Int,
        insertIndex: Int,
        newVertex: LatLng
    ): LandPlotInProgress {
        if (polygonIndex !in polygons.indices) return this
        
        val polygon = polygons[polygonIndex]
        val updatedPolygon = when (ringIndex) {
            0 -> {
                // 插入到外边界
                if (insertIndex in 0..polygon.exterior.size) {
                    val updatedExterior = polygon.exterior.toMutableList()
                    updatedExterior.add(insertIndex, newVertex)
                    polygon.copy(exterior = updatedExterior)
                } else polygon
            }
            else -> {
                // 插入到孔洞
                val holeIndex = ringIndex - 1
                if (holeIndex in polygon.holes.indices && insertIndex in 0..polygon.holes[holeIndex].size) {
                    val updatedHoles = polygon.holes.toMutableList()
                    val updatedHole = updatedHoles[holeIndex].toMutableList()
                    updatedHole.add(insertIndex, newVertex)
                    updatedHoles[holeIndex] = updatedHole
                    polygon.copy(holes = updatedHoles)
                } else polygon
            }
        }
        
        val updatedPolygons = polygons.toMutableList()
        updatedPolygons[polygonIndex] = updatedPolygon
        
        return copy(polygons = updatedPolygons)
    }

    /**
     * 更新当前环的顶点列表
     */
    private fun updateCurrentRing(newRing: List<LatLng>): LandPlotInProgress {
        val currentPolygon = getCurrentPolygon() ?: return this
        
        val updatedPolygon = if (activeRingIndex == 0) {
            currentPolygon.copy(exterior = newRing)
        } else {
            val holeIndex = activeRingIndex - 1
            if (holeIndex in currentPolygon.holes.indices) {
                val updatedHoles = currentPolygon.holes.toMutableList()
                updatedHoles[holeIndex] = newRing
                currentPolygon.copy(holes = updatedHoles)
            } else {
                currentPolygon
            }
        }
        
        val updatedPolygons = polygons.toMutableList()
        updatedPolygons[activePolygonIndex] = updatedPolygon
        
        return copy(polygons = updatedPolygons)
    }

    /**
     * 转换为GeoJSON地块特征（MapViewModel需要的签名）
     */
    fun toGeoJsonFeature(id: String, properties: LandPlotProperties): LandPlotFeature {
        return LandPlotFeature(
            id = id,
            geometry = toGeoJsonGeometry(),
            properties = properties
        )
    }

    companion object {
        /**
         * 从GeoJSON地块特征创建绘制进度对象
         */
        fun fromGeoJsonFeature(feature: LandPlotFeature): LandPlotInProgress {
            val polygons = feature.geometry.coordinates.mapIndexed { _, polygonCoords ->
                val exterior = if (polygonCoords.isNotEmpty()) {
                    polygonCoords[0].map { coord ->
                        LatLng(coord[1], coord[0]) // 注意：GeoJSON是[经度, 纬度]
                    }
                } else {
                    emptyList()
                }
                
                val holes = if (polygonCoords.size > 1) {
                    polygonCoords.drop(1).map { holeCoords ->
                        holeCoords.map { coord ->
                            LatLng(coord[1], coord[0])
                        }
                    }
                } else {
                    emptyList()
                }
                
                PolygonInProgress(
                    exterior = exterior,
                    holes = holes,
                    isCompleted = exterior.size >= 3
                )
            }
            
            return LandPlotInProgress(
                polygons = polygons,
                activePolygonIndex = 0,
                activeRingIndex = 0
            )
        }
    }
    
    private fun isCoordinateEqual(coord1: List<Double>, coord2: List<Double>): Boolean {
        return coord1.size >= 2 && coord2.size >= 2 &&
               Math.abs(coord1[0] - coord2[0]) < 1e-10 &&
               Math.abs(coord1[1] - coord2[1]) < 1e-10
    }
}

/**
 * 多边形绘制进行中的数据结构
 */
data class PolygonInProgress(
    val exterior: List<LatLng> = emptyList(),
    val holes: List<List<LatLng>> = emptyList(),
    val isCompleted: Boolean = false
) {
    /**
     * 获取总的环数量（外边界 + 孔洞）
     */
    fun getTotalRingCount(): Int = 1 + holes.size
    
    /**
     * 检查多边形是否有效
     */
    fun isValid(): Boolean = exterior.size >= 3
    
    /**
     * 获取指定环的顶点列表
     */
    fun getRing(ringIndex: Int): List<LatLng> {
        return when (ringIndex) {
            0 -> exterior
            else -> holes.getOrNull(ringIndex - 1) ?: emptyList()
        }
    }
}

/**
 * 绘制统计信息
 */
data class DrawingStatistics(
    val totalVertices: Int,
    val polygonCount: Int,
    val holeCount: Int
) 