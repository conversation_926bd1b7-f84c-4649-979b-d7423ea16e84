package cn.agrolinking.wmst.domain.geojson

import com.google.gson.annotations.SerializedName

/**
 * GeoJSON几何类型基类
 * 为不同几何类型提供统一接口
 */
sealed class Geometry {
    abstract val type: String
    abstract fun isValid(): Boolean
    abstract fun getBoundingBox(): BoundingBox?
    abstract fun getCentroid(): Pair<Double, Double>?
}

/**
 * Point几何类型（地标用）
 */
data class PointGeometry(
    @SerializedName("type")
    override val type: String = "Point",
    
    @SerializedName("coordinates")
    val coordinates: List<Double> // [lng, lat]
) : Geometry() {
    
    override fun isValid(): Boolean {
        return type == "Point" && coordinates.size >= 2
    }
    
    override fun getBoundingBox(): BoundingBox? {
        return if (isValid()) {
            BoundingBox(coordinates[0], coordinates[1], coordinates[0], coordinates[1])
        } else null
    }
    
    override fun getCentroid(): Pair<Double, Double>? {
        return if (isValid()) {
            Pair(coordinates[0], coordinates[1])
        } else null
    }
    
    /**
     * 获取经度
     */
    fun getLongitude(): Double = coordinates.getOrElse(0) { 0.0 }
    
    /**
     * 获取纬度
     */
    fun getLatitude(): Double = coordinates.getOrElse(1) { 0.0 }
}

/**
 * LineString几何类型（路径用）
 */
data class LineStringGeometry(
    @SerializedName("type")
    override val type: String = "LineString",
    
    @SerializedName("coordinates")
    val coordinates: List<List<Double>> // [[lng, lat], [lng, lat], ...]
) : Geometry() {
    
    override fun isValid(): Boolean {
        return type == "LineString" && 
               coordinates.size >= 2 && 
               coordinates.all { it.size >= 2 }
    }
    
    override fun getBoundingBox(): BoundingBox? {
        if (!isValid()) return null
        
        var minLng = Double.MAX_VALUE
        var maxLng = Double.MIN_VALUE
        var minLat = Double.MAX_VALUE
        var maxLat = Double.MIN_VALUE
        
        coordinates.forEach { vertex ->
            val lng = vertex[0]
            val lat = vertex[1]
            minLng = minOf(minLng, lng)
            maxLng = maxOf(maxLng, lng)
            minLat = minOf(minLat, lat)
            maxLat = maxOf(maxLat, lat)
        }
        
        return BoundingBox(minLng, minLat, maxLng, maxLat)
    }
    
    override fun getCentroid(): Pair<Double, Double>? {
        if (!isValid()) return null
        
        var totalLng = 0.0
        var totalLat = 0.0
        
        coordinates.forEach { vertex ->
            totalLng += vertex[0]
            totalLat += vertex[1]
        }
        
        return Pair(totalLng / coordinates.size, totalLat / coordinates.size)
    }
    
    /**
     * 获取线段长度（米）
     */
    fun getLength(): Double {
        if (coordinates.size < 2) return 0.0
        
        var totalLength = 0.0
        for (i in 0 until coordinates.size - 1) {
            val point1 = coordinates[i]
            val point2 = coordinates[i + 1]
            totalLength += calculateDistance(
                point1[1], point1[0], // lat, lng
                point2[1], point2[0]
            )
        }
        
        return totalLength
    }
    
    /**
     * 获取顶点数量
     */
    fun getVertexCount(): Int = coordinates.size
    
    private fun calculateDistance(lat1: Double, lng1: Double, lat2: Double, lng2: Double): Double {
        val earthRadius = 6378137.0 // 地球半径(米)
        
        val lat1Rad = Math.toRadians(lat1)
        val lat2Rad = Math.toRadians(lat2)
        val deltaLatRad = Math.toRadians(lat2 - lat1)
        val deltaLngRad = Math.toRadians(lng2 - lng1)
        
        val a = kotlin.math.sin(deltaLatRad / 2) * kotlin.math.sin(deltaLatRad / 2) +
                kotlin.math.cos(lat1Rad) * kotlin.math.cos(lat2Rad) *
                kotlin.math.sin(deltaLngRad / 2) * kotlin.math.sin(deltaLngRad / 2)
        val c = 2 * kotlin.math.atan2(kotlin.math.sqrt(a), kotlin.math.sqrt(1 - a))
        
        return earthRadius * c
    }
}

/**
 * Polygon几何类型（单个多边形）
 */
data class PolygonGeometry(
    @SerializedName("type")
    override val type: String = "Polygon",
    
    @SerializedName("coordinates")
    val coordinates: List<List<List<Double>>>
    // coordinates[i][j] = [lng, lat]
    // i: 第几个环 (0=外边界, 1+=孔洞)
    // j: 第几个顶点
) : Geometry() {
    
    override fun isValid(): Boolean {
        if (type != "Polygon" || coordinates.isEmpty()) {
            return false
        }
        
        // 验证外边界
        val exterior = coordinates[0]
        if (exterior.size < 4 || !isRingClosed(exterior)) {
            return false
        }
        
        // 验证孔洞
        return coordinates.drop(1).all { hole ->
            hole.size >= 4 && isRingClosed(hole)
        }
    }
    
    override fun getBoundingBox(): BoundingBox? {
        if (!isValid()) return null
        
        var minLng = Double.MAX_VALUE
        var maxLng = Double.MIN_VALUE
        var minLat = Double.MAX_VALUE
        var maxLat = Double.MIN_VALUE
        
        coordinates.forEach { ring ->
            ring.forEach { vertex ->
                val lng = vertex[0]
                val lat = vertex[1]
                minLng = minOf(minLng, lng)
                maxLng = maxOf(maxLng, lng)
                minLat = minOf(minLat, lat)
                maxLat = maxOf(maxLat, lat)
            }
        }
        
        return BoundingBox(minLng, minLat, maxLng, maxLat)
    }
    
    override fun getCentroid(): Pair<Double, Double>? {
        if (!isValid()) return null
        
        // 只计算外边界的中心点
        val exterior = coordinates[0]
        var totalLng = 0.0
        var totalLat = 0.0
        
        exterior.forEach { vertex ->
            totalLng += vertex[0]
            totalLat += vertex[1]
        }
        
        return Pair(totalLng / exterior.size, totalLat / exterior.size)
    }
    
    /**
     * 获取孔洞数量
     */
    fun getHoleCount(): Int = maxOf(0, coordinates.size - 1)
    
    /**
     * 检查是否有孔洞
     */
    fun hasHoles(): Boolean = coordinates.size > 1
    
    /**
     * 获取外边界
     */
    fun getExteriorRing(): List<List<Double>> = coordinates.getOrElse(0) { emptyList() }
    
    /**
     * 获取所有孔洞
     */
    fun getHoles(): List<List<List<Double>>> = coordinates.drop(1)
    
    /**
     * 获取指定索引的孔洞
     */
    fun getHole(index: Int): List<List<Double>>? {
        return getHoles().getOrNull(index)
    }
    
    private fun isRingClosed(ring: List<List<Double>>): Boolean {
        if (ring.size < 2) return false
        val first = ring.first()
        val last = ring.last()
        return Math.abs(first[0] - last[0]) < 1e-10 && 
               Math.abs(first[1] - last[1]) < 1e-10
    }
}

/**
 * MultiPolygon几何类型（地块用）
 * 继承自之前定义的MultiPolygonGeometry，这里做类型扩展
 */
// 已在LandPlotFeature.kt中定义，这里不重复

/**
 * 通用的GeoJSON Feature基类
 */
abstract class Feature {
    abstract val type: String
    abstract val geometry: Geometry
    abstract val properties: Map<String, Any?>
    
    /**
     * 验证Feature的有效性
     */
    open fun isValid(): Boolean {
        return type == "Feature" && geometry.isValid()
    }
}

/**
 * 地标Feature（Point）
 */
data class LandmarkFeature(
    @SerializedName("type")
    override val type: String = "Feature",
    
    @SerializedName("geometry")
    override val geometry: PointGeometry,
    
    @SerializedName("properties")
    val landmarkProperties: LandmarkProperties,
    
    @SerializedName("id")
    val id: String = landmarkProperties.id
) : Feature() {
    override val properties: Map<String, Any?> get() = mapOf(
        "id" to landmarkProperties.id,
        "name" to landmarkProperties.name,
        "type" to landmarkProperties.landmarkType,
        "description" to landmarkProperties.description,
        "iconUrl" to landmarkProperties.iconUrl,
        "tags" to landmarkProperties.tags,
        "createdAt" to landmarkProperties.createdAt,
        "updatedAt" to landmarkProperties.updatedAt,
        "atlasId" to landmarkProperties.atlasId
    )
}

/**
 * 路径Feature（LineString）
 */
data class PathFeature(
    @SerializedName("type")
    override val type: String = "Feature",
    
    @SerializedName("geometry")
    override val geometry: LineStringGeometry,
    
    @SerializedName("properties")
    val pathProperties: PathProperties,
    
    @SerializedName("id")
    val id: String = pathProperties.id
) : Feature() {
    override val properties: Map<String, Any?> get() = mapOf(
        "id" to pathProperties.id,
        "name" to pathProperties.name,
        "type" to pathProperties.pathType,
        "length" to pathProperties.length,
        "width" to pathProperties.width,
        "material" to pathProperties.material,
        "description" to pathProperties.description,
        "tags" to pathProperties.tags,
        "createdAt" to pathProperties.createdAt,
        "updatedAt" to pathProperties.updatedAt,
        "atlasId" to pathProperties.atlasId
    )
}

/**
 * 地标属性
 */
data class LandmarkProperties(
    @SerializedName("id")
    val id: String,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("type")
    val landmarkType: String, // 地标类型：井、建筑、路口等
    
    @SerializedName("description")
    val description: String? = null,
    
    @SerializedName("iconUrl")
    val iconUrl: String? = null,
    
    @SerializedName("tags")
    val tags: List<String> = emptyList(),
    
    @SerializedName("createdAt")
    val createdAt: Long = System.currentTimeMillis(),
    
    @SerializedName("updatedAt")  
    val updatedAt: Long = System.currentTimeMillis(),
    
    @SerializedName("atlasId")
    val atlasId: String? = null
)

/**
 * 路径属性
 */
data class PathProperties(
    @SerializedName("id")
    val id: String,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("type")
    val pathType: String, // 路径类型：道路、水渠、田埂等
    
    @SerializedName("length")
    val length: Double, // 长度(米)
    
    @SerializedName("width")
    val width: Double? = null, // 宽度(米)
    
    @SerializedName("material")
    val material: String? = null, // 材质：土路、水泥路等
    
    @SerializedName("description")
    val description: String? = null,
    
    @SerializedName("tags")
    val tags: List<String> = emptyList(),
    
    @SerializedName("createdAt")
    val createdAt: Long = System.currentTimeMillis(),
    
    @SerializedName("updatedAt")
    val updatedAt: Long = System.currentTimeMillis(),
    
    @SerializedName("atlasId")
    val atlasId: String? = null
) {
    /**
     * 格式化长度显示
     */
    fun getFormattedLength(): String {
        return when {
            length < 1000 -> String.format("%.1f 米", length)
            else -> String.format("%.2f 公里", length / 1000)
        }
    }
} 