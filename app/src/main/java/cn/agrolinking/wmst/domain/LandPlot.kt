package cn.agrolinking.wmst.domain

import org.maplibre.android.geometry.LatLng
import java.util.UUID

data class LandPlot(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val description: String = "",
    
    // 地理信息
    val vertices: List<LatLng>,
    val area: Double, // 面积(平方米)
    val perimeter: Double, // 周长(米)
    val centroid: LatLng, // 中心点坐标
    
    // 农业信息
    val cropType: String = "",
    val plantingDate: Long = 0,
    val harvestDate: Long = 0,
    
    // 元数据
    val atlasId: String,
    val createdBy: String,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    
    // 状态
    val isActive: Boolean = true
) 