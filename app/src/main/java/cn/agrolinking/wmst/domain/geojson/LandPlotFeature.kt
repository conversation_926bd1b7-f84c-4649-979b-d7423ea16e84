package cn.agrolinking.wmst.domain.geojson

import com.google.gson.annotations.SerializedName
import java.util.UUID

/**
 * GeoJSON 地块特征
 * 符合GeoJSON标准的地块数据结构
 */
data class LandPlotFeature(
    @SerializedName("type")
    val type: String = "Feature",
    
    @SerializedName("geometry")
    val geometry: MultiPolygonGeometry,
    
    @SerializedName("properties")
    val properties: LandPlotProperties,
    
    @SerializedName("id")
    val id: String = properties.id
) {
    /**
     * 验证GeoJSON特征的有效性
     */
    fun isValid(): Boolean {
        return type == "Feature" && 
               geometry.isValid() && 
               properties.id.isNotBlank()
    }
    
    /**
     * 获取地块中心点坐标
     */
    fun getCentroid(): Pair<Double, Double>? {
        return geometry.getCentroid()
    }
    
    /**
     * 获取地块边界框
     */
    fun getBoundingBox(): BoundingBox? {
        return geometry.getBoundingBox()
    }
}

/**
 * MultiPolygon 几何体
 * 支持多个多边形，每个多边形可以包含孔洞
 */
data class MultiPolygonGeometry(
    @SerializedName("type")
    val type: String = "MultiPolygon",
    
    @SerializedName("coordinates")
    val coordinates: List<List<List<List<Double>>>>
    // coordinates[i][j][k] = [lng, lat]
    // i: 第几个多边形 (Polygon Index)
    // j: 第几个环 (Ring Index) - 0=外边界(Exterior), 1+=孔洞(Holes)  
    // k: 第几个顶点 (Vertex Index)
) {
    /**
     * 验证MultiPolygon几何体的有效性
     */
    fun isValid(): Boolean {
        if (type != "MultiPolygon" || coordinates.isEmpty()) {
            return false
        }
        
        return coordinates.all { polygon ->
            polygon.isNotEmpty() && // 至少有外边界
            polygon[0].size >= 4 && // 外边界至少4个点(闭合)
            isRingClosed(polygon[0]) && // 外边界必须闭合
            polygon.drop(1).all { hole -> // 验证所有孔洞
                hole.size >= 4 && isRingClosed(hole)
            }
        }
    }
    
    /**
     * 检查环是否闭合
     */
    private fun isRingClosed(ring: List<List<Double>>): Boolean {
        if (ring.size < 2) return false
        val first = ring.first()
        val last = ring.last()
        return Math.abs(first[0] - last[0]) < 1e-10 && 
               Math.abs(first[1] - last[1]) < 1e-10
    }
    
    /**
     * 获取所有多边形的数量
     */
    fun getPolygonCount(): Int = coordinates.size
    
    /**
     * 获取指定多边形的孔洞数量
     */
    fun getHoleCount(polygonIndex: Int): Int {
        return if (polygonIndex in coordinates.indices) {
            maxOf(0, coordinates[polygonIndex].size - 1)
        } else 0
    }
    
    /**
     * 获取几何体中心点
     */
    fun getCentroid(): Pair<Double, Double>? {
        if (coordinates.isEmpty()) return null
        
        var totalLng = 0.0
        var totalLat = 0.0
        var pointCount = 0
        
        coordinates.forEach { polygon ->
            if (polygon.isNotEmpty()) {
                // 只计算外边界的中心点
                val exterior = polygon[0]
                exterior.forEach { vertex ->
                    if (vertex.size >= 2) {
                        totalLng += vertex[0]
                        totalLat += vertex[1]
                        pointCount++
                    }
                }
            }
        }
        
        return if (pointCount > 0) {
            Pair(totalLng / pointCount, totalLat / pointCount)
        } else null
    }
    
    /**
     * 获取边界框
     */
    fun getBoundingBox(): BoundingBox? {
        if (coordinates.isEmpty()) return null
        
        var minLng = Double.MAX_VALUE
        var maxLng = Double.MIN_VALUE
        var minLat = Double.MAX_VALUE
        var maxLat = Double.MIN_VALUE
        
        coordinates.forEach { polygon ->
            polygon.forEach { ring ->
                ring.forEach { vertex ->
                    if (vertex.size >= 2) {
                        val lng = vertex[0]
                        val lat = vertex[1]
                        minLng = minOf(minLng, lng)
                        maxLng = maxOf(maxLng, lng)
                        minLat = minOf(minLat, lat)
                        maxLat = maxOf(maxLat, lat)
                    }
                }
            }
        }
        
        return if (minLng != Double.MAX_VALUE) {
            BoundingBox(minLng, minLat, maxLng, maxLat)
        } else null
    }
}

/**
 * 地块属性
 */
data class LandPlotProperties(
    @SerializedName("id")
    val id: String = UUID.randomUUID().toString(),
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("area")
    val area: Double, // 面积(平方米)
    
    @SerializedName("perimeter")
    val perimeter: Double = 0.0, // 周长(米)
    
    @SerializedName("cropType")
    val cropType: String? = null, // 作物类型
    
    @SerializedName("plantingDate")
    val plantingDate: Long? = null, // 种植日期(时间戳)
    
    @SerializedName("harvestDate")
    val harvestDate: Long? = null, // 收获日期(时间戳)
    
    @SerializedName("description")
    val description: String? = null, // 描述信息
    
    @SerializedName("tags")
    val tags: List<String> = emptyList(), // 标签
    
    @SerializedName("createdAt")
    val createdAt: Long = System.currentTimeMillis(),
    
    @SerializedName("updatedAt")
    val updatedAt: Long = System.currentTimeMillis(),
    
    @SerializedName("createdBy")
    val createdBy: String? = null, // 创建者
    
    @SerializedName("atlasId")
    val atlasId: String? = null // 所属图册ID
) {
    /**
     * 格式化面积显示
     */
    fun getFormattedArea(): String {
        return when {
            area < 10000 -> String.format("%.1f 平方米", area)
            area < 1000000 -> String.format("%.2f 亩", area / 666.67)
            else -> String.format("%.2f 平方公里", area / 1000000)
        }
    }
    
    /**
     * 格式化周长显示
     */
    fun getFormattedPerimeter(): String {
        return when {
            perimeter < 1000 -> String.format("%.1f 米", perimeter)
            else -> String.format("%.2f 公里", perimeter / 1000)
        }
    }
    
    /**
     * 更新时间戳
     */
    fun withUpdatedTimestamp(): LandPlotProperties {
        return copy(updatedAt = System.currentTimeMillis())
    }
}

/**
 * 边界框
 */
data class BoundingBox(
    val minLng: Double,
    val minLat: Double,
    val maxLng: Double,
    val maxLat: Double
) {
    /**
     * 获取中心点
     */
    fun getCenter(): Pair<Double, Double> {
        return Pair(
            (minLng + maxLng) / 2,
            (minLat + maxLat) / 2
        )
    }
    
    /**
     * 检查点是否在边界框内
     */
    fun contains(lng: Double, lat: Double): Boolean {
        return lng >= minLng && lng <= maxLng && lat >= minLat && lat <= maxLat
    }
    
    /**
     * 扩展边界框
     */
    fun expand(ratio: Double): BoundingBox {
        val lngExpansion = (maxLng - minLng) * ratio / 2
        val latExpansion = (maxLat - minLat) * ratio / 2
        
        return BoundingBox(
            minLng - lngExpansion,
            minLat - latExpansion,
            maxLng + lngExpansion,
            maxLat + latExpansion
        )
    }
} 