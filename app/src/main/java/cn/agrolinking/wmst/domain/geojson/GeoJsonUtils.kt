package cn.agrolinking.wmst.domain.geojson

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonParseException
import org.maplibre.android.geometry.LatLng
import org.maplibre.geojson.FeatureCollection
import org.maplibre.geojson.Feature as MapLibreFeature
import org.maplibre.geojson.Point as MapLibrePoint
import org.maplibre.geojson.LineString as MapLibreLineString
import org.maplibre.geojson.Polygon as MapLibrePolygon
import org.maplibre.geojson.MultiPolygon as MapLibreMultiPolygon
import android.util.Log

/**
 * GeoJSON工具类
 * 提供序列化、反序列化和MapLibre集成功能
 */
object GeoJsonUtils {
    private const val TAG = "GeoJsonUtils"
    
    private val gson: Gson = GsonBuilder()
        .setPrettyPrinting()
        .create()
    
    /**
     * 将LandPlotFeature序列化为GeoJSON字符串
     */
    fun serializeLandPlot(landPlot: LandPlotFeature): String {
        return try {
            gson.toJson(landPlot)
        } catch (e: Exception) {
            Log.e(TAG, "序列化地块失败", e)
            ""
        }
    }
    
    /**
     * 将GeoJSON字符串反序列化为LandPlotFeature
     */
    fun deserializeLandPlot(geoJson: String): LandPlotFeature? {
        return try {
            gson.fromJson(geoJson, LandPlotFeature::class.java)
        } catch (e: JsonParseException) {
            Log.e(TAG, "反序列化地块失败", e)
            null
        }
    }
    
    /**
     * 将地块列表序列化为FeatureCollection
     */
    fun serializeLandPlots(landPlots: List<LandPlotFeature>): String {
        return try {
            val featureCollection = mapOf(
                "type" to "FeatureCollection",
                "features" to landPlots
            )
            gson.toJson(featureCollection)
        } catch (e: Exception) {
            Log.e(TAG, "序列化地块集合失败", e)
            ""
        }
    }
    
    /**
     * 将FeatureCollection字符串反序列化为地块列表
     */
    fun deserializeLandPlots(geoJson: String): List<LandPlotFeature> {
        return try {
            // 首先解析为通用结构
            val jsonObject = gson.fromJson(geoJson, Map::class.java)
            
            if (jsonObject["type"] != "FeatureCollection") {
                Log.w(TAG, "不是有效的FeatureCollection")
                return emptyList()
            }
            
            @Suppress("UNCHECKED_CAST")
            val features = jsonObject["features"] as? List<Map<String, Any>> ?: return emptyList()
            
            features.mapNotNull { featureMap ->
                try {
                    val featureJson = gson.toJson(featureMap)
                    gson.fromJson(featureJson, LandPlotFeature::class.java)
                } catch (e: Exception) {
                    Log.w(TAG, "跳过无效的地块特征", e)
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "反序列化地块集合失败", e)
            emptyList()
        }
    }
    
    /**
     * 将LandPlotFeature转换为MapLibre FeatureCollection
     * 用于直接与MapLibre GeoJsonSource集成
     */
    fun landPlotToMapLibreFeatureCollection(landPlots: List<LandPlotFeature>): FeatureCollection {
        val mapLibreFeatures = landPlots.mapNotNull { landPlot ->
            landPlotToMapLibreFeature(landPlot)
        }
        
        return FeatureCollection.fromFeatures(mapLibreFeatures)
    }
    
    /**
     * 将单个LandPlotFeature转换为MapLibre Feature
     */
    fun landPlotToMapLibreFeature(landPlot: LandPlotFeature): MapLibreFeature? {
        return try {
            val geometry = landPlotToMapLibreMultiPolygon(landPlot.geometry)
            
            // 将属性转换为JsonObject
            val propertiesJson = gson.toJsonTree(mapOf(
                "id" to landPlot.id,
                "name" to landPlot.properties.name,
                "area" to landPlot.properties.area,
                "perimeter" to landPlot.properties.perimeter,
                "cropType" to landPlot.properties.cropType,
                "description" to landPlot.properties.description,
                "tags" to landPlot.properties.tags,
                "formattedArea" to landPlot.properties.getFormattedArea(),
                "formattedPerimeter" to landPlot.properties.getFormattedPerimeter(),
                "createdAt" to landPlot.properties.createdAt,
                "updatedAt" to landPlot.properties.updatedAt
            )).asJsonObject
            
            MapLibreFeature.fromGeometry(geometry, propertiesJson, landPlot.id)
        } catch (e: Exception) {
            Log.e(TAG, "转换为MapLibre Feature失败", e)
            null
        }
    }
    
    /**
     * 将MultiPolygonGeometry转换为MapLibre MultiPolygon
     */
    private fun landPlotToMapLibreMultiPolygon(geometry: MultiPolygonGeometry): MapLibreMultiPolygon {
        val polygons = geometry.coordinates.map { polygonCoords ->
            val rings = polygonCoords.map { ringCoords ->
                val points = ringCoords.map { coordPair ->
                    org.maplibre.geojson.Point.fromLngLat(coordPair[0], coordPair[1])
                }
                points
            }
            MapLibrePolygon.fromLngLats(rings)
        }
        
        return MapLibreMultiPolygon.fromPolygons(polygons)
    }
    
    /**
     * 将LatLng列表转换为GeoJSON坐标格式
     */
    fun latLngListToGeoJsonCoordinates(points: List<LatLng>): List<List<Double>> {
        return points.map { latLng ->
            listOf(latLng.longitude, latLng.latitude)
        }
    }
    
    /**
     * 将GeoJSON坐标格式转换为LatLng列表
     */
    fun geoJsonCoordinatesToLatLngList(coordinates: List<List<Double>>): List<LatLng> {
        return coordinates.mapNotNull { coord ->
            if (coord.size >= 2) {
                LatLng(coord[1], coord[0]) // lat, lng
            } else null
        }
    }
    
    /**
     * 将MapLibre FeatureCollection转换回LandPlotFeature列表
     */
    fun mapLibreFeatureCollectionToLandPlots(featureCollection: FeatureCollection): List<LandPlotFeature> {
        val features = featureCollection.features() ?: return emptyList()
        
        return features.mapNotNull { feature ->
            mapLibreFeatureToLandPlot(feature)
        }
    }
    
    /**
     * 将MapLibre Feature转换回LandPlotFeature
     */
    private fun mapLibreFeatureToLandPlot(feature: MapLibreFeature): LandPlotFeature? {
        return try {
            val geometry = feature.geometry()
            
            if (geometry !is MapLibreMultiPolygon) {
                Log.w(TAG, "Feature几何体不是MultiPolygon类型")
                return null
            }
            
            val properties = feature.properties()
            
            val landPlotGeometry = mapLibreMultiPolygonToLandPlot(geometry)
            val landPlotProperties = LandPlotProperties(
                id = feature.id() ?: "",
                name = properties?.get("name")?.asString ?: "",
                area = properties?.get("area")?.asDouble ?: 0.0,
                perimeter = properties?.get("perimeter")?.asDouble ?: 0.0,
                cropType = properties?.get("cropType")?.asString,
                description = properties?.get("description")?.asString,
                tags = properties?.get("tags")?.asJsonArray?.map { it.asString } ?: emptyList(),
                createdAt = properties?.get("createdAt")?.asLong ?: System.currentTimeMillis(),
                updatedAt = properties?.get("updatedAt")?.asLong ?: System.currentTimeMillis()
            )
            
            LandPlotFeature(
                geometry = landPlotGeometry,
                properties = landPlotProperties
            )
        } catch (e: Exception) {
            Log.e(TAG, "转换MapLibre Feature失败", e)
            null
        }
    }
    
    /**
     * 将MapLibre MultiPolygon转换为MultiPolygonGeometry
     */
    private fun mapLibreMultiPolygonToLandPlot(multiPolygon: MapLibreMultiPolygon): MultiPolygonGeometry {
        val coordinates = multiPolygon.coordinates().map { polygon ->
            polygon.map { ring ->
                ring.map { point ->
                    listOf(point.longitude(), point.latitude())
                }
            }
        }
        
        return MultiPolygonGeometry(coordinates = coordinates)
    }
    
    /**
     * 验证GeoJSON字符串的有效性
     */
    fun validateGeoJson(geoJson: String): ValidationResult {
        return try {
            val jsonObject = gson.fromJson(geoJson, Map::class.java)
            
            when (jsonObject["type"]) {
                "Feature" -> {
                    val geometry = jsonObject["geometry"] as? Map<*, *>
                    val properties = jsonObject["properties"] as? Map<*, *>
                    
                    when {
                        geometry == null -> ValidationResult.Error("缺少geometry字段")
                        properties == null -> ValidationResult.Error("缺少properties字段")
                        geometry["type"] != "MultiPolygon" -> ValidationResult.Error("仅支持MultiPolygon几何体")
                        else -> ValidationResult.Valid
                    }
                }
                "FeatureCollection" -> {
                    val features = jsonObject["features"] as? List<*>
                    when {
                        features == null -> ValidationResult.Error("缺少features字段")
                        features.isEmpty() -> ValidationResult.Warning("features数组为空")
                        else -> ValidationResult.Valid
                    }
                }
                else -> ValidationResult.Error("不支持的GeoJSON类型: ${jsonObject["type"]}")
            }
        } catch (e: JsonParseException) {
            ValidationResult.Error("无效的JSON格式: ${e.message}")
        } catch (e: Exception) {
            ValidationResult.Error("验证失败: ${e.message}")
        }
    }
    
    /**
     * 计算地块面积（平方米）
     * 使用Shoelace公式计算多边形面积
     */
    fun calculateLandPlotArea(landPlot: LandPlotFeature): Double {
        var totalArea = 0.0
        
        landPlot.geometry.coordinates.forEach { polygon ->
            if (polygon.isNotEmpty()) {
                // 外边界面积（正值）
                val exteriorArea = calculatePolygonArea(polygon[0])
                totalArea += Math.abs(exteriorArea)
                
                // 减去孔洞面积
                polygon.drop(1).forEach { hole ->
                    val holeArea = calculatePolygonArea(hole)
                    totalArea -= Math.abs(holeArea)
                }
            }
        }
        
        return totalArea
    }
    
    /**
     * 使用Shoelace公式计算单个多边形面积
     */
    private fun calculatePolygonArea(ring: List<List<Double>>): Double {
        if (ring.size < 3) return 0.0
        
        var area = 0.0
        val earthRadius = 6378137.0 // 地球半径(米)
        
        for (i in ring.indices) {
            val j = (i + 1) % ring.size
            val lat1 = Math.toRadians(ring[i][1])
            val lng1 = Math.toRadians(ring[i][0])
            val lat2 = Math.toRadians(ring[j][1])
            val lng2 = Math.toRadians(ring[j][0])
            
            area += (lng2 - lng1) * (2 + kotlin.math.sin(lat1) + kotlin.math.sin(lat2))
        }
        
        area = Math.abs(area) * earthRadius * earthRadius / 2.0
        return area
    }
    
    /**
     * 计算地块周长（米）
     */
    fun calculateLandPlotPerimeter(landPlot: LandPlotFeature): Double {
        var totalPerimeter = 0.0
        
        landPlot.geometry.coordinates.forEach { polygon ->
            if (polygon.isNotEmpty()) {
                // 外边界周长
                totalPerimeter += calculateRingPerimeter(polygon[0])
                
                // 孔洞周长
                polygon.drop(1).forEach { hole ->
                    totalPerimeter += calculateRingPerimeter(hole)
                }
            }
        }
        
        return totalPerimeter
    }
    
    /**
     * 计算环的周长
     */
    private fun calculateRingPerimeter(ring: List<List<Double>>): Double {
        if (ring.size < 2) return 0.0
        
        var perimeter = 0.0
        
        for (i in 0 until ring.size - 1) {
            val point1 = ring[i]
            val point2 = ring[i + 1]
            
            perimeter += calculateDistance(
                point1[1], point1[0], // lat, lng
                point2[1], point2[0]
            )
        }
        
        return perimeter
    }
    
    /**
     * 计算两点间距离（米）
     */
    private fun calculateDistance(lat1: Double, lng1: Double, lat2: Double, lng2: Double): Double {
        val earthRadius = 6378137.0 // 地球半径(米)
        
        val lat1Rad = Math.toRadians(lat1)
        val lat2Rad = Math.toRadians(lat2)
        val deltaLatRad = Math.toRadians(lat2 - lat1)
        val deltaLngRad = Math.toRadians(lng2 - lng1)
        
        val a = kotlin.math.sin(deltaLatRad / 2) * kotlin.math.sin(deltaLatRad / 2) +
                kotlin.math.cos(lat1Rad) * kotlin.math.cos(lat2Rad) *
                kotlin.math.sin(deltaLngRad / 2) * kotlin.math.sin(deltaLngRad / 2)
        val c = 2 * kotlin.math.atan2(kotlin.math.sqrt(a), kotlin.math.sqrt(1 - a))
        
        return earthRadius * c
    }
}

/**
 * GeoJSON验证结果
 */
sealed class ValidationResult {
    object Valid : ValidationResult()
    data class Warning(val message: String) : ValidationResult()
    data class Error(val message: String) : ValidationResult()
} 