package cn.agrolinking.wmst.network.model

import cn.agrolinking.wmst.database.UserEntity
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class UserApiModel(
    @Json(name = "avatar_url")
    val avatarUrl: String,
    @J<PERSON>(name = "events_url")
    val eventsUrl: String,
    @Json(name = "followers_url")
    val followersUrl: String,
    @<PERSON><PERSON>(name = "following_url")
    val followingUrl: String,
    @Json(name = "gists_url")
    val gistsUrl: String,
    @<PERSON><PERSON>(name = "gravatar_id")
    val gravatarId: String,
    @Json(name = "html_url")
    val htmlUrl: String,
    @J<PERSON>(name = "id")
    val id: Int,
    @<PERSON><PERSON>(name = "login")
    val login: String,
    @<PERSON><PERSON>(name = "node_id")
    val nodeId: String,
    @<PERSON><PERSON>(name = "organizations_url")
    val organizationsUrl: String,
    @<PERSON><PERSON>(name = "received_events_url")
    val receivedEventsUrl: String,
    @<PERSON><PERSON>(name = "repos_url")
    val reposUrl: String,
    @<PERSON><PERSON>(name = "site_admin")
    val siteAdmin: Boolean,
    @Json(name = "starred_url")
    val starredUrl: String,
    @Json(name = "subscriptions_url")
    val subscriptionsUrl: String,
    @Json(name = "type")
    val type: String,
    @Json(name = "url")
    val url: String
)

fun List<UserApiModel>.asDatabaseModel(): List<UserEntity> {
    return map {
        UserEntity(
            id = it.id,
            avatar = it.avatarUrl,
            username = it.login
        )
    }
}