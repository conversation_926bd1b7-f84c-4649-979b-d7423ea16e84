package cn.agrolinking.wmst.network.model

import cn.agrolinking.wmst.database.DetailsEntity
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class DetailsApiModel(
    @<PERSON><PERSON>(name = "avatar_url")
    val avatarUrl: String,
    @<PERSON><PERSON>(name = "bio")
    val bio: String? = null,
    @<PERSON><PERSON>(name = "blog")
    val blog: String? = null,
    @<PERSON><PERSON>(name = "company")
    val company: String? = null,
    @<PERSON><PERSON>(name = "created_at")
    val createdAt: String? = null,
    @<PERSON><PERSON>(name = "email")
    val email: String? = null,
    @<PERSON><PERSON>(name = "events_url")
    val eventsUrl: String? = null,
    @<PERSON><PERSON>(name = "followers")
    val followers: Int? = null,
    @<PERSON><PERSON>(name = "followers_url")
    val followersUrl: String? = null,
    @<PERSON><PERSON>(name = "following")
    val following: Int? = null,
    @<PERSON><PERSON>(name = "following_url")
    val followingUrl: String? = null,
    @<PERSON><PERSON>(name = "gists_url")
    val gistsUrl: String? = null,
    @<PERSON><PERSON>(name = "gravatar_id")
    val gravatarId: String? = null,
    @<PERSON><PERSON>(name = "hireable")
    val hireable: <PERSON>olean? = null,
    @Json(name = "html_url")
    val htmlUrl: String? = null,
    @Json(name = "id")
    val id: Int,
    @Json(name = "location")
    val location: String? = null,
    @Json(name = "login")
    val login: String,
    @Json(name = "name")
    val name: String? = null,
    @Json(name = "node_id")
    val nodeId: String? = null,
    @Json(name = "organizations_url")
    val organizationsUrl: String? = null,
    @Json(name = "public_gists")
    val publicGists: Int? = null,
    @Json(name = "public_repos")
    val publicRepos: Int? = null,
    @Json(name = "received_events_url")
    val receivedEventsUrl: String? = null,
    @Json(name = "repos_url")
    val reposUrl: String? = null,
    @Json(name = "site_admin")
    val siteAdmin: Boolean? = null,
    @Json(name = "starred_url")
    val starredUrl: String? = null,
    @Json(name = "subscriptions_url")
    val subscriptionsUrl: String? = null,
    @Json(name = "twitter_username")
    val twitterUsername: String? = null,
    @Json(name = "type")
    val type: String? = null,
    @Json(name = "updated_at")
    val updatedAt: String? = null,
    @Json(name = "url")
    val url: String? = null
)

fun DetailsApiModel.asDatabaseModel(): DetailsEntity {
    return DetailsEntity(
        user = login,
        avatar = avatarUrl,
        name = name ?: "",
        userSince = createdAt ?: "",
        location = location ?: ""
    )

}