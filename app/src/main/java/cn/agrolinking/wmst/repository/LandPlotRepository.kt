package cn.agrolinking.wmst.repository

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton
import cn.agrolinking.wmst.database.dao.LandPlotDao
import cn.agrolinking.wmst.database.dao.CropTypeStatistic
import cn.agrolinking.wmst.database.dao.MonthlyStatistic
import cn.agrolinking.wmst.database.entity.LandPlotEntity
import cn.agrolinking.wmst.domain.geojson.LandPlotFeature
import timber.log.Timber

/**
 * 地块数据仓库
 * 封装地块相关的数据操作，提供给ViewModel使用
 */
@Singleton
class LandPlotRepository @Inject constructor(
    private val landPlotDao: LandPlotDao
) {

    // ========== 查询操作 ==========

    /**
     * 获取所有活跃地块
     */
    fun getAllPlots(): Flow<List<LandPlotFeature>> {
        return landPlotDao.getAllActivePlots().map { entities ->
            entities.mapNotNull { entity ->
                entity.toDomainModel()?.also { landPlot ->
                    Timber.d("加载地块: ${landPlot.properties.name}, 面积: ${entity.getFormattedArea()}")
                }
            }
        }
    }

    /**
     * 根据图册ID获取地块
     */
    fun getPlotsByAtlas(atlasId: String): Flow<List<LandPlotFeature>> {
        return landPlotDao.getPlotsByAtlas(atlasId).map { entities ->
            entities.mapNotNull { it.toDomainModel() }
        }
    }

    /**
     * 根据ID获取单个地块
     */
    fun getPlotById(plotId: String): Flow<LandPlotFeature?> {
        return landPlotDao.getPlotById(plotId).map { entity ->
            entity?.toDomainModel()
        }
    }

    /**
     * 根据作物类型查询地块
     */
    fun getPlotsByCropType(cropType: String): Flow<List<LandPlotFeature>> {
        return landPlotDao.getPlotsByCropType(cropType).map { entities ->
            entities.mapNotNull { it.toDomainModel() }
        }
    }

    /**
     * 根据名称模糊搜索地块
     */
    fun searchPlotsByName(query: String): Flow<List<LandPlotFeature>> {
        return landPlotDao.searchPlotsByName(query).map { entities ->
            entities.mapNotNull { it.toDomainModel() }
        }
    }

    /**
     * 获取指定面积范围内的地块
     */
    fun getPlotsByAreaRange(minArea: Double, maxArea: Double): Flow<List<LandPlotFeature>> {
        return landPlotDao.getPlotsByAreaRange(minArea, maxArea).map { entities ->
            entities.mapNotNull { it.toDomainModel() }
        }
    }

    /**
     * 获取指定日期范围内创建的地块
     */
    fun getPlotsByDateRange(startTime: Long, endTime: Long): Flow<List<LandPlotFeature>> {
        return landPlotDao.getPlotsByDateRange(startTime, endTime).map { entities ->
            entities.mapNotNull { it.toDomainModel() }
        }
    }

    // ========== 保存和更新操作 ==========

    /**
     * 保存地块
     * @param landPlot 地块领域模型
     * @param atlasId 图册ID
     * @param createdBy 创建者
     * @return 保存是否成功
     */
    suspend fun savePlot(
        landPlot: LandPlotFeature, 
        atlasId: String? = null, 
        createdBy: String? = null
    ): Result<String> {
        return try {
            val entity = LandPlotEntity.fromDomainModel(landPlot, atlasId, createdBy)
            val success = landPlotDao.saveOrUpdatePlot(entity)
            
            if (success) {
                Timber.i("地块保存成功: ${landPlot.properties.name} (ID: ${landPlot.id})")
                Result.success(landPlot.id)
            } else {
                Timber.w("地块保存失败: ${landPlot.properties.name}")
                Result.failure(Exception("保存地块失败"))
            }
        } catch (e: Exception) {
            Timber.e(e, "保存地块时发生错误: ${landPlot.properties.name}")
            Result.failure(e)
        }
    }

    /**
     * 批量保存地块
     */
    suspend fun savePlots(
        landPlots: List<LandPlotFeature>, 
        atlasId: String? = null, 
        createdBy: String? = null
    ): Result<Int> {
        return try {
            val entities = landPlots.map { 
                LandPlotEntity.fromDomainModel(it, atlasId, createdBy) 
            }
            val savedCount = landPlotDao.saveOrUpdatePlots(entities)
            
            Timber.i("批量保存地块完成: $savedCount/${landPlots.size}")
            Result.success(savedCount)
        } catch (e: Exception) {
            Timber.e(e, "批量保存地块时发生错误")
            Result.failure(e)
        }
    }

    /**
     * 更新地块基本信息
     */
    suspend fun updatePlotBasicInfo(
        plotId: String,
        name: String,
        description: String?,
        cropType: String?
    ): Result<Boolean> {
        return try {
            val updatedRows = landPlotDao.updatePlotBasicInfo(plotId, name, description, cropType)
            val success = updatedRows > 0
            
            if (success) {
                Timber.i("地块基本信息更新成功: $plotId")
            } else {
                Timber.w("地块基本信息更新失败: $plotId")
            }
            
            Result.success(success)
        } catch (e: Exception) {
            Timber.e(e, "更新地块基本信息时发生错误: $plotId")
            Result.failure(e)
        }
    }

    /**
     * 更新种植收获日期
     */
    suspend fun updatePlantingHarvestDates(
        plotId: String,
        plantingDate: Long?,
        harvestDate: Long?
    ): Result<Boolean> {
        return try {
            val updatedRows = landPlotDao.updatePlantingHarvestDates(plotId, plantingDate, harvestDate)
            val success = updatedRows > 0
            
            if (success) {
                Timber.i("地块种植收获日期更新成功: $plotId")
            }
            
            Result.success(success)
        } catch (e: Exception) {
            Timber.e(e, "更新地块种植收获日期时发生错误: $plotId")
            Result.failure(e)
        }
    }

    // ========== 删除操作 ==========

    /**
     * 删除地块 (软删除)
     */
    suspend fun deletePlot(plotId: String): Result<Boolean> {
        return try {
            val deletedRows = landPlotDao.softDeletePlot(plotId)
            val success = deletedRows > 0
            
            if (success) {
                Timber.i("地块删除成功: $plotId")
            } else {
                Timber.w("地块删除失败: $plotId (可能已被删除)")
            }
            
            Result.success(success)
        } catch (e: Exception) {
            Timber.e(e, "删除地块时发生错误: $plotId")
            Result.failure(e)
        }
    }

    /**
     * 批量删除地块
     */
    suspend fun deletePlots(plotIds: List<String>): Result<Int> {
        return try {
            val deletedRows = landPlotDao.softDeletePlots(plotIds)
            
            Timber.i("批量删除地块完成: $deletedRows/${plotIds.size}")
            Result.success(deletedRows)
        } catch (e: Exception) {
            Timber.e(e, "批量删除地块时发生错误")
            Result.failure(e)
        }
    }

    // ========== 统计操作 ==========

    /**
     * 获取地块总数
     */
    suspend fun getPlotCount(): Int {
        return try {
            landPlotDao.getActivePlotCount()
        } catch (e: Exception) {
            Timber.e(e, "获取地块总数时发生错误")
            0
        }
    }

    /**
     * 获取总面积
     */
    suspend fun getTotalArea(): Double {
        return try {
            landPlotDao.getTotalArea() ?: 0.0
        } catch (e: Exception) {
            Timber.e(e, "获取总面积时发生错误")
            0.0
        }
    }

    /**
     * 获取平均面积
     */
    suspend fun getAverageArea(): Double {
        return try {
            landPlotDao.getAverageArea() ?: 0.0
        } catch (e: Exception) {
            Timber.e(e, "获取平均面积时发生错误")
            0.0
        }
    }

    /**
     * 获取作物类型统计
     */
    suspend fun getCropTypeStatistics(): List<CropTypeStatistic> {
        return try {
            landPlotDao.getCropTypeStatistics()
        } catch (e: Exception) {
            Timber.e(e, "获取作物类型统计时发生错误")
            emptyList()
        }
    }

    /**
     * 获取月度创建统计
     */
    suspend fun getMonthlyCreationStatistics(): List<MonthlyStatistic> {
        return try {
            landPlotDao.getMonthlyCreationStatistics()
        } catch (e: Exception) {
            Timber.e(e, "获取月度创建统计时发生错误")
            emptyList()
        }
    }

    // ========== 同步操作 ==========

    /**
     * 获取需要同步的地块
     */
    suspend fun getPlotsNeedingSync(): List<LandPlotFeature> {
        return try {
            val entities = landPlotDao.getPlotsNeedingSync()
            entities.mapNotNull { it.toDomainModel() }
        } catch (e: Exception) {
            Timber.e(e, "获取需要同步的地块时发生错误")
            emptyList()
        }
    }

    /**
     * 更新地块同步状态
     */
    suspend fun updateSyncStatus(plotId: String, status: String): Result<Boolean> {
        return try {
            val updatedRows = landPlotDao.updateSyncStatus(plotId, status)
            val success = updatedRows > 0
            
            if (success) {
                Timber.d("地块同步状态更新成功: $plotId -> $status")
            }
            
            Result.success(success)
        } catch (e: Exception) {
            Timber.e(e, "更新地块同步状态时发生错误: $plotId")
            Result.failure(e)
        }
    }

    // ========== 数据维护操作 ==========

    /**
     * 清理旧的已删除数据
     * @param daysToKeep 保留天数，默认30天
     */
    suspend fun cleanupOldDeletedData(daysToKeep: Int = 30): Result<Int> {
        return try {
            val cutoffTime = System.currentTimeMillis() - (daysToKeep * 24 * 60 * 60 * 1000L)
            val deletedRows = landPlotDao.cleanupOldDeletedPlots(cutoffTime)
            
            Timber.i("清理旧数据完成: 删除了 $deletedRows 条记录")
            Result.success(deletedRows)
        } catch (e: Exception) {
            Timber.e(e, "清理旧数据时发生错误")
            Result.failure(e)
        }
    }
} 