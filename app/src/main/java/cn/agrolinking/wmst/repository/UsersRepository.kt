package cn.agrolinking.wmst.repository

import cn.agrolinking.wmst.database.AppDatabase
import cn.agrolinking.wmst.database.asDomainModel
import cn.agrolinking.wmst.domain.User
import cn.agrolinking.wmst.network.UsersApi
import cn.agrolinking.wmst.network.model.asDatabaseModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject

class UsersRepository @Inject constructor(
    private val usersApi: UsersApi,
    private val appDatabase: AppDatabase
) {

    val users: Flow<List<User>?> =
        appDatabase.usersDao.getUsers().map { it?.asDomainModel() }

    suspend fun refreshUsers() {
        try {
            val users = usersApi.getUsers()
            appDatabase.usersDao.insertUsers(users.asDatabaseModel())
        } catch (e: Exception) {
            Timber.w(e)
        }
    }
}