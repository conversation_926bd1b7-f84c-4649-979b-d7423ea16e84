package cn.agrolinking.wmst.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ColumnInfo
import androidx.room.Index
import cn.agrolinking.wmst.domain.geojson.LandPlotFeature
import cn.agrolinking.wmst.domain.geojson.GeoJsonUtils

/**
 * 地块数据库实体
 * 使用GeoJSON字符串存储复杂几何体数据
 */
@Entity(
    tableName = "land_plots",
    indices = [
        Index(value = ["atlasId"]),
        Index(value = ["name"]),
        Index(value = ["cropType"]),
        Index(value = ["createdAt"]),
        Index(value = ["isActive"])
    ]
)
data class LandPlotEntity(
    @PrimaryKey 
    val id: String,
    
    @ColumnInfo(name = "name")
    val name: String,
    
    @ColumnInfo(name = "geoJson", typeAffinity = ColumnInfo.TEXT)
    val geoJson: String, // 完整的GeoJSON Feature字符串
    
    @ColumnInfo(name = "area")
    val area: Double, // 面积(平方米)
    
    @ColumnInfo(name = "perimeter")
    val perimeter: Double = 0.0, // 周长(米)
    
    @ColumnInfo(name = "cropType")
    val cropType: String? = null, // 作物类型
    
    @ColumnInfo(name = "plantingDate")
    val plantingDate: Long? = null, // 种植日期(时间戳)
    
    @ColumnInfo(name = "harvestDate")
    val harvestDate: Long? = null, // 收获日期(时间戳)
    
    @ColumnInfo(name = "description")
    val description: String? = null, // 描述信息
    
    @ColumnInfo(name = "tags", typeAffinity = ColumnInfo.TEXT)
    val tags: String = "[]", // JSON数组字符串
    
    @ColumnInfo(name = "atlasId")
    val atlasId: String? = null, // 所属图册ID
    
    @ColumnInfo(name = "createdBy")
    val createdBy: String? = null, // 创建者
    
    @ColumnInfo(name = "createdAt")
    val createdAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "updatedAt")
    val updatedAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "isActive")
    val isActive: Boolean = true, // 软删除标记
    
    @ColumnInfo(name = "version")
    val version: Long = 1, // 版本号，用于同步冲突解决
    
    @ColumnInfo(name = "syncStatus")
    val syncStatus: String = "LOCAL" // 同步状态: LOCAL, SYNCED, PENDING, ERROR
) {
    /**
     * 转换为领域模型
     */
    fun toDomainModel(): LandPlotFeature? {
        return GeoJsonUtils.deserializeLandPlot(geoJson)
    }
    
    /**
     * 获取格式化面积
     */
    fun getFormattedArea(): String {
        return when {
            area < 10000 -> String.format("%.1f 平方米", area)
            area < 1000000 -> String.format("%.2f 亩", area / 666.67)
            else -> String.format("%.2f 平方公里", area / 1000000)
        }
    }
    
    /**
     * 获取格式化周长
     */
    fun getFormattedPerimeter(): String {
        return when {
            perimeter < 1000 -> String.format("%.1f 米", perimeter)
            else -> String.format("%.2f 公里", perimeter / 1000)
        }
    }
    
    /**
     * 检查是否需要同步
     */
    fun needsSync(): Boolean {
        return syncStatus in listOf("LOCAL", "PENDING", "ERROR")
    }
    
    companion object {
        /**
         * 从领域模型创建数据库实体
         */
        fun fromDomainModel(
            landPlot: LandPlotFeature, 
            atlasId: String? = null,
            createdBy: String? = null
        ): LandPlotEntity {
            val geoJsonString = GeoJsonUtils.serializeLandPlot(landPlot)
            val tagsJson = com.google.gson.Gson().toJson(landPlot.properties.tags)
            
            return LandPlotEntity(
                id = landPlot.id,
                name = landPlot.properties.name,
                geoJson = geoJsonString,
                area = landPlot.properties.area,
                perimeter = landPlot.properties.perimeter,
                cropType = landPlot.properties.cropType,
                plantingDate = landPlot.properties.plantingDate,
                harvestDate = landPlot.properties.harvestDate,
                description = landPlot.properties.description,
                tags = tagsJson,
                atlasId = atlasId ?: landPlot.properties.atlasId,
                createdBy = createdBy ?: landPlot.properties.createdBy,
                createdAt = landPlot.properties.createdAt,
                updatedAt = landPlot.properties.updatedAt,
                isActive = true,
                version = 1,
                syncStatus = "LOCAL"
            )
        }
    }
}

/**
 * 同步状态枚举
 */
enum class SyncStatus {
    LOCAL,    // 仅本地存在
    SYNCED,   // 已同步
    PENDING,  // 待同步
    ERROR     // 同步错误
} 