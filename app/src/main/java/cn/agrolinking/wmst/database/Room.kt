package cn.agrolinking.wmst.database

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import cn.agrolinking.wmst.database.entity.LandPlotEntity
import cn.agrolinking.wmst.database.dao.LandPlotDao

@Dao
interface UsersDao {

    @Query("select * from UserEntity")
    fun getUsers(): Flow<List<UserEntity>?>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertUsers(users: List<UserEntity>)

    @Query("select * from DetailsEntity WHERE user LIKE :user")
    fun getDetails(user: String): Flow<DetailsEntity?>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertDetails(detailsEntity: DetailsEntity)
}

@Database(
    entities = [
        UserEntity::class, 
        DetailsEntity::class, 
        LandPlotEntity::class
    ], 
    version = 2,  // 增加版本号以支持地块表
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    abstract val usersDao: UsersDao
    abstract val landPlotDao: LandPlotDao
}