package cn.agrolinking.wmst.database.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import cn.agrolinking.wmst.database.entity.LandPlotEntity

/**
 * 地块数据访问对象
 * 提供响应式的数据库操作接口
 */
@Dao
interface LandPlotDao {
    
    // ========== 查询操作 ==========
    
    /**
     * 获取所有活跃地块 (响应式)
     */
    @Query("SELECT * FROM land_plots WHERE isActive = 1 ORDER BY createdAt DESC")
    fun getAllActivePlots(): Flow<List<LandPlotEntity>>
    
    /**
     * 根据图册ID获取地块 (响应式)
     */
    @Query("SELECT * FROM land_plots WHERE atlasId = :atlasId AND isActive = 1 ORDER BY createdAt DESC")
    fun getPlotsByAtlas(atlasId: String): Flow<List<LandPlotEntity>>
    
    /**
     * 根据ID获取单个地块 (响应式)
     */
    @Query("SELECT * FROM land_plots WHERE id = :plotId AND isActive = 1")
    fun getPlotById(plotId: String): Flow<LandPlotEntity?>
    
    /**
     * 根据ID获取单个地块 (挂起函数)
     */
    @Query("SELECT * FROM land_plots WHERE id = :plotId AND isActive = 1")
    suspend fun getPlotByIdSuspend(plotId: String): LandPlotEntity?
    
    /**
     * 根据作物类型查询地块
     */
    @Query("SELECT * FROM land_plots WHERE cropType = :cropType AND isActive = 1 ORDER BY createdAt DESC")
    fun getPlotsByCropType(cropType: String): Flow<List<LandPlotEntity>>
    
    /**
     * 根据名称模糊搜索地块
     */
    @Query("SELECT * FROM land_plots WHERE name LIKE '%' || :query || '%' AND isActive = 1 ORDER BY createdAt DESC")
    fun searchPlotsByName(query: String): Flow<List<LandPlotEntity>>
    
    /**
     * 获取指定面积范围内的地块
     */
    @Query("SELECT * FROM land_plots WHERE area BETWEEN :minArea AND :maxArea AND isActive = 1 ORDER BY area ASC")
    fun getPlotsByAreaRange(minArea: Double, maxArea: Double): Flow<List<LandPlotEntity>>
    
    /**
     * 获取指定日期范围内创建的地块
     */
    @Query("SELECT * FROM land_plots WHERE createdAt BETWEEN :startTime AND :endTime AND isActive = 1 ORDER BY createdAt DESC")
    fun getPlotsByDateRange(startTime: Long, endTime: Long): Flow<List<LandPlotEntity>>
    
    /**
     * 获取需要同步的地块
     */
    @Query("SELECT * FROM land_plots WHERE syncStatus IN ('LOCAL', 'PENDING', 'ERROR') AND isActive = 1")
    suspend fun getPlotsNeedingSync(): List<LandPlotEntity>
    
    /**
     * 获取地块总数
     */
    @Query("SELECT COUNT(*) FROM land_plots WHERE isActive = 1")
    suspend fun getActivePlotCount(): Int
    
    /**
     * 获取总面积
     */
    @Query("SELECT SUM(area) FROM land_plots WHERE isActive = 1")
    suspend fun getTotalArea(): Double?
    
    /**
     * 获取平均面积
     */
    @Query("SELECT AVG(area) FROM land_plots WHERE isActive = 1")
    suspend fun getAverageArea(): Double?
    
    // ========== 插入操作 ==========
    
    /**
     * 插入单个地块
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPlot(plot: LandPlotEntity): Long
    
    /**
     * 批量插入地块
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPlots(plots: List<LandPlotEntity>): List<Long>
    
    // ========== 更新操作 ==========
    
    /**
     * 更新地块
     */
    @Update
    suspend fun updatePlot(plot: LandPlotEntity): Int
    
    /**
     * 批量更新地块
     */
    @Update
    suspend fun updatePlots(plots: List<LandPlotEntity>): Int
    
    /**
     * 更新地块同步状态
     */
    @Query("UPDATE land_plots SET syncStatus = :status, updatedAt = :timestamp WHERE id = :plotId")
    suspend fun updateSyncStatus(plotId: String, status: String, timestamp: Long = System.currentTimeMillis()): Int
    
    /**
     * 更新地块版本号
     */
    @Query("UPDATE land_plots SET version = :version, updatedAt = :timestamp WHERE id = :plotId")
    suspend fun updateVersion(plotId: String, version: Long, timestamp: Long = System.currentTimeMillis()): Int
    
    /**
     * 更新地块基本信息
     */
    @Query("""
        UPDATE land_plots 
        SET name = :name, 
            description = :description, 
            cropType = :cropType, 
            updatedAt = :timestamp
        WHERE id = :plotId
    """)
    suspend fun updatePlotBasicInfo(
        plotId: String, 
        name: String, 
        description: String?, 
        cropType: String?, 
        timestamp: Long = System.currentTimeMillis()
    ): Int
    
    /**
     * 更新种植收获日期
     */
    @Query("""
        UPDATE land_plots 
        SET plantingDate = :plantingDate, 
            harvestDate = :harvestDate, 
            updatedAt = :timestamp
        WHERE id = :plotId
    """)
    suspend fun updatePlantingHarvestDates(
        plotId: String, 
        plantingDate: Long?, 
        harvestDate: Long?, 
        timestamp: Long = System.currentTimeMillis()
    ): Int
    
    // ========== 删除操作 ==========
    
    /**
     * 软删除地块 (推荐使用)
     */
    @Query("UPDATE land_plots SET isActive = 0, updatedAt = :timestamp WHERE id = :plotId")
    suspend fun softDeletePlot(plotId: String, timestamp: Long = System.currentTimeMillis()): Int
    
    /**
     * 硬删除地块 (谨慎使用)
     */
    @Query("DELETE FROM land_plots WHERE id = :plotId")
    suspend fun hardDeletePlot(plotId: String): Int
    
    /**
     * 批量软删除地块
     */
    @Query("UPDATE land_plots SET isActive = 0, updatedAt = :timestamp WHERE id IN (:plotIds)")
    suspend fun softDeletePlots(plotIds: List<String>, timestamp: Long = System.currentTimeMillis()): Int
    
    /**
     * 清理已软删除的旧数据 (删除30天前软删除的记录)
     */
    @Query("DELETE FROM land_plots WHERE isActive = 0 AND updatedAt < :cutoffTime")
    suspend fun cleanupOldDeletedPlots(cutoffTime: Long): Int
    
    // ========== 事务操作 ==========
    
    /**
     * 事务：保存或更新地块
     */
    @Transaction
    suspend fun saveOrUpdatePlot(plot: LandPlotEntity): Boolean {
        val existing = getPlotByIdSuspend(plot.id)
        return if (existing != null) {
            // 更新时递增版本号
            val updated = plot.copy(
                version = existing.version + 1,
                updatedAt = System.currentTimeMillis()
            )
            updatePlot(updated) > 0
        } else {
            insertPlot(plot) > 0
        }
    }
    
    /**
     * 事务：批量保存或更新地块
     */
    @Transaction
    suspend fun saveOrUpdatePlots(plots: List<LandPlotEntity>): Int {
        var savedCount = 0
        plots.forEach { plot ->
            if (saveOrUpdatePlot(plot)) {
                savedCount++
            }
        }
        return savedCount
    }
    
    // ========== 统计查询 ==========
    
    /**
     * 获取作物类型统计
     */
    @Query("""
        SELECT cropType, COUNT(*) as count, SUM(area) as totalArea 
        FROM land_plots 
        WHERE isActive = 1 AND cropType IS NOT NULL 
        GROUP BY cropType 
        ORDER BY count DESC
    """)
    suspend fun getCropTypeStatistics(): List<CropTypeStatistic>
    
    /**
     * 获取月度创建统计
     */
    @Query("""
        SELECT 
            strftime('%Y-%m', datetime(createdAt/1000, 'unixepoch')) as month,
            COUNT(*) as count,
            SUM(area) as totalArea
        FROM land_plots 
        WHERE isActive = 1 
        GROUP BY month 
        ORDER BY month DESC
    """)
    suspend fun getMonthlyCreationStatistics(): List<MonthlyStatistic>
}

/**
 * 作物类型统计数据类
 */
data class CropTypeStatistic(
    val cropType: String,
    val count: Int,
    val totalArea: Double
)

/**
 * 月度统计数据类
 */
data class MonthlyStatistic(
    val month: String,
    val count: Int,
    val totalArea: Double
) 