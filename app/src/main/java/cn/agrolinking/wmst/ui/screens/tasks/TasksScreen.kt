package cn.agrolinking.wmst.ui.screens.tasks

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccessTime
import androidx.compose.material.icons.filled.Assignment
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cn.agrolinking.wmst.ui.theme.WmstTheme

/**
 * 工单页面 - 显示分配给作业人员的任务
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TasksScreen(
    modifier: Modifier = Modifier
) {
    var selectedFilter by remember { mutableStateOf("全部") }
    val filters = listOf("全部", "待执行", "进行中", "已完成")
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 页面标题
        Text(
            text = "我的工单",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onBackground
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 筛选器
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            filters.forEach { filter ->
                FilterChip(
                    onClick = { selectedFilter = filter },
                    label = { Text(filter) },
                    selected = selectedFilter == filter
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 工单列表
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(getSampleTasks().filter { task ->
                selectedFilter == "全部" || task.status == selectedFilter
            }) { task ->
                TaskCard(task = task)
            }
        }
    }
}

@Composable
private fun TaskCard(
    task: Task
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 任务标题和状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
                TaskStatusChip(status = task.status)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 任务描述
            Text(
                text = task.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 任务信息
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.LocationOn,
                    contentDescription = "位置",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = task.location,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Icon(
                    imageVector = Icons.Default.AccessTime,
                    contentDescription = "时间",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = task.deadline,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 优先级
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "优先级：",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                PriorityChip(priority = task.priority)
            }
        }
    }
}

@Composable
private fun TaskStatusChip(status: String) {
    val (color, icon) = when (status) {
        "待执行" -> Pair(MaterialTheme.colorScheme.primary, Icons.Default.Assignment)
        "进行中" -> Pair(Color(0xFFFF9800), Icons.Default.Schedule)
        "已完成" -> Pair(Color(0xFF4CAF50), Icons.Default.CheckCircle)
        else -> Pair(MaterialTheme.colorScheme.outline, Icons.Default.Assignment)
    }
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = status,
                tint = color,
                modifier = Modifier.size(12.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = status,
                style = MaterialTheme.typography.labelSmall,
                color = color,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun PriorityChip(priority: String) {
    val color = when (priority) {
        "高" -> Color(0xFFFF5722)
        "中" -> Color(0xFFFF9800)
        "低" -> Color(0xFF4CAF50)
        else -> MaterialTheme.colorScheme.outline
    }
    
    Box(
        modifier = Modifier
            .padding(start = 4.dp)
    ) {
        Text(
            text = priority,
            style = MaterialTheme.typography.labelSmall,
            color = color,
            fontWeight = FontWeight.Bold
        )
    }
}

// 示例数据
data class Task(
    val id: String,
    val title: String,
    val description: String,
    val location: String,
    val deadline: String,
    val status: String,
    val priority: String
)

private fun getSampleTasks(): List<Task> {
    return listOf(
        Task(
            id = "1",
            title = "东区玉米田除草作业",
            description = "对东区15亩玉米田进行除草作业，注意保护幼苗",
            location = "东区A-15地块",
            deadline = "今天 17:00",
            status = "待执行",
            priority = "高"
        ),
        Task(
            id = "2",
            title = "南区水稻田施肥",
            description = "对南区水稻田进行第二次追肥，使用复合肥",
            location = "南区B-08地块",
            deadline = "明天 09:00",
            status = "进行中",
            priority = "中"
        ),
        Task(
            id = "3",
            title = "西区大豆田病虫害检查",
            description = "检查西区大豆田病虫害情况，发现问题及时上报",
            location = "西区C-22地块",
            deadline = "后天 14:00",
            status = "待执行",
            priority = "中"
        ),
        Task(
            id = "4",
            title = "北区小麦收割",
            description = "北区小麦已成熟，进行收割作业",
            location = "北区D-05地块",
            deadline = "昨天 18:00",
            status = "已完成",
            priority = "高"
        ),
        Task(
            id = "5",
            title = "中心区域设备维护",
            description = "对中心区域的灌溉设备进行日常维护检查",
            location = "中心区域",
            deadline = "上周五 16:00",
            status = "已完成",
            priority = "低"
        )
    )
}

@Preview(showBackground = true)
@Composable
fun TasksScreenPreview() {
    WmstTheme {
        TasksScreen()
    }
} 