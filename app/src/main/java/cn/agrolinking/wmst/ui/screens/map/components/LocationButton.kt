package cn.agrolinking.wmst.ui.screens.map.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 定位按钮组件
 * 提供"定位到我的位置"功能
 */
@Composable
fun LocationButton(
    onLocationClick: () -> Unit,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false
) {
    Box(
        modifier = modifier
            .size(52.dp)
            .shadow(
                elevation = 8.dp,
                shape = RoundedCornerShape(16.dp)
            )
            .clip(RoundedCornerShape(16.dp))
            .background(Color.White.copy(alpha = 0.9f))
            .clickable(enabled = !isLoading) { 
                android.util.Log.d("LocationButton", "定位按钮被点击")
                onLocationClick() 
            },
        contentAlignment = Alignment.Center
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(24.dp),
                strokeWidth = 2.dp,
                color = MaterialTheme.colorScheme.primary
            )
        } else {
            // 自定义定位图标
            LocationIcon()
        }
    }
}

/**
 * 自定义定位图标组件
 * 复现HTML设计中的十字定位图标
 */
@Composable
private fun LocationIcon() {
    Box(
        modifier = Modifier.size(24.dp),
        contentAlignment = Alignment.Center
    ) {
        // 外圆圈
        Box(
            modifier = Modifier
                .size(16.dp)
                .background(
                    Color.Transparent,
                    shape = androidx.compose.foundation.shape.CircleShape
                )
                .background(
                    Color.Transparent,
                    shape = androidx.compose.foundation.shape.CircleShape
                )
        ) {
            // 使用Material图标的定位符号
            Text(
                text = "🎯",
                fontSize = 18.sp,
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

/**
 * 更简洁的定位图标 - 使用Material Design图标
 */
@Composable
fun SimpleLocationIcon() {
    Text(
        text = "📍",
        fontSize = 20.sp,
        fontWeight = FontWeight.Bold
    )
} 