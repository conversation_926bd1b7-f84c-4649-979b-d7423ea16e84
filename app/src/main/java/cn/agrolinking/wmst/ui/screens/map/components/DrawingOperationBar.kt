package cn.agrolinking.wmst.ui.screens.map.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Done
import androidx.compose.material.icons.filled.Undo
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.agrolinking.wmst.map.drawing.DrawingMode
import android.util.Log

/**
 * 绘制操作栏组件
 * 设计：灰色半透明背景，包含模式切换下拉框和操作按钮
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DrawingOperationBar(
    canUndo: Boolean,
    canClear: Boolean,
    onUndo: () -> Unit,
    onClear: () -> Unit,
    onClose: () -> Unit,
    onFinish: (() -> Unit)? = null, // 完成按钮变为可选
    showFinishButton: Boolean = false, // 控制是否显示完成按钮
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface)
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧按钮组
        Row(
            horizontalArrangement = Arrangement.Start,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.weight(1f)
        ) {
            // 撤销按钮
            IconButton(
                onClick = onUndo,
                enabled = canUndo
            ) {
                Icon(
                    imageVector = Icons.Default.Undo,
                    contentDescription = "撤销",
                    tint = if (canUndo) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                )
            }

            // 清除按钮
            IconButton(
                onClick = onClear,
                enabled = canClear
            ) {
                Icon(
                    imageVector = Icons.Default.Clear,
                    contentDescription = "清除",
                    tint = if (canClear) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                )
            }
        }

        // 右侧按钮组
        Row(
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.weight(1f)
        ) {
            // 关闭按钮
            TextButton(
                onClick = onClose,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text("关闭")
            }

            // 完成按钮 - 仅在需要时显示
            if (showFinishButton && onFinish != null) {
                Button(
                    onClick = onFinish,
                    enabled = canClear
                ) {
                    Text("完成")
                }
            }
        }
    }
}

/**
 * 操作按钮组件
 */
@Composable
private fun OperationButton(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    contentDescription: String,
    enabled: Boolean = true,
    tint: Color = Color.White,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(40.dp)
            .clip(RoundedCornerShape(6.dp))
            .background(
                if (enabled) {
                    Color.White.copy(alpha = 0.9f)
                } else {
                    Color.White.copy(alpha = 0.3f)
                }
            )
            .clickable(enabled = enabled) { onClick() }
            .alpha(if (enabled) 1f else 0.5f),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            tint = if (enabled) {
                if (tint == Color.White) Color.Black else tint
            } else {
                Color.Gray
            },
            modifier = Modifier.size(24.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun DrawingOperationBarPreview() {
    DrawingOperationBar(
        canUndo = true,
        canClear = true,
        onUndo = {},
        onClear = {},
        onClose = {},
        onFinish = {},
        showFinishButton = true,
        modifier = Modifier.padding(16.dp)
    )
} 