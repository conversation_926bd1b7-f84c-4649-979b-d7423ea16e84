package cn.agrolinking.wmst.ui.screens.map

import cn.agrolinking.wmst.map.model.TileSource
import cn.agrolinking.wmst.map.drawing.DrawingMode
import cn.agrolinking.wmst.domain.drawing.LandPlotInProgress
import cn.agrolinking.wmst.domain.LandPlot
import cn.agrolinking.wmst.domain.geojson.LandPlotFeature
import org.maplibre.android.geometry.LatLng

/**
 * 地图工具枚举 - 简化为基本工具
 */
enum class MapTool {
    VIEW,    // 浏览模式 (默认)
    DRAW     // 绘制模式
}

/**
 * 地图模式
 */
enum class MapMode {
    NORMAL,         // 普通模式
    DRAWING,        // 绘制模式
    ANNOTATION,     // 标注模式
    MEASUREMENT     // 测量模式
}

/**
 * 绘制状态数据类 - 支持复杂地块
 */
data class DrawingState(
    val mode: DrawingMode = DrawingMode.NONE,
    val workingLandPlot: LandPlotInProgress? = null,
    val selectedLandPlotId: String? = null,
    val isActive: Boolean = false,
    val isDragMode: Boolean = false,
    val isDragging: Boolean = false,
    val dragTargetIndex: Int? = null, // 兼容旧代码
    val complexDragTarget: ComplexDragTarget? = null, // 新的复杂拖拽目标
    val dragOriginalPoint: LatLng? = null,
    val showMidpoints: Boolean = false,
    val canUndo: Boolean = false,
    val canClear: Boolean = false,
    // 新增：验证相关状态
    val validationResult: cn.agrolinking.wmst.map.validation.GeometryValidator.ValidationResult? = null,
    val enableAutoValidation: Boolean = true,
    val showValidationIndicators: Boolean = true
) {
    /**
     * 当前正在绘制的多边形
     */
    val currentPolygon: cn.agrolinking.wmst.domain.drawing.PolygonInProgress?
        get() = workingLandPlot?.polygons?.getOrNull(workingLandPlot.activePolygonIndex)
    
    /**
     * 当前正在绘制的环（外边界或孔洞）
     */
    val currentRing: List<LatLng>
        get() = when (workingLandPlot?.activeRingIndex) {
            0 -> currentPolygon?.exterior ?: emptyList()
            else -> currentPolygon?.holes?.getOrNull((workingLandPlot?.activeRingIndex ?: 1) - 1) ?: emptyList()
        }
    
    /**
     * 状态指示文本
     */
    val statusText: String
        get() = when {
            // 拖拽模式优先显示
            isDragMode -> "拖拽模式 - 可拖拽顶点调整形状"
            // 有地块且有点时显示详细状态
            workingLandPlot != null && currentRing.isNotEmpty() -> {
                val plot = workingLandPlot
                val polygonIndex = plot.activePolygonIndex + 1
                val ringType = if (plot.activeRingIndex == 0) "外边界" else "孔洞${plot.activeRingIndex}"
                "正在绘制：地块 > 多边形$polygonIndex > $ringType"
            }
            // 绘制模式但无点时
            mode != DrawingMode.NONE && isActive -> "开始绘制地块"
            else -> ""
        }

    /**
     * 检查当前环是否可以完成（至少3个点）
     */
    val canCompleteCurrentRing: Boolean
        get() = currentRing.size >= 3

    /**
     * 检查当前多边形是否可以添加孔洞（外边界已完成）
     */
    val canAddHole: Boolean
        get() = currentPolygon?.exterior?.size ?: 0 >= 3 && workingLandPlot?.activeRingIndex == 0

    /**
     * 检查当前地块是否可以添加新多边形（至少有一个完成的多边形）
     */
    val canAddNewPolygon: Boolean
        get() = workingLandPlot?.polygons?.any { it.exterior.size >= 3 } == true

    /**
     * 检查当前地块是否可以保存（至少有一个有效多边形）
     */
    val canSaveLandPlot: Boolean
        get() = workingLandPlot?.polygons?.any { it.exterior.size >= 3 } == true && 
                (validationResult?.isValid != false || !enableAutoValidation)

    /**
     * 验证状态信息
     */
    val validationStatusText: String
        get() = when {
            validationResult == null -> ""
            validationResult.isValid && !validationResult.hasWarnings -> "✓ 几何体有效"
            validationResult.isValid && validationResult.hasWarnings -> "⚠ 有效但有警告 (${validationResult.warnings.size})"
            else -> "✗ 验证失败 (${validationResult.errors.size}个错误)"
        }
    
    /**
     * 是否有验证问题
     */
    val hasValidationIssues: Boolean
        get() = validationResult?.hasErrors == true || validationResult?.hasWarnings == true
    
    /**
     * 验证问题数量
     */
    val validationIssueCount: Int
        get() = (validationResult?.errors?.size ?: 0) + (validationResult?.warnings?.size ?: 0)

    /**
     * 兼容旧代码的currentPoints属性
     */
    val currentPoints: List<LatLng>
        get() = currentRing
}

/**
 * 地块查看状态
 */
data class ViewingState(
    val loadedLandPlots: List<LandPlotFeature> = emptyList(),
    val selectedLandPlot: LandPlotFeature? = null,
    val isLoading: Boolean = false,
    val error: String? = null
) {
    /**
     * 是否有已选择的地块
     */
    val hasSelectedPlot: Boolean
        get() = selectedLandPlot != null

    /**
     * 是否显示地块列表面板
     */
    val showLandPlotList: Boolean
        get() = loadedLandPlots.isNotEmpty()
}

/**
 * 编辑状态
 */
data class EditingState(
    val editingLandPlot: LandPlotFeature? = null,
    val workingLandPlot: LandPlotInProgress? = null,
    val isEditing: Boolean = false,
    val hasUnsavedChanges: Boolean = false
) {
    /**
     * 是否正在编辑
     */
    val isEditMode: Boolean
        get() = editingLandPlot != null && isEditing

    /**
     * 可以保存编辑
     */
    val canSaveEdits: Boolean
        get() = isEditMode && hasUnsavedChanges
}

/**
 * 统一的UI状态
 */
data class MapUiState(
    // 瓦片源相关
    val baseLayerSources: List<TileSource> = emptyList(),
    val overlayLayerSources: List<TileSource> = emptyList(),
    val selectedBaseLayer: TileSource? = null,
    val selectedOverlayLayer: TileSource? = null,
    val isTileSourcesLoaded: Boolean = false,
    
    // 工具和模式
    val currentTool: MapTool = MapTool.VIEW,
    val currentMode: MapMode = MapMode.NORMAL,
    
    // 状态管理
    val drawingState: DrawingState = DrawingState(),
    val viewingState: ViewingState = ViewingState(),
    val editingState: EditingState = EditingState(),
    
    // 面板控制
    val showLayerPanel: Boolean = false,
    val showDrawingControls: Boolean = false,
    val showLandPlotInfo: Boolean = false,
    
    // 地图状态
    val latitude: Double = 0.0,
    val longitude: Double = 0.0,
    val zoomLevel: Double = 0.0
) {
    /**
     * 当前活跃的操作模式
     */
    val activeOperationMode: OperationMode
        get() = when {
            drawingState.isActive -> OperationMode.DRAWING
            editingState.isEditing -> OperationMode.EDITING
            viewingState.hasSelectedPlot -> OperationMode.VIEWING
            else -> OperationMode.BROWSING
        }

    /**
     * 是否显示绘制控制面板
     */
    val shouldShowDrawingControls: Boolean
        get() = drawingState.isActive || editingState.isEditing

    /**
     * 当前状态的提示文本 (统一从drawingState获取)
     */
    val statusText: String
        get() = drawingState.statusText.ifEmpty {
            when (activeOperationMode) {
                OperationMode.EDITING -> "编辑模式：${editingState.editingLandPlot?.properties?.name ?: "未知地块"}"
                OperationMode.VIEWING -> "查看：${viewingState.selectedLandPlot?.properties?.name ?: "未知地块"}"
                else -> ""
            }
        }
}

/**
 * 操作模式枚举
 */
enum class OperationMode {
    BROWSING,   // 浏览模式
    DRAWING,    // 绘制模式
    VIEWING,    // 查看模式
    EDITING     // 编辑模式
}

/**
 * 地图事件
 */
sealed class MapEvent {
    data class SatelliteSourceChanged(val sourceId: String) : MapEvent()
    data class RoadLayerVisibilityChanged(val visible: Boolean) : MapEvent()
    data class RoadLayerOpacityChanged(val opacity: Float) : MapEvent()
    
    // 绘制相关事件
    data class StartDrawing(val mode: DrawingMode) : MapEvent()
    object CompleteCurrentRing : MapEvent()
    object StartNewHole : MapEvent()
    object StartNewPolygon : MapEvent()
    object SaveCurrentLandPlot : MapEvent()
    object CancelDrawing : MapEvent()
    
    // 查看编辑相关事件
    data class SelectLandPlot(val landPlot: LandPlotFeature) : MapEvent()
    data class StartEditingLandPlot(val landPlot: LandPlotFeature) : MapEvent()
    object SaveEdits : MapEvent()
    object CancelEditing : MapEvent()
    
    // 交互事件
    data class AddPoint(val point: LatLng) : MapEvent()
    object UndoLastPoint : MapEvent()
    object ClearCurrentDrawing : MapEvent()
    data class StartDrag(val targetIndex: Int, val originalPoint: LatLng) : MapEvent()
    data class UpdateDraggedPoint(val newPoint: LatLng) : MapEvent()
    object EndDrag : MapEvent()
    data class InsertMidpoint(val edgeStartIndex: Int, val midpoint: LatLng) : MapEvent()
    
    // 新增：统一交互事件
    data class Click(val latLng: LatLng) : MapEvent()
    data class LongPress(val latLng: LatLng) : MapEvent()
    data class DragStart(val vertexIndex: Int, val latLng: LatLng) : MapEvent()
    data class DragUpdate(val latLng: LatLng) : MapEvent()
    object DragEnd : MapEvent()
    data class MidpointClick(val edgeIndex: Int, val latLng: LatLng) : MapEvent()
    data class DragPreview(val preview: cn.agrolinking.wmst.map.interaction.DragInteractionManager.DragPreview?) : MapEvent()
    
    // 兼容性事件（用于向后兼容旧回调）
    data class CameraPositionChanged(val latitude: Double, val longitude: Double, val zoom: Double) : MapEvent()
    data class DrawingCompleted(val landPlot: LandPlot) : MapEvent()
    data class DrawingModeManagerReady(val manager: cn.agrolinking.wmst.map.drawing.DrawingModeManager) : MapEvent()
}

/**
 * 复杂几何体拖拽目标 - 支持多多边形和孔洞
 */
sealed class ComplexDragTarget {
    /**
     * 顶点拖拽目标
     */
    data class Vertex(
        val polygonIndex: Int,  // 多边形索引
        val ringIndex: Int,     // 环索引 (0=外边界, >0=孔洞)
        val vertexIndex: Int    // 顶点索引
    ) : ComplexDragTarget()
    
    /**
     * 边线中点拖拽目标
     */
    data class EdgeMidpoint(
        val polygonIndex: Int,    // 多边形索引
        val ringIndex: Int,       // 环索引 (0=外边界, >0=孔洞)
        val edgeStartIndex: Int,  // 边线起点索引
        val edgeEndIndex: Int     // 边线终点索引
    ) : ComplexDragTarget()
} 