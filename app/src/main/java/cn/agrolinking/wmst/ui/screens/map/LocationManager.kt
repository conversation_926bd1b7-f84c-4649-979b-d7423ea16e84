package cn.agrolinking.wmst.ui.screens.map

import android.content.Context
import android.util.Log
import androidx.compose.runtime.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import org.maplibre.android.camera.CameraPosition
import org.maplibre.android.camera.CameraUpdateFactory
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.location.LocationComponent
import org.maplibre.android.location.modes.CameraMode
import org.maplibre.android.location.permissions.PermissionsManager
import org.maplibre.android.maps.MapLibreMap
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.FusedLocationProviderClient

/**
 * 定位状态枚举
 */
enum class LocationState {
    IDLE,           // 空闲状态
    LOADING,        // 正在定位
    SUCCESS,        // 定位成功
    ERROR,          // 定位失败
    NO_PERMISSION   // 无权限
}

/**
 * 定位管理器
 * 管理用户位置获取和地图相机控制
 */
class LocationManager(
    private val context: Context,
    private val coroutineScope: CoroutineScope
) {
    private val TAG = "LocationManager"
    
    // Google Play位置服务客户端
    private val fusedLocationClient: FusedLocationProviderClient = 
        LocationServices.getFusedLocationProviderClient(context)
    
    // 定位状态
    private val _locationState = MutableStateFlow(LocationState.IDLE)
    val locationState: StateFlow<LocationState> = _locationState.asStateFlow()
    
    // 当前位置
    private val _currentLocation = MutableStateFlow<LatLng?>(null)
    val currentLocation: StateFlow<LatLng?> = _currentLocation.asStateFlow()
    
    // MapLibre相关组件
    private var mapLibreMap: MapLibreMap? = null
    private var locationComponent: LocationComponent? = null
    
    /**
     * 初始化定位管理器
     */
    fun initialize(map: MapLibreMap, locationComp: LocationComponent) {
        this.mapLibreMap = map
        this.locationComponent = locationComp
        Log.i(TAG, "定位管理器已初始化")
    }
    
    /**
     * 定位到我的位置
     */
    fun locateToMyPosition() {
        Log.d(TAG, "定位按钮点击 - 开始执行定位")
        coroutineScope.launch(Dispatchers.Main) {
            if (!PermissionsManager.areLocationPermissionsGranted(context)) {
                Log.w(TAG, "位置权限未授权")
                _locationState.value = LocationState.NO_PERMISSION
                return@launch
            }
            
            Log.d(TAG, "位置权限已授权，继续定位流程")

            val map = mapLibreMap
            val locComponent = locationComponent
            Log.d(TAG, "地图状态: map=${map != null}, locationComponent=${locComponent != null}")
            
            if (map == null || locComponent == null) {
                Log.e(TAG, "地图或LocationComponent未初始化")
                _locationState.value = LocationState.ERROR
                return@launch
            }

            try {
                _locationState.value = LocationState.LOADING
                
                // 首先检查上次已知位置
                val lastLocation = locComponent.lastKnownLocation
                Log.d(TAG, "上次已知位置: $lastLocation")
                
                if (lastLocation != null) {
                    val latLng = LatLng(lastLocation.latitude, lastLocation.longitude)
                    Log.i(TAG, "使用上次已知位置: ${latLng.latitude}, ${latLng.longitude}")
                    moveToLocation(latLng)
                    _currentLocation.value = latLng
                    _locationState.value = LocationState.SUCCESS
                } else {
                    Log.d(TAG, "无上次已知位置，使用Android系统位置服务")
                    
                    // 使用Android系统位置服务获取位置
                    requestSystemLocation()
                }
            } catch (e: Exception) {
                Log.e(TAG, "定位过程发生异常", e)
                _locationState.value = LocationState.ERROR
            }
        }
    }
    
    /**
     * 移动地图到指定位置
     */
    private fun moveToLocation(latLng: LatLng, zoom: Double = 16.0) {
        val map = mapLibreMap ?: return
        
        try {
            val cameraPosition = CameraPosition.Builder()
                .target(latLng)
                .zoom(zoom)
                .build()
            
            // 带动画的相机移动
            map.animateCamera(
                CameraUpdateFactory.newCameraPosition(cameraPosition),
                1500 // 1.5秒动画
            )
            
            Log.i(TAG, "地图相机已移动到: ${latLng.latitude}, ${latLng.longitude}, 缩放级别: $zoom")
            
        } catch (e: Exception) {
            Log.e(TAG, "移动地图相机失败: ${e.message}", e)
        }
    }
    
    /**
     * 重置定位状态
     */
    fun resetState() {
        _locationState.value = LocationState.IDLE
    }
    
    /**
     * 检查是否有位置权限
     */
    fun hasLocationPermission(): Boolean {
        return PermissionsManager.areLocationPermissionsGranted(context)
    }
    
    /**
     * 使用Android系统位置服务获取当前位置
     */
    @Suppress("MissingPermission")
    private fun requestSystemLocation() {
        Log.d(TAG, "开始使用系统位置服务获取位置")
        
        try {
            fusedLocationClient.lastLocation
                .addOnSuccessListener { location ->
                    if (location != null) {
                        val latLng = LatLng(location.latitude, location.longitude)
                        Log.i(TAG, "系统位置服务获取到位置: ${latLng.latitude}, ${latLng.longitude}")
                        
                        // 更新LocationComponent的位置
                        locationComponent?.forceLocationUpdate(location)
                        
                        _currentLocation.value = latLng
                        _locationState.value = LocationState.SUCCESS
                        moveToLocation(latLng)
                    } else {
                        Log.w(TAG, "系统位置服务无法获取位置")
                        _locationState.value = LocationState.ERROR
                    }
                }
                .addOnFailureListener { exception ->
                    Log.e(TAG, "系统位置服务获取位置失败", exception)
                    _locationState.value = LocationState.ERROR
                }
        } catch (e: Exception) {
            Log.e(TAG, "系统位置服务请求失败", e)
            _locationState.value = LocationState.ERROR
        }
    }
    
    /**
     * 获取权限状态描述
     */
    fun getLocationStateMessage(): String {
        return when (_locationState.value) {
            LocationState.IDLE -> "点击定位按钮获取当前位置"
            LocationState.LOADING -> "正在定位中..."
            LocationState.SUCCESS -> "定位成功"
            LocationState.ERROR -> "定位失败，请检查GPS或网络"
            LocationState.NO_PERMISSION -> "需要位置权限才能定位"
        }
    }
} 