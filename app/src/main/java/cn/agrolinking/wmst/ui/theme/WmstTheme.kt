package cn.agrolinking.wmst.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

// 万亩数田主题色彩 - 基于原型的橙色系
private val Orange50 = Color(0xFFFFF3E0)
private val Orange100 = Color(0xFFFFE0B2)
private val Orange200 = Color(0xFFFFCC80)
private val Orange300 = Color(0xFFFFB74D)
private val Orange400 = Color(0xFFFFA726)
private val Orange500 = Color(0xFFFF9800) // 主色
private val Orange600 = Color(0xFFFB8C00)
private val Orange700 = Color(0xFFF57700)
private val Orange800 = Color(0xFFEF6C00)
private val Orange900 = Color(0xFFE65100)

private val Green50 = Color(0xFFE8F5E8)
private val Green300 = Color(0xFF81C784)
private val Green500 = Color(0xFF4CAF50)
private val Green700 = Color(0xFF388E3C)
private val Green800 = Color(0xFF2E7D32)

private val LightColorScheme = lightColorScheme(
    primary = Orange500,
    onPrimary = Color.White,
    primaryContainer = Orange100,
    onPrimaryContainer = Orange900,
    
    secondary = Green500,
    onSecondary = Color.White,
    secondaryContainer = Green50,
    onSecondaryContainer = Green700,
    
    tertiary = Orange300,
    onTertiary = Color.White,
    
    background = Color(0xFFF5F7FA),
    onBackground = Color(0xFF1C1B1F),
    
    surface = Color.White,
    onSurface = Color(0xFF1C1B1F),
    surfaceVariant = Orange50,
    onSurfaceVariant = Orange800,
    
    error = Color(0xFFBA1A1A),
    onError = Color.White,
    
    outline = Color(0xFF79747E),
    outlineVariant = Color(0xFFCAC4D0)
)

private val DarkColorScheme = darkColorScheme(
    primary = Orange300,
    onPrimary = Orange900,
    primaryContainer = Orange700,
    onPrimaryContainer = Orange100,
    
    secondary = Green300,
    onSecondary = Green800,
    secondaryContainer = Green700,
    onSecondaryContainer = Green50,
    
    tertiary = Orange200,
    onTertiary = Orange800,
    
    background = Color(0xFF1C1B1F),
    onBackground = Color(0xFFE6E1E5),
    
    surface = Color(0xFF1C1B1F),
    onSurface = Color(0xFFE6E1E5),
    surfaceVariant = Color(0xFF49454F),
    onSurfaceVariant = Color(0xFFCAC4D0),
    
    error = Color(0xFFFFB4AB),
    onError = Color(0xFF690005),
    
    outline = Color(0xFF938F99),
    outlineVariant = Color(0xFF49454F)
)



@Composable
fun WmstTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    val colorScheme = if (darkTheme) {
        DarkColorScheme
    } else {
        LightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
} 