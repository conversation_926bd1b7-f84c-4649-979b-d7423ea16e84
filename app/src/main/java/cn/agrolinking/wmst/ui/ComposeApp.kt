package cn.agrolinking.wmst.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.Lifecycle
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument

import cn.agrolinking.wmst.ui.details.DetailsScreen
import cn.agrolinking.wmst.ui.screens.dashboard.DashboardScreen
import cn.agrolinking.wmst.ui.screens.map.MapScreen
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController

import cn.agrolinking.wmst.ui.screens.profile.ProfileScreen
import cn.agrolinking.wmst.ui.screens.records.RecordsScreen
import cn.agrolinking.wmst.ui.screens.tasks.TasksScreen
import cn.agrolinking.wmst.ui.users.UsersScreen

@Composable
fun ComposeApp() {
    val navController = rememberNavController()
    
    // 现在不使用Scaffold，因为导航栏在MainActivity中处理
    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
            NavHost(
                navController = navController,
                startDestination = Route.MAP // 设置地图为启动页面，方便测试绘制功能
            ) {
                composable(Route.DASHBOARD) {
                    DashboardScreen()
                }
                composable(Route.TASKS) {
                    TasksScreen()
                }
                composable(Route.MAP) {
                    MapScreen()
                }
                composable(Route.RECORDS) {
                    RecordsScreen()
                }
                composable(Route.PROFILE) {
                    ProfileScreen()
                }
                
                // 保留原有的详细页面导航
                composable(Route.USER) { backStackEntry ->
                    UsersScreen(
                        onUserClick = { username ->
                            // In order to discard duplicated navigation events, we check the Lifecycle
                            if (backStackEntry.lifecycle.currentState == Lifecycle.State.RESUMED) {
                                navController.navigate("${Route.DETAIL}/$username")
                            }
                        }
                    )
                }
                composable(
                    route = "${Route.DETAIL}/{${Argument.USERNAME}}",
                    arguments = listOf(
                        navArgument(Argument.USERNAME) {
                            type = NavType.StringType
                        }
                    ),
                ) {
                    DetailsScreen()
                }
            }
    }
}

object Route {
    const val DASHBOARD = "dashboard"
    const val TASKS = "tasks"
    const val MAP = "map"
    const val RECORDS = "records"
    const val PROFILE = "profile"
    const val USER = "user"
    const val DETAIL = "detail"
}

object Argument {
    const val USERNAME = "username"
}