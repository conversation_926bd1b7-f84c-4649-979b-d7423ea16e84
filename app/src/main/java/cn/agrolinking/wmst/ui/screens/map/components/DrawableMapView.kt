package cn.agrolinking.wmst.ui.screens.map.components

import android.content.Context
import android.util.Log
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import cn.agrolinking.wmst.domain.LandPlot
import cn.agrolinking.wmst.map.drawing.DrawingMode
import cn.agrolinking.wmst.map.drawing.DrawingModeManager
import cn.agrolinking.wmst.map.manager.LayerCompositeManager
import cn.agrolinking.wmst.ui.screens.map.LocationManager
import cn.agrolinking.wmst.ui.screens.map.components.LayerConfiguration
import org.maplibre.android.camera.CameraPosition
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.location.LocationComponentActivationOptions
import org.maplibre.android.location.modes.RenderMode
import org.maplibre.android.location.permissions.PermissionsManager
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.Style
import org.maplibre.android.plugins.scalebar.ScaleBarOptions
import org.maplibre.android.plugins.scalebar.ScaleBarPlugin
import timber.log.Timber

private const val TAG = "DrawableMapView"

@Composable
fun DrawableMapView(
    modifier: Modifier = Modifier,
    initialLatitude: Double = 37.60,
    initialLongitude: Double = 112.35,
    initialZoom: Double = 12.0,
    layerConfiguration: LayerConfiguration?,
    layerCompositeManager: LayerCompositeManager?,
    locationManager: LocationManager?,
    drawingMode: DrawingMode = DrawingMode.NONE,
    onDrawingCompleted: ((LandPlot) -> Unit)? = null,
    onDrawingCancelled: (() -> Unit)? = null,
    onMapPositionChanged: ((Double, Double, Double) -> Unit)? = null,
    onDrawingModeManagerReady: ((DrawingModeManager) -> Unit)? = null
) {
    val context = LocalContext.current
    
    // 创建MapView实例
    val mapView = remember { MapView(context) }
    
    // 绘制模式管理器状态
    var drawingModeManager by remember { mutableStateOf<DrawingModeManager?>(null) }
    
    // 初始化地图
    LaunchedEffect(mapView) {
        mapView.getMapAsync { map ->
            map.setStyle(Style.Builder()) { style ->
                Timber.i("地图样式已加载，开始初始化绘制功能...")
                
                // 确保Style完全加载后再进行任何操作
                if (!style.isFullyLoaded) {
                    Timber.w("Style未完全加载，等待...")
                    return@setStyle
                }

                // 初始化图层管理器
                layerCompositeManager?.initialize(map, style)
                
                // 创建绘制模式管理器
                val manager = DrawingModeManager(mapView, map)
                drawingModeManager = manager
                onDrawingModeManagerReady?.invoke(manager)
                
                // 设置绘制回调
                setupDrawingCallbacks(manager, onDrawingCompleted, onDrawingCancelled)
                
                // 设置地图UI控件
                setupMapUI(map)
                
                // 安全初始化LocationComponent
                initializeLocationComponentSafely(context, map, style, locationManager)

                // 添加比例尺
                val scaleBarPlugin = ScaleBarPlugin(mapView, map)
                scaleBarPlugin.create(ScaleBarOptions(context))

                // 设置初始相机位置
                map.cameraPosition = CameraPosition.Builder()
                    .target(LatLng(initialLatitude, initialLongitude))
                    .zoom(initialZoom)
                    .build()

                // 添加地图移动监听器
                onMapPositionChanged?.let { callback ->
                    map.addOnCameraIdleListener {
                        val cameraPosition = map.cameraPosition
                        val target = cameraPosition.target
                        if (target != null) {
                            callback(target.latitude, target.longitude, cameraPosition.zoom)
                        }
                    }
                }

                Timber.i("地图初始化完成，绘制功能已启用")
            }
        }
    }

    // 监听绘制模式变化
    LaunchedEffect(drawingMode, drawingModeManager) {
        drawingModeManager?.let { manager ->
            mapView.getMapAsync { map ->
                val style = map.style
                if (style != null && style.isFullyLoaded) {
                    manager.setDrawingMode(drawingMode, style)
                    Timber.d("绘制模式已更新: $drawingMode")
                }
            }
        }
    }

    // 监听图层配置变化
    LaunchedEffect(layerConfiguration, layerCompositeManager) {
        if (layerConfiguration == null || layerCompositeManager == null) return@LaunchedEffect

        mapView.getMapAsync { map ->
            val style = map.style
            if (style != null && style.isFullyLoaded) {
                layerCompositeManager.updateLayers(layerConfiguration)
            }
        }
    }

    AndroidView(
        factory = { mapView },
        modifier = modifier
    ) { view ->
        view.onResume()
    }

    DisposableEffect(mapView) {
        onDispose {
            // 清理绘制模式管理器
            drawingModeManager?.cleanup()
            drawingModeManager = null
            
            // 安全清理LocationComponent
            try {
                if (PermissionsManager.areLocationPermissionsGranted(context)) {
                    mapView.getMapAsync { map ->
                        val locationComponent = map.locationComponent
                        if (locationComponent.isLocationComponentActivated) {
                            locationComponent.isLocationComponentEnabled = false
                        }
                    }
                }
            } catch (e: Exception) {
                Timber.w(e, "清理LocationComponent时发生异常")
            }
            
            mapView.onPause()
            mapView.onDestroy()
        }
    }
}

/**
 * 设置绘制回调
 */
private fun setupDrawingCallbacks(
    manager: DrawingModeManager,
    onDrawingCompleted: ((LandPlot) -> Unit)?,
    onDrawingCancelled: (() -> Unit)?
) {
    manager.addModeChangeListener { mode ->
        val currentHandler = manager.getCurrentHandler()
        currentHandler?.onDrawingCompleted = onDrawingCompleted
        currentHandler?.onDrawingCancelled = onDrawingCancelled
        
        Timber.d("绘制模式回调已设置: $mode")
    }
}

/**
 * 设置地图UI控件
 */
private fun setupMapUI(map: MapLibreMap) {
    val uiSettings = map.uiSettings
    uiSettings.isCompassEnabled = true
    uiSettings.setCompassGravity(android.view.Gravity.TOP or android.view.Gravity.END)
    uiSettings.setCompassMargins(16, 80, 16, 16)
    
    uiSettings.isAttributionEnabled = true
    uiSettings.setAttributionGravity(android.view.Gravity.BOTTOM or android.view.Gravity.END)
    uiSettings.setAttributionMargins(16, 16, 120, 16)
}

/**
 * 安全地初始化LocationComponent
 */
private fun initializeLocationComponentSafely(
    context: Context,
    map: MapLibreMap,
    style: Style,
    locationManager: LocationManager?
) {
    try {
        if (!PermissionsManager.areLocationPermissionsGranted(context)) {
            Timber.w("位置权限未授予")
            return
        }
        
        if (!style.isFullyLoaded) {
            Timber.w("Style未完全加载，跳过LocationComponent初始化")
            return
        }
        
        val locationComponent = map.locationComponent
        
        if (locationComponent.isLocationComponentActivated) {
            Timber.i("LocationComponent已激活，跳过重复初始化")
            return
        }
        
        val activationOptions = LocationComponentActivationOptions.builder(context, style)
            .useDefaultLocationEngine(true)
            .build()
        
        locationComponent.activateLocationComponent(activationOptions)
        locationComponent.isLocationComponentEnabled = true
        locationComponent.renderMode = RenderMode.NORMAL
        
        locationManager?.initialize(map, locationComponent)
        
        Timber.i("LocationComponent 已安全激活")
        
    } catch (e: Exception) {
        Timber.e(e, "LocationComponent初始化失败")
    }
} 