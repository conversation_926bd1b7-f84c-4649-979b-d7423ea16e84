package cn.agrolinking.wmst.ui.screens.map.components

import android.content.Context
import android.graphics.PointF
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.MotionEvent
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import cn.agrolinking.wmst.domain.LandPlot
import cn.agrolinking.wmst.domain.drawing.LandPlotInProgress
import cn.agrolinking.wmst.map.interaction.DragInteractionManager
import cn.agrolinking.wmst.map.manager.LayerCompositeManager
import cn.agrolinking.wmst.map.manager.TileSourceManager
import cn.agrolinking.wmst.map.model.LayerType
import cn.agrolinking.wmst.map.model.TileSource
import cn.agrolinking.wmst.map.drawing.DrawingMode
import cn.agrolinking.wmst.map.drawing.DrawingModeManager
import cn.agrolinking.wmst.map.util.GeometryUtils
import cn.agrolinking.wmst.ui.screens.map.LocationManager
import cn.agrolinking.wmst.ui.screens.map.MapUiState
import cn.agrolinking.wmst.ui.screens.map.MapEvent
import org.maplibre.android.MapLibre
import org.maplibre.android.camera.CameraPosition
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.location.LocationComponent
import org.maplibre.android.location.LocationComponentActivationOptions
import org.maplibre.android.location.LocationComponentOptions
import org.maplibre.android.location.modes.CameraMode
import org.maplibre.android.location.modes.RenderMode
import org.maplibre.android.location.permissions.PermissionsManager
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.OnMapReadyCallback
import org.maplibre.android.maps.Style
import org.maplibre.android.plugins.scalebar.ScaleBarOptions
import org.maplibre.android.plugins.scalebar.ScaleBarPlugin
import org.maplibre.android.style.layers.CircleLayer
import org.maplibre.android.style.layers.FillLayer
import org.maplibre.android.style.layers.LineLayer
import org.maplibre.android.style.layers.PropertyFactory.*
import org.maplibre.android.style.expressions.Expression.*
import org.maplibre.android.style.layers.RasterLayer
import org.maplibre.android.style.sources.GeoJsonSource
import org.maplibre.android.style.sources.RasterSource
import org.maplibre.android.style.sources.TileSet
import org.maplibre.geojson.Feature
import org.maplibre.geojson.FeatureCollection
import org.maplibre.geojson.LineString
import org.maplibre.geojson.Point
import org.maplibre.geojson.Polygon
import org.maplibre.android.plugins.annotation.OnCircleClickListener
import org.maplibre.android.plugins.annotation.CircleOptions
import org.maplibre.android.plugins.annotation.CircleManager
import org.maplibre.android.plugins.annotation.FillOptions
import org.maplibre.android.plugins.annotation.FillManager
import org.maplibre.android.plugins.annotation.LineOptions
import org.maplibre.android.plugins.annotation.LineManager

private const val TAG = "MapViewComponent"

/**
 * 多图层配置
 */
data class LayerConfiguration(
    val baseLayerSourceId: String? = null,
    val overlayLayers: List<OverlayLayerConfig> = emptyList(),
    val enableSatelliteWithRoads: Boolean = false
)

data class OverlayLayerConfig(
    val sourceId: String,
    val layerType: LayerType,
    val opacity: Float = 0.8f,
    val isVisible: Boolean = true
)

// 🎯 统一事件分发器 - 负责向后兼容和新事件系统的桥接
internal class MapEventDispatcher(
    private val onMapEvent: ((MapEvent) -> Unit)?,
    private val onMapPositionChanged: ((Double, Double, Double) -> Unit)?,
    private val onDrawingCompleted: ((LandPlot) -> Unit)?,
    private val onMapClick: ((LatLng) -> Unit)?,
    private val onDragStart: ((Int, LatLng) -> Unit)?,
    private val onDragUpdate: ((LatLng) -> Unit)?,
    private val onDragEnd: (() -> Unit)?,
    private val onMidpointClick: ((Int, LatLng) -> Unit)?,
    private val onLongPress: ((LatLng) -> Unit)?,
    private val onDrawingModeManagerReady: ((DrawingModeManager) -> Unit)?
) {
    fun onMapEvent(event: MapEvent) {
        onMapEvent?.invoke(event)
    }
    
    fun onMapPositionChanged(latitude: Double, longitude: Double, zoom: Double) {
        onMapPositionChanged?.invoke(latitude, longitude, zoom)
        onMapEvent?.invoke(MapEvent.CameraPositionChanged(latitude, longitude, zoom))
    }
    
    fun onDrawingCompleted(landPlot: LandPlot) {
        onDrawingCompleted?.invoke(landPlot)
        onMapEvent?.invoke(MapEvent.DrawingCompleted(landPlot))
    }
    
    fun onMapClick(latLng: LatLng) {
        onMapClick?.invoke(latLng)
        onMapEvent?.invoke(MapEvent.Click(latLng))
    }
    
    fun onDragStart(pointIndex: Int, latLng: LatLng) {
        onDragStart?.invoke(pointIndex, latLng)
        onMapEvent?.invoke(MapEvent.DragStart(pointIndex, latLng))
    }
    
    fun onDragUpdate(latLng: LatLng) {
        onDragUpdate?.invoke(latLng)
        onMapEvent?.invoke(MapEvent.DragUpdate(latLng))
    }
    
    fun onDragEnd() {
        onDragEnd?.invoke()
        onMapEvent?.invoke(MapEvent.DragEnd)
    }
    
    fun onMidpointClick(afterIndex: Int, latLng: LatLng) {
        onMidpointClick?.invoke(afterIndex, latLng)
        onMapEvent?.invoke(MapEvent.MidpointClick(afterIndex, latLng))
    }
    
    fun onLongPress(latLng: LatLng) {
        onLongPress?.invoke(latLng)
        onMapEvent?.invoke(MapEvent.LongPress(latLng))
    }
    
    fun onDrawingModeManagerReady(manager: DrawingModeManager) {
        onDrawingModeManagerReady?.invoke(manager)
        onMapEvent?.invoke(MapEvent.DrawingModeManagerReady(manager))
    }
}

@Composable
fun MapView(
    modifier: Modifier = Modifier,
    // 地图基础配置
    initialLatitude: Double = 43.8371,
    initialLongitude: Double = 125.8906,
    initialZoom: Double = 15.0,
    layerConfiguration: LayerConfiguration? = null,
    layerCompositeManager: LayerCompositeManager? = null,
    locationManager: LocationManager? = null,
    
    // 🔥 统一状态管理（新版本 - 推荐使用）
    mapUiState: MapUiState? = null,
    onMapEvent: ((MapEvent) -> Unit)? = null,
    
    // 🔄 向后兼容参数（保留以支持现有代码）
    // 注意：推荐使用 mapUiState.drawingState 替代以下参数
    isDrawingMode: Boolean = false,
    drawingMode: DrawingMode = DrawingMode.NONE, 
    drawingPoints: List<LatLng> = emptyList(),
    isDragMode: Boolean = false,
    showMidpoints: Boolean = false,
    isDragging: Boolean = false,
    
    // 🔄 向后兼容回调（保留以支持现有代码）
    // 注意：推荐使用 onMapEvent 替代以下回调
    onMapPositionChanged: ((Double, Double, Double) -> Unit)? = null,
    onDrawingCompleted: ((LandPlot) -> Unit)? = null,
    onDrawingModeManagerReady: ((DrawingModeManager) -> Unit)? = null,
    onMapClick: ((LatLng) -> Unit)? = null,
    onDragStart: ((Int, LatLng) -> Unit)? = null,
    onDragUpdate: ((LatLng) -> Unit)? = null,
    onDragEnd: (() -> Unit)? = null,
    onMidpointClick: ((Int, LatLng) -> Unit)? = null,
    onLongPress: ((LatLng) -> Unit)? = null
) {
    val context = LocalContext.current
    
    // 使用基本的remember创建MapView，避免复杂的生命周期管理
    val mapView = remember { MapView(context) }
    
    // 绘制模式管理器状态
    var drawingModeManager by remember { mutableStateOf<DrawingModeManager?>(null) }
    
    // 拖拽交互管理器状态
    var dragInteractionManager by remember { mutableStateOf<DragInteractionManager?>(null) }
    
    // 🔥 修复拖拽状态管理 - 将状态提升到Compose层面，避免函数重新调用时重置
    val isDraggingState = remember { mutableStateOf(false) }
    val currentDragTargetState = remember { mutableStateOf<DragInteractionManager.DragTarget?>(null) }

    // 🔥 统一状态解析（优先使用新状态系统，向后兼容旧参数）
    val resolvedDrawingState = mapUiState?.drawingState
    val resolvedViewingState = mapUiState?.viewingState
    val resolvedEditingState = mapUiState?.editingState
    
    // 解析实际使用的状态值（新状态优先，旧参数兜底）
    val actualDrawingState = resolvedDrawingState
    val actualDrawingPoints = resolvedDrawingState?.currentPoints ?: drawingPoints
    val actualIsDrawingMode = resolvedDrawingState?.isActive ?: isDrawingMode
    val actualDrawingMode = resolvedDrawingState?.mode ?: drawingMode
    val actualIsDragMode = resolvedDrawingState?.isDragMode ?: isDragMode
    val actualShowMidpoints = resolvedDrawingState?.showMidpoints ?: showMidpoints
    val actualIsDragging = resolvedDrawingState?.isDragging ?: isDragging
    val currentLandPlot = resolvedDrawingState?.workingLandPlot
    
    // 🎯 统一的事件分发器
    val eventDispatcher = remember(onMapEvent) { 
        MapEventDispatcher(onMapEvent, onMapPositionChanged, onDrawingCompleted, 
                          onMapClick, onDragStart, onDragUpdate, onDragEnd, 
                          onMidpointClick, onLongPress, onDrawingModeManagerReady)
    }

    LaunchedEffect(mapView) {
        mapView.getMapAsync { map ->
            map.setStyle(Style.Builder()) { style ->
                Log.i(TAG, "地图样式已加载，开始初始化...")
                
                // 确保Style完全加载后再进行任何操作
                if (!style.isFullyLoaded) {
                    Log.w(TAG, "Style未完全加载，等待...")
                    return@setStyle
                }

                // 初始化图层管理器
                layerCompositeManager?.initialize(map, style)
                
                // 创建绘制模式管理器
                val manager = DrawingModeManager(mapView, map)
                drawingModeManager = manager
                eventDispatcher.onDrawingModeManagerReady(manager)
                
                // 创建拖拽交互管理器
                val dragManager = DragInteractionManager(map)
                dragInteractionManager = dragManager
                
                // 设置拖拽预览回调
                dragManager.onDragPreviewUpdate = { preview ->
                    // 渲染拖拽预览
                    renderDragPreview(map, style, preview)
                    // 触发统一事件
                    eventDispatcher.onMapEvent(MapEvent.DragPreview(preview))
                }
                
                // 设置绘制回调
                setupDrawingCallbacks(manager, eventDispatcher::onDrawingCompleted)
                
                // 设置地图UI控件
                val uiSettings = map.uiSettings
                uiSettings.isCompassEnabled = true
                uiSettings.setCompassGravity(android.view.Gravity.TOP or android.view.Gravity.END)
                uiSettings.setCompassMargins(16, 80, 16, 16)
                
                uiSettings.isAttributionEnabled = true
                uiSettings.setAttributionGravity(android.view.Gravity.BOTTOM or android.view.Gravity.END)
                uiSettings.setAttributionMargins(16, 16, 120, 16)

                // 安全初始化LocationComponent
                initializeLocationComponentSafely(context, map, style, locationManager)

                // 添加比例尺
                val scaleBarPlugin = ScaleBarPlugin(mapView, map)
                scaleBarPlugin.create(ScaleBarOptions(context))

                // 设置初始相机位置
                map.cameraPosition = CameraPosition.Builder()
                    .target(LatLng(initialLatitude, initialLongitude))
                    .zoom(initialZoom)
                    .build()

                // 添加地图移动监听器
                map.addOnCameraIdleListener {
                    val cameraPosition = map.cameraPosition
                    val target = cameraPosition.target
                    if (target != null) {
                        eventDispatcher.onMapPositionChanged(target.latitude, target.longitude, cameraPosition.zoom)
                    }
                }

                Log.i(TAG, "地图初始化完成")
            }
        }
    }

    // 监听图层配置变化
    LaunchedEffect(layerConfiguration, layerCompositeManager) {
        if (layerConfiguration == null || layerCompositeManager == null) return@LaunchedEffect

        mapView.getMapAsync { map ->
            // 确保样式已经完全加载后再更新图层
            val style = map.style
            if (style != null && style.isFullyLoaded) {
                layerCompositeManager.updateLayers(layerConfiguration)
            }
        }
    }
    
    // 监听绘制模式变化
    LaunchedEffect(actualDrawingMode, drawingModeManager) {
        drawingModeManager?.let { manager ->
            mapView.getMapAsync { map ->
                val style = map.style
                if (style != null && style.isFullyLoaded) {
                    manager.setDrawingMode(actualDrawingMode, style)
                    Log.d(TAG, "绘制模式已更新: $actualDrawingMode")
                }
            }
        }
    }
    
    // 🔥 使用单独的标志来跟踪监听器是否已设置
    var listenersInitialized by remember { mutableStateOf(false) }
    
    // 监听交互状态变化并设置监听器
    // 🔥 只在必要时设置监听器，避免拖拽时重新设置
    LaunchedEffect(dragInteractionManager) {
        dragInteractionManager?.let { manager ->
            mapView.getMapAsync { map ->
                val style = map.style
                if (style != null && style.isFullyLoaded && !listenersInitialized) {
                    // 一次性设置统一的地图交互监听器
                    setupUnifiedMapInteractionListeners(
                        mapView,
                        map, 
                        manager,
                        // 🔥 传递状态获取函数而不是静态值
                        { actualIsDragMode },
                        { actualDrawingPoints },
                        { currentLandPlot },
                        onMapClick,
                        onMapEvent,
                        onDragStart,
                        onDragUpdate,
                        onDragEnd,
                        onMidpointClick,
                        onLongPress,
                        isDraggingState,
                        currentDragTargetState
                    )
                    listenersInitialized = true
                    Log.d(TAG, "统一交互监听器初始化完成")
                }
            }
        }
    }
    
    // 🔥 监听拖拽模式变化，动态更新地图手势设置
    LaunchedEffect(actualIsDragMode) {
        mapView.getMapAsync { map ->
            if (actualIsDragMode) {
                // 进入拖拽模式时，预先设置地图手势为更敏感的状态
                // 但不完全禁用，因为用户可能需要缩放查看
                map.uiSettings.apply {
                    // 保持缩放功能，但降低滚动敏感度
                    isScrollGesturesEnabled = true
                    isZoomGesturesEnabled = true
                    isRotateGesturesEnabled = false // 禁用旋转避免冲突
                    isTiltGesturesEnabled = false   // 禁用倾斜避免冲突
                }
                Log.d(TAG, "进入拖拽模式，调整地图手势设置")
            } else {
                // 退出拖拽模式时，恢复所有地图手势
                map.uiSettings.apply {
                    isScrollGesturesEnabled = true
                    isZoomGesturesEnabled = true
                    isRotateGesturesEnabled = true
                    isTiltGesturesEnabled = true
                }
                Log.d(TAG, "退出拖拽模式，恢复地图手势设置")
            }
        }
    }
    
    // 监听绘制点变化并渲染
    LaunchedEffect(
        actualDrawingPoints, 
        actualShowMidpoints, 
        actualIsDragMode, 
        actualIsDrawingMode,
        currentLandPlot
    ) {
        mapView.getMapAsync { map ->
            val style = map.style
            if (style != null && style.isFullyLoaded) {
                if (currentLandPlot != null) {
                    // 渲染复杂几何体
                    renderComplexLandPlot(
                        map, 
                        style, 
                        currentLandPlot, 
                        actualShowMidpoints, 
                        actualIsDragMode
                    )
                    Log.d(TAG, "已渲染复杂地块")
                } else if (actualIsDragMode) {
                    // 向后兼容：渲染简单多边形的拖拽模式
                    renderEnhancedDrawingPoints(
                        map, 
                        style, 
                        actualDrawingPoints, 
                        actualShowMidpoints, 
                        actualIsDragMode
                    )
                    Log.d(TAG, "已渲染拖拽模式: ${actualDrawingPoints.size} 个绘制点")
                } else if (actualIsDrawingMode || actualDrawingPoints.isNotEmpty()) {
                    // 向后兼容：渲染简单多边形的绘制模式
                    renderDrawingPoints(map, style, actualDrawingPoints)
                    Log.d(TAG, "已渲染绘制模式: ${actualDrawingPoints.size} 个绘制点")
                } else {
                    // 清空渲染
                                          clearAllDrawingLayers(style)
                    Log.d(TAG, "清空绘制渲染")
                }
            }
        }
    }

    AndroidView(
        factory = { mapView },
        modifier = modifier
    ) { view ->
        // 在这里处理MapView的生命周期
        view.onResume()
    }

    DisposableEffect(mapView) {
        onDispose {
            // 清理绘制模式管理器
            drawingModeManager?.cleanup()
            drawingModeManager = null
            
            // 清理拖拽交互管理器
            dragInteractionManager = null
            
            // 安全清理LocationComponent
            try {
                if (PermissionsManager.areLocationPermissionsGranted(context)) {
                    mapView.getMapAsync { map ->
                        val locationComponent = map.locationComponent
                        if (locationComponent.isLocationComponentActivated) {
                            locationComponent.isLocationComponentEnabled = false
                        }
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG, "清理LocationComponent时发生异常", e)
            }
            
            mapView.onPause()
            mapView.onDestroy()
        }
    }
}

/**
 * 设置绘制回调
 */
private fun setupDrawingCallbacks(
    manager: DrawingModeManager,
    onDrawingCompleted: ((LandPlot) -> Unit)?
) {
    manager.addModeChangeListener { mode ->
        val currentHandler = manager.getCurrentHandler()
        currentHandler?.onDrawingCompleted = onDrawingCompleted
        
        Log.d(TAG, "绘制模式回调已设置: $mode")
    }
}

/**
 * 安全地初始化LocationComponent
 */
private fun initializeLocationComponentSafely(
    context: Context,
    map: MapLibreMap,
    style: Style,
    locationManager: LocationManager?
) {
    try {
        if (!PermissionsManager.areLocationPermissionsGranted(context)) {
            Log.w(TAG, "位置权限未授予")
            return
        }
        
        // 再次确保Style状态
        if (!style.isFullyLoaded) {
            Log.w(TAG, "Style未完全加载，跳过LocationComponent初始化")
            return
        }
        
        val locationComponent = map.locationComponent
        
        // 检查是否已经激活
        if (locationComponent.isLocationComponentActivated) {
            Log.i(TAG, "LocationComponent已激活，跳过重复初始化")
            return
        }
        
        val activationOptions = LocationComponentActivationOptions.builder(context, style)
            .useDefaultLocationEngine(true)
            .build()
        
        locationComponent.activateLocationComponent(activationOptions)
        locationComponent.isLocationComponentEnabled = true
        locationComponent.renderMode = RenderMode.NORMAL
        
        // 初始化LocationManager
        locationManager?.initialize(map, locationComponent)
        
        Log.i(TAG, "LocationComponent 已安全激活")
        
    } catch (e: Exception) {
        Log.e(TAG, "LocationComponent初始化失败", e)
    }
}

/**
 * 图层配置更新扩展函数
 */
fun LayerCompositeManager.updateLayers(configuration: LayerConfiguration) {
    clearAllLayers()
    
    configuration.baseLayerSourceId?.let {
        setBaseLayer(it)
    }
    
    configuration.overlayLayers.forEach {
        addOverlayLayer(it.sourceId, it.layerType, it.opacity)
        if (!it.isVisible) {
            setLayerVisibility(it.layerType, false)
        }
    }
}

/**
 * 多图层地图组件（新接口）
 */
@Composable
fun MultiLayerMapView(
    modifier: Modifier = Modifier,
    initialLatitude: Double = 37.60,
    initialLongitude: Double = 112.35,
    initialZoom: Double = 12.0,
    layerConfiguration: LayerConfiguration,
    layerCompositeManager: LayerCompositeManager
) {
    MapView(
        modifier = modifier,
        initialLatitude = initialLatitude,
        initialLongitude = initialLongitude,
        initialZoom = initialZoom,
        layerConfiguration = layerConfiguration,
        layerCompositeManager = layerCompositeManager,
        locationManager = null
    )
}

/**
 * 设置统一的地图交互监听器
 * 支持简单多边形和复杂几何体(多边形+孔洞)的统一交互处理
 */
private fun setupUnifiedMapInteractionListeners(
    mapView: MapView,
    map: MapLibreMap,
    dragManager: DragInteractionManager,
    // 🔥 使用状态获取函数，确保监听器内部能获取最新状态
    isDragModeProvider: () -> Boolean,
    drawingPointsProvider: () -> List<LatLng>,
    currentLandPlotProvider: () -> LandPlotInProgress?,
    onMapClick: ((LatLng) -> Unit)?,
    onMapEvent: ((MapEvent) -> Unit)?,
    onDragStart: ((Int, LatLng) -> Unit)?,
    onDragUpdate: ((LatLng) -> Unit)?,
    onDragEnd: (() -> Unit)?,
    onMidpointClick: ((Int, LatLng) -> Unit)?,
    onLongPress: ((LatLng) -> Unit)?,
    // 🔥 新增拖拽状态参数，避免状态重置问题
    isDraggingState: androidx.compose.runtime.MutableState<Boolean>,
    currentDragTargetState: androidx.compose.runtime.MutableState<DragInteractionManager.DragTarget?>
) {
    // 🔥 设置统一的地图点击监听器
    map.addOnMapClickListener { latLng ->
        if (isDraggingState.value) return@addOnMapClickListener true
        
        val currentIsDragMode = isDragModeProvider()
        val currentDrawingPoints = drawingPointsProvider()
        val currentLandPlot = currentLandPlotProvider()
        
        // 根据当前模式处理点击事件
        if (!currentIsDragMode) {
            onMapClick?.invoke(latLng)
            onMapEvent?.invoke(MapEvent.Click(latLng))
            Log.d(TAG, "常规地图点击: $latLng")
        } else {
            // 拖拽模式下，检查是否是中点点击（非拖拽操作）
            val touchPoint = map.projection.toScreenLocation(latLng)
            val dragTarget = if (currentLandPlot != null) {
                dragManager.detectDragTarget(touchPoint, currentLandPlot)
            } else {
                dragManager.detectDragTarget(touchPoint, currentDrawingPoints)
            }
            
            if (dragTarget is DragInteractionManager.DragTarget.EdgeMidpoint) {
                // 中点插入
                onMidpointClick?.invoke(dragTarget.edgeStartIndex, latLng)
                onMapEvent?.invoke(MapEvent.MidpointClick(dragTarget.edgeStartIndex, latLng))
                Log.d(TAG, "中点插入: 边${dragTarget.edgeStartIndex}")
            }
        }
        
        true
    }
    
    // 设置触摸监听器处理拖拽移动 - 优先处理拖拽事件
    mapView.setOnTouchListener { _, event ->
        val currentIsDragMode = isDragModeProvider()

        // 添加调试日志
        if (currentIsDragMode && event.action == MotionEvent.ACTION_DOWN) {
            Log.d(TAG, "拖拽模式下收到触摸事件: ACTION_DOWN at (${event.x}, ${event.y})")
        }

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 只在拖拽模式下且未在拖拽时检测拖拽目标
                if (currentIsDragMode && !isDraggingState.value) {
                    val screenPoint = PointF(event.x, event.y)

                    // 获取当前状态
                    val currentDrawingPoints = drawingPointsProvider()
                    val currentLandPlot = currentLandPlotProvider()

                    // 尝试检测拖拽目标
                    val dragTarget = if (currentLandPlot != null) {
                        dragManager.detectDragTarget(screenPoint, currentLandPlot)
                    } else {
                        dragManager.detectDragTarget(screenPoint, currentDrawingPoints)
                    }

                    if (dragTarget is DragInteractionManager.DragTarget.Vertex) {
                        // 转换屏幕坐标到地理坐标
                        val latLng = map.projection.fromScreenLocation(screenPoint)

                        // 开始拖拽顶点
                        isDraggingState.value = true
                        currentDragTargetState.value = dragTarget

                        // 立即禁用地图手势，防止冲突
                        map.uiSettings.apply {
                            isScrollGesturesEnabled = false
                            isZoomGesturesEnabled = false
                            isRotateGesturesEnabled = false
                            isTiltGesturesEnabled = false
                        }

                        // 启动拖拽管理器
                        dragManager.startDrag(dragTarget, latLng, currentLandPlot)

                        // 触发回调事件
                        onDragStart?.invoke(dragTarget.vertexIndex, latLng)
                        onMapEvent?.invoke(MapEvent.DragStart(dragTarget.vertexIndex, latLng))

                        Log.d(TAG, "开始拖拽顶点: ${dragTarget.vertexIndex} at (${"%.6f".format(latLng.latitude)}, ${"%.6f".format(latLng.longitude)})")

                        return@setOnTouchListener true
                    } else {
                        // 在拖拽模式下，即使没有检测到拖拽目标，也要拦截触摸事件
                        // 这样可以防止意外的地图移动
                        Log.d(TAG, "拖拽模式下触摸，但未检测到拖拽目标")
                        return@setOnTouchListener true
                    }
                }
                // 如果不在拖拽模式，让地图处理
                false
            }

            MotionEvent.ACTION_MOVE -> {
                // 只有在拖拽状态下才处理移动事件
                if (isDraggingState.value && currentDragTargetState.value != null) {
                    val screenPoint = PointF(event.x, event.y)
                    val latLng = map.projection.fromScreenLocation(screenPoint)

                    // 更新拖拽管理器的位置
                    dragManager.updateDrag(latLng)

                    // 使用增强的拖拽预览功能
                    val currentLandPlot = currentLandPlotProvider()
                    val preview = dragManager.updateDragPosition(latLng, currentLandPlot)

                    // 触发传统回调（保持兼容性）
                    // 使用预览点而不是原始触摸点，这样可以包含吸附效果
                    val finalPoint = preview?.previewPoint ?: latLng
                    onDragUpdate?.invoke(finalPoint)
                    onMapEvent?.invoke(MapEvent.DragUpdate(finalPoint))

                    Log.v(TAG, "拖拽移动到: (${"%.6f".format(finalPoint.latitude)}, ${"%.6f".format(finalPoint.longitude)})")

                    // 拖拽时阻止地图滚动
                    return@setOnTouchListener true
                }
                // 如果不在拖拽状态，让地图处理
                false
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isDraggingState.value) {
                    val wasCancel = event.action == MotionEvent.ACTION_CANCEL

                    // 获取最终位置（如果不是取消操作）
                    val finalPoint = if (!wasCancel) {
                        val screenPoint = PointF(event.x, event.y)
                        map.projection.fromScreenLocation(screenPoint)
                    } else {
                        null
                    }

                    // 清理拖拽状态
                    isDraggingState.value = false
                    currentDragTargetState.value = null

                    // 重新启用地图手势
                    map.uiSettings.apply {
                        isScrollGesturesEnabled = true
                        isZoomGesturesEnabled = true
                        isRotateGesturesEnabled = true
                        isTiltGesturesEnabled = true
                    }

                    // 结束拖拽
                    val dragFinalPoint = dragManager.endDrag()

                    // 触发结束回调
                    onDragEnd?.invoke()
                    onMapEvent?.invoke(MapEvent.DragEnd)

                    Log.d(TAG, "拖拽结束${if (wasCancel) "（取消）" else ""}，最终位置: ${finalPoint ?: dragFinalPoint}")

                    return@setOnTouchListener true
                }
                // 如果不在拖拽状态，让地图处理
                false
            }

            else -> {
                // 其他触摸事件让地图处理
                false
            }
        }
    }
    
    // 设置长按监听器
    map.addOnMapLongClickListener { latLng ->
        if (!isDraggingState.value) {
            onLongPress?.invoke(latLng)
            onMapEvent?.invoke(MapEvent.LongPress(latLng))
        }
        true
    }
}

/**
 * 渲染复杂地块（支持多边形+孔洞）
 */
private fun renderComplexLandPlot(
    map: MapLibreMap,
    style: Style,
    landPlot: LandPlotInProgress,
    showMidpoints: Boolean,
    isDragMode: Boolean
) {
    // 确保样式已加载
    if (!style.isFullyLoaded) {
        Log.w(TAG, "样式未完全加载，跳过复杂地块渲染")
        return
    }
    
    try {
        // 🔥 首先渲染正在绘制的基础点（解决单个点不显示的问题）
        val currentDrawingPoints = landPlot.getCurrentRing()
        if (currentDrawingPoints.isNotEmpty()) {
            renderDrawingPoints(map, style, currentDrawingPoints)
            Log.d(TAG, "已渲染基础绘制点: ${currentDrawingPoints.size}个点")
        }
        
        // 1. 渲染所有完整的多边形（>=3个点的）
        val polygonFeatures = mutableListOf<Feature>()
        landPlot.polygons.forEachIndexed { polygonIndex, polygon ->
            // 外边界
            val exteriorCoords = polygon.exterior.map { Point.fromLngLat(it.longitude, it.latitude) }
            
            // 孔洞
            val holeCoords = polygon.holes.map { hole ->
                hole.map { Point.fromLngLat(it.longitude, it.latitude) }
            }
            
            // 构建完整的多边形坐标（外边界 + 孔洞）
            val allCoords = mutableListOf<List<Point>>().apply {
                add(exteriorCoords)
                addAll(holeCoords)
            }
            
            val polygonGeometry = Polygon.fromLngLats(allCoords)
            val feature = Feature.fromGeometry(polygonGeometry)
            feature.addStringProperty("polygon_index", polygonIndex.toString())
            polygonFeatures.add(feature)
        }
        
        // 更新多边形数据源
        val polygonSource = style.getSourceAs<GeoJsonSource>("complex-polygon-source")
            ?: GeoJsonSource("complex-polygon-source").also { style.addSource(it) }
        polygonSource.setGeoJson(FeatureCollection.fromFeatures(polygonFeatures))
        
        // 添加多边形填充图层
        if (style.getLayer("complex-polygon-fill") == null) {
            val fillLayer = FillLayer("complex-polygon-fill", "complex-polygon-source")
            fillLayer.setProperties(
                fillColor("#4CAF50"),
                fillOpacity(0.3f),
                fillOutlineColor("#2E7D32")
            )
            style.addLayer(fillLayer)
        }
        
        // 添加多边形边框图层
        if (style.getLayer("complex-polygon-stroke") == null) {
            val strokeLayer = LineLayer("complex-polygon-stroke", "complex-polygon-source")
            strokeLayer.setProperties(
                lineColor("#2E7D32"),
                lineWidth(2f),
                lineOpacity(0.8f)
            )
            style.addLayer(strokeLayer)
        }
        
        if (isDragMode) {
            // 2. 渲染所有顶点
            renderComplexVertices(map, style, landPlot)
            
            // 3. 渲染所有中点（如果需要）
            if (showMidpoints) {
                renderComplexMidpoints(map, style, landPlot)
            }
        } else {
            // 清理顶点和中点图层
            clearVerticesAndMidpoints(style)
        }
        
        Log.d(TAG, "复杂地块渲染完成: ${landPlot.polygons.size}个多边形")
        
    } catch (e: Exception) {
        Log.e(TAG, "渲染复杂地块失败", e)
    }
}

/**
 * 渲染复杂几何体的所有顶点
 */
private fun renderComplexVertices(
    map: MapLibreMap,
    style: Style,
    landPlot: LandPlotInProgress
) {
    val vertexFeatures = mutableListOf<Feature>()
    
    landPlot.polygons.forEachIndexed { polygonIndex, polygon ->
        // 外边界顶点
        polygon.exterior.forEachIndexed { vertexIndex, point ->
            val feature = Feature.fromGeometry(Point.fromLngLat(point.longitude, point.latitude))
            feature.addStringProperty("type", "vertex")
            feature.addStringProperty("polygon_index", polygonIndex.toString())
            feature.addStringProperty("ring_index", "0") // 外边界
            feature.addStringProperty("vertex_index", vertexIndex.toString())
            vertexFeatures.add(feature)
        }
        
        // 孔洞顶点
        polygon.holes.forEachIndexed { ringIndex, hole ->
            hole.forEachIndexed { vertexIndex, point ->
                val feature = Feature.fromGeometry(Point.fromLngLat(point.longitude, point.latitude))
                feature.addStringProperty("type", "hole_vertex")
                feature.addStringProperty("polygon_index", polygonIndex.toString())
                feature.addStringProperty("ring_index", (ringIndex + 1).toString()) // 孔洞从1开始
                feature.addStringProperty("vertex_index", vertexIndex.toString())
                vertexFeatures.add(feature)
            }
        }
    }
    
    // 更新顶点数据源
    val vertexSource = style.getSourceAs<GeoJsonSource>("complex-vertices-source")
        ?: GeoJsonSource("complex-vertices-source").also { style.addSource(it) }
    vertexSource.setGeoJson(FeatureCollection.fromFeatures(vertexFeatures))
    
    // 添加顶点图层
    if (style.getLayer("complex-vertices") == null) {
        val vertexLayer = CircleLayer("complex-vertices", "complex-vertices-source")
        vertexLayer.setProperties(
            circleRadius(8f), // 增大顶点半径，更容易点击
            circleColor("#FF5722"),
            circleStrokeWidth(3f), // 增大边框宽度
            circleStrokeColor("#FFFFFF")
        )
        style.addLayer(vertexLayer)
    }

    Log.d(TAG, "复杂顶点渲染完成: ${vertexFeatures.size}个顶点")
}

/**
 * 渲染复杂几何体的所有中点
 */
private fun renderComplexMidpoints(
    map: MapLibreMap,
    style: Style,
    landPlot: LandPlotInProgress
) {
    val midpointFeatures = mutableListOf<Feature>()
    
    landPlot.polygons.forEachIndexed { polygonIndex, polygon ->
        // 外边界中点
        for (i in 0 until polygon.exterior.size) {
            val nextIndex = (i + 1) % polygon.exterior.size
            val current = polygon.exterior[i]
            val next = polygon.exterior[nextIndex]
            
            val midpoint = LatLng(
                (current.latitude + next.latitude) / 2,
                (current.longitude + next.longitude) / 2
            )
            
            val feature = Feature.fromGeometry(Point.fromLngLat(midpoint.longitude, midpoint.latitude))
            feature.addStringProperty("type", "midpoint")
            feature.addStringProperty("polygon_index", polygonIndex.toString())
            feature.addStringProperty("ring_index", "0")
            feature.addStringProperty("edge_index", i.toString())
            midpointFeatures.add(feature)
        }
        
        // 孔洞中点
        polygon.holes.forEachIndexed { ringIndex, hole ->
            for (i in 0 until hole.size) {
                val nextIndex = (i + 1) % hole.size
                val current = hole[i]
                val next = hole[nextIndex]
                
                val midpoint = LatLng(
                    (current.latitude + next.latitude) / 2,
                    (current.longitude + next.longitude) / 2
                )
                
                val feature = Feature.fromGeometry(Point.fromLngLat(midpoint.longitude, midpoint.latitude))
                feature.addStringProperty("type", "hole_midpoint")
                feature.addStringProperty("polygon_index", polygonIndex.toString())
                feature.addStringProperty("ring_index", (ringIndex + 1).toString())
                feature.addStringProperty("edge_index", i.toString())
                midpointFeatures.add(feature)
            }
        }
    }
    
    // 更新中点数据源
    val midpointSource = style.getSourceAs<GeoJsonSource>("complex-midpoints-source")
        ?: GeoJsonSource("complex-midpoints-source").also { style.addSource(it) }
    midpointSource.setGeoJson(FeatureCollection.fromFeatures(midpointFeatures))
    
    // 添加中点图层
    if (style.getLayer("complex-midpoints") == null) {
        val midpointLayer = CircleLayer("complex-midpoints", "complex-midpoints-source")
        midpointLayer.setProperties(
            circleRadius(4f),
            circleColor("#2196F3"),
            circleStrokeWidth(1f),
            circleStrokeColor("#FFFFFF"),
            circleOpacity(0.8f)
        )
        style.addLayer(midpointLayer)
    }
}

/**
 * 渲染简单多边形的增强版（支持拖拽模式）
 * 向后兼容函数
 */
private fun renderEnhancedDrawingPoints(
    map: MapLibreMap,
    style: Style,
    points: List<LatLng>,
    showMidpoints: Boolean,
    isDragMode: Boolean
) {
    if (points.isEmpty()) {
        clearAllDrawingLayers(style)
        return
    }
    
    try {
        // 渲染基础多边形
        renderDrawingPoints(map, style, points)
        
        if (isDragMode) {
            // 渲染顶点（传入拖拽目标索引以高亮显示）
            renderSimpleVertices(map, style, points)
            
            // 渲染中点
            if (showMidpoints) {
                renderSimpleMidpoints(map, style, points)
            }
        } else {
            // 清理顶点和中点
            clearVerticesAndMidpoints(style)
        }
        
    } catch (e: Exception) {
        Log.e(TAG, "渲染增强绘制点失败", e)
    }
}

/**
 * 渲染简单多边形的顶点（支持高亮选中顶点）
 */
private fun renderSimpleVertices(
    map: MapLibreMap,
    style: Style,
    points: List<LatLng>
) {
    val vertexFeatures = points.mapIndexed { index, point ->
        val feature = Feature.fromGeometry(Point.fromLngLat(point.longitude, point.latitude))
        feature.addStringProperty("type", "vertex")
        feature.addStringProperty("vertex_index", index.toString())
        feature
    }
    
    val vertexSource = style.getSourceAs<GeoJsonSource>("simple-vertices-source")
        ?: GeoJsonSource("simple-vertices-source").also { style.addSource(it) }
    vertexSource.setGeoJson(FeatureCollection.fromFeatures(vertexFeatures))
    
    if (style.getLayer("simple-vertices") == null) {
        val vertexLayer = CircleLayer("simple-vertices", "simple-vertices-source")
        vertexLayer.setProperties(
            circleRadius(8f), // 拖拽模式下顶点更大，更易点击
            circleColor("#FF5722"),
            circleStrokeWidth(3f), // 更粗的边框，便于识别
            circleStrokeColor("#FFFFFF")
        )
        style.addLayer(vertexLayer)
    }

    Log.d(TAG, "简单顶点渲染完成: ${vertexFeatures.size}个顶点")
}

/**
 * 渲染简单多边形的中点
 */
private fun renderSimpleMidpoints(
    map: MapLibreMap,
    style: Style,
    points: List<LatLng>
) {
    if (points.size < 2) return
    
    val midpointFeatures = mutableListOf<Feature>()
    for (i in 0 until points.size) {
        val nextIndex = (i + 1) % points.size
        val current = points[i]
        val next = points[nextIndex]
        
        val midpoint = LatLng(
            (current.latitude + next.latitude) / 2,
            (current.longitude + next.longitude) / 2
        )
        
        val feature = Feature.fromGeometry(Point.fromLngLat(midpoint.longitude, midpoint.latitude))
        feature.addStringProperty("type", "midpoint")
        feature.addStringProperty("edge_index", i.toString())
        midpointFeatures.add(feature)
    }
    
    val midpointSource = style.getSourceAs<GeoJsonSource>("simple-midpoints-source")
        ?: GeoJsonSource("simple-midpoints-source").also { style.addSource(it) }
    midpointSource.setGeoJson(FeatureCollection.fromFeatures(midpointFeatures))
    
    if (style.getLayer("simple-midpoints") == null) {
        val midpointLayer = CircleLayer("simple-midpoints", "simple-midpoints-source")
        midpointLayer.setProperties(
            circleRadius(4f),
            circleColor("#2196F3"),
            circleStrokeWidth(1f),
            circleStrokeColor("#FFFFFF"),
            circleOpacity(0.8f)
        )
        style.addLayer(midpointLayer)
    }
}

/**
 * 清理顶点和中点图层
 */
private fun clearVerticesAndMidpoints(style: Style) {
    // 清理复杂几何体图层
    listOf(
        "complex-vertices",
        "complex-midpoints",
        "simple-vertices", 
        "simple-midpoints"
    ).forEach { layerId ->
        style.getLayer(layerId)?.let { 
            style.removeLayer(it) 
        }
    }
    
    // 清理数据源
    listOf(
        "complex-vertices-source",
        "complex-midpoints-source",
        "simple-vertices-source",
        "simple-midpoints-source"
    ).forEach { sourceId ->
        style.getSource(sourceId)?.let { 
            style.removeSource(it) 
        }
    }
}

/**
 * 渲染基础绘制点（简单多边形）
 * 向后兼容函数
 */
private fun renderDrawingPoints(
    map: MapLibreMap,
    style: Style,
    points: List<LatLng>
) {
    if (points.isEmpty()) {
        clearAllDrawingLayers(style)
        return
    }
    
    if (!style.isFullyLoaded) {
        Log.w(TAG, "样式未完全加载，跳过绘制点渲染")
        return
    }
    
    try {
        // 1. 渲染线条（如果有2个或以上点）
        if (points.size >= 2) {
            val linePoints = points.map { Point.fromLngLat(it.longitude, it.latitude) }
            val lineString = LineString.fromLngLats(linePoints)
            val lineFeature = Feature.fromGeometry(lineString)
            
            val lineSource = style.getSourceAs<GeoJsonSource>("drawing-lines-source")
                ?: GeoJsonSource("drawing-lines-source").also { style.addSource(it) }
            lineSource.setGeoJson(lineFeature)
            
            if (style.getLayer("drawing-lines") == null) {
                val lineLayer = LineLayer("drawing-lines", "drawing-lines-source")
                lineLayer.setProperties(
                    lineColor("#2196F3"),
                    lineWidth(3f),
                    lineOpacity(0.8f)
                )
                style.addLayer(lineLayer)
            }
        }
        
        // 2. 渲染多边形（如果有3个或以上点）
        if (points.size >= 3) {
            // 闭合多边形
            val polygonPoints = points.map { Point.fromLngLat(it.longitude, it.latitude) }.toMutableList()
            polygonPoints.add(polygonPoints.first()) // 闭合多边形
            
            val polygon = Polygon.fromLngLats(listOf(polygonPoints))
            val polygonFeature = Feature.fromGeometry(polygon)
            
            val polygonSource = style.getSourceAs<GeoJsonSource>("drawing-polygon-source")
                ?: GeoJsonSource("drawing-polygon-source").also { style.addSource(it) }
            polygonSource.setGeoJson(polygonFeature)
            
            // 填充图层
            if (style.getLayer("drawing-polygon-fill") == null) {
                val fillLayer = FillLayer("drawing-polygon-fill", "drawing-polygon-source")
                fillLayer.setProperties(
                    fillColor("#4CAF50"),
                    fillOpacity(0.3f)
                )
                style.addLayer(fillLayer)
            }
            
            // 边框图层
            if (style.getLayer("drawing-polygon-stroke") == null) {
                val strokeLayer = LineLayer("drawing-polygon-stroke", "drawing-polygon-source")
                strokeLayer.setProperties(
                    lineColor("#2E7D32"),
                    lineWidth(2f),
                    lineOpacity(0.8f)
                )
                style.addLayer(strokeLayer)
            }
        }
        
        // 3. 渲染所有绘制点
        val pointFeatures = points.mapIndexed { index, point ->
            val feature = Feature.fromGeometry(Point.fromLngLat(point.longitude, point.latitude))
            feature.addStringProperty("point_index", index.toString())
            feature
        }
        
        val pointSource = style.getSourceAs<GeoJsonSource>("drawing-points-source")
            ?: GeoJsonSource("drawing-points-source").also { style.addSource(it) }
        
        // 🔥 修复关键bug：设置点数据到数据源
        pointSource.setGeoJson(FeatureCollection.fromFeatures(pointFeatures))
        
        if (style.getLayer("drawing-points") == null) {
            val pointLayer = CircleLayer("drawing-points", "drawing-points-source")
            pointLayer.setProperties(
                circleRadius(5f),
                circleColor("#FF5722"),
                circleStrokeWidth(2f),
                circleStrokeColor("#FFFFFF")
            )
            style.addLayer(pointLayer)
        }
        
        Log.d(TAG, "基础绘制点渲染完成: ${points.size}个点")
        
    } catch (e: Exception) {
        Log.e(TAG, "渲染基础绘制点失败", e)
    }
}

/**
 * 清理所有绘制图层
 */
private fun clearAllDrawingLayers(style: Style) {
    // 移除所有绘制相关的图层
    val layersToRemove = listOf(
        "drawing-points", "drawing-lines", "drawing-polygon-fill", "drawing-polygon-stroke",
        "complex-polygon-fill", "complex-polygon-stroke",
        "complex-vertices", "complex-midpoints",
        "simple-vertices", "simple-midpoints"
    )
    
    layersToRemove.forEach { layerId ->
        style.getLayer(layerId)?.let { layer ->
            style.removeLayer(layer)
            Log.d(TAG, "移除图层: $layerId")
        }
    }
    
    // 移除所有绘制相关的数据源
    val sourcesToRemove = listOf(
        "drawing-points-source", "drawing-lines-source", "drawing-polygon-source",
        "complex-polygon-source", "complex-vertices-source", "complex-midpoints-source",
        "simple-vertices-source", "simple-midpoints-source"
    )
    
    sourcesToRemove.forEach { sourceId ->
        style.getSource(sourceId)?.let { source ->
            style.removeSource(source)
            Log.d(TAG, "移除数据源: $sourceId")
        }
    }
}

/**
 * 渲染拖拽预览
 * 显示拖拽的实时效果，包括吸附指示和几何体验证结果
 */
private fun renderDragPreview(
    map: MapLibreMap,
    style: Style,
    preview: DragInteractionManager.DragPreview?
) {
    if (!style.isFullyLoaded) {
        return
    }
    
    try {
        if (preview == null) {
            // 清除预览渲染
            clearDragPreview(style)
            return
        }
        
        // 1. 渲染拖拽轨迹线（从原始位置到预览位置）
        renderDragTrail(style, preview)
        
        // 2. 渲染预览点和吸附指示
        renderDragPreviewPoint(style, preview)
        
        // 3. 渲染验证状态指示
        renderValidationIndicator(style, preview)
        
    } catch (e: Exception) {
        Log.e(TAG, "渲染拖拽预览失败", e)
    }
}

/**
 * 渲染拖拽轨迹线
 */
private fun renderDragTrail(
    style: Style,
    preview: DragInteractionManager.DragPreview
) {
    val trailPoints = listOf(
        Point.fromLngLat(preview.originalPoint.longitude, preview.originalPoint.latitude),
        Point.fromLngLat(preview.previewPoint.longitude, preview.previewPoint.latitude)
    )
    
    val lineString = LineString.fromLngLats(trailPoints)
    val lineFeature = Feature.fromGeometry(lineString)
    
    val trailSource = style.getSourceAs<GeoJsonSource>("drag-trail-source")
        ?: GeoJsonSource("drag-trail-source").also { style.addSource(it) }
    trailSource.setGeoJson(lineFeature)
    
    if (style.getLayer("drag-trail") == null) {
        val trailLayer = LineLayer("drag-trail", "drag-trail-source")
        trailLayer.setProperties(
            lineColor("#FF9800"),
            lineWidth(2f),
            lineOpacity(0.6f),
            lineDasharray(arrayOf(5f, 3f)) // 虚线效果
        )
        style.addLayer(trailLayer)
    }
}

/**
 * 渲染预览点和吸附指示
 */
private fun renderDragPreviewPoint(
    style: Style,
    preview: DragInteractionManager.DragPreview
) {
    val previewFeature = Feature.fromGeometry(
        Point.fromLngLat(preview.previewPoint.longitude, preview.previewPoint.latitude)
    )
    
    // 根据吸附类型设置不同的视觉样式
    val (color, strokeColor, radius) = when (preview.snapType) {
        is DragInteractionManager.SnapType.Vertex -> Triple("#E91E63", "#FFFFFF", 8f) // 粉色，顶点吸附
        is DragInteractionManager.SnapType.Grid -> Triple("#9C27B0", "#FFFFFF", 6f)   // 紫色，网格吸附
        is DragInteractionManager.SnapType.Angle -> Triple("#3F51B5", "#FFFFFF", 6f)  // 蓝色，角度吸附
        is DragInteractionManager.SnapType.None -> Triple("#FF5722", "#FFFFFF", 6f)   // 橙色，无吸附
    }
    
    previewFeature.addStringProperty("snap_type", preview.snapType::class.simpleName ?: "None")
    
    val previewSource = style.getSourceAs<GeoJsonSource>("drag-preview-source")
        ?: GeoJsonSource("drag-preview-source").also { style.addSource(it) }
    previewSource.setGeoJson(previewFeature)
    
    if (style.getLayer("drag-preview-point") == null) {
        val previewLayer = CircleLayer("drag-preview-point", "drag-preview-source")
        previewLayer.setProperties(
            circleRadius(radius),
            circleColor(color),
            circleStrokeWidth(2f),
            circleStrokeColor(strokeColor),
            circleOpacity(0.9f)
        )
        style.addLayer(previewLayer)
    } else {
        // 更新现有图层的样式
        val previewLayer = style.getLayer("drag-preview-point") as CircleLayer
        previewLayer.setProperties(
            circleRadius(radius),
            circleColor(color),
            circleStrokeColor(strokeColor)
        )
    }
}

/**
 * 渲染几何体验证指示
 */
private fun renderValidationIndicator(
    style: Style,
    preview: DragInteractionManager.DragPreview
) {
    if (!preview.isValidGeometry) {
        // 在预览点周围显示警告环
        val warningFeature = Feature.fromGeometry(
            Point.fromLngLat(preview.previewPoint.longitude, preview.previewPoint.latitude)
        )
        
        val warningSource = style.getSourceAs<GeoJsonSource>("drag-warning-source")
            ?: GeoJsonSource("drag-warning-source").also { style.addSource(it) }
        warningSource.setGeoJson(warningFeature)
        
        if (style.getLayer("drag-warning-ring") == null) {
            val warningLayer = CircleLayer("drag-warning-ring", "drag-warning-source")
            warningLayer.setProperties(
                circleRadius(12f),
                circleColor("#F44336"),
                circleOpacity(0.0f), // 透明填充
                circleStrokeWidth(3f),
                circleStrokeColor("#F44336"),
                circleStrokeOpacity(0.8f)
            )
            style.addLayer(warningLayer)
        }
    } else {
        // 清除警告指示
        style.getLayer("drag-warning-ring")?.let { 
            style.removeLayer(it) 
        }
        style.getSource("drag-warning-source")?.let { 
            style.removeSource(it) 
        }
    }
}

/**
 * 清除拖拽预览渲染
 */
private fun clearDragPreview(style: Style) {
    val previewLayers = listOf(
        "drag-trail",
        "drag-preview-point", 
        "drag-warning-ring"
    )
    
    val previewSources = listOf(
        "drag-trail-source",
        "drag-preview-source",
        "drag-warning-source"
    )
    
    previewLayers.forEach { layerId ->
        style.getLayer(layerId)?.let { 
            style.removeLayer(it) 
        }
    }
    
    previewSources.forEach { sourceId ->
        style.getSource(sourceId)?.let { 
            style.removeSource(it) 
        }
    }
} 