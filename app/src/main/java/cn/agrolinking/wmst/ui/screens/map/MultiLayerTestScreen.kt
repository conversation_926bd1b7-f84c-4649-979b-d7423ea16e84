package cn.agrolinking.wmst.ui.screens.map

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import cn.agrolinking.wmst.map.manager.LayerCompositeManager
import cn.agrolinking.wmst.map.manager.TileSourceManager
import cn.agrolinking.wmst.map.model.LayerType
import cn.agrolinking.wmst.ui.screens.map.components.LayerConfiguration
import cn.agrolinking.wmst.ui.screens.map.components.MultiLayerMapView
import cn.agrolinking.wmst.ui.screens.map.components.OverlayLayerConfig
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 多图层测试屏幕
 * 用于验证LayerCompositeManager的多图层叠加功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MultiLayerTestScreen(
    @Suppress("UNUSED_PARAMETER") tileSourceManager: TileSourceManager,
    layerCompositeManager: LayerCompositeManager
) {
    var currentTest by remember { mutableStateOf("") }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = "多图层叠加测试",
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 测试按钮区域
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .weight(0.3f),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(getTestConfigurations()) { testConfig ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    onClick = {
                        currentTest = testConfig.name
                    }
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = testConfig.name,
                            style = MaterialTheme.typography.titleMedium
                        )
                        Text(
                            text = testConfig.description,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 地图显示区域
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .weight(0.7f)
        ) {
            Box(modifier = Modifier.fillMaxSize()) {
                if (currentTest.isNotEmpty()) {
                    val testConfig = getTestConfigurations().find { it.name == currentTest }
                    testConfig?.let { config ->
                        MultiLayerMapView(
                            modifier = Modifier.fillMaxSize(),
                            layerConfiguration = config.layerConfiguration,
                            layerCompositeManager = layerCompositeManager
                        )
                    }
                } else {
                    // 默认显示
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "选择一个测试配置",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                // 浮动信息卡片
                if (currentTest.isNotEmpty()) {
                    Card(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "当前测试: $currentTest",
                            modifier = Modifier.padding(8.dp),
                            style = MaterialTheme.typography.labelMedium
                        )
                    }
                }
            }
        }
    }
}

/**
 * 测试配置数据类
 */
data class TestConfiguration(
    val name: String,
    val description: String,
    val layerConfiguration: LayerConfiguration
)

/**
 * 获取所有测试配置
 */
private fun getTestConfigurations(): List<TestConfiguration> {
    return listOf(
        TestConfiguration(
            name = "吉林一号卫星图",
            description = "单独显示吉林一号卫星底图",
            layerConfiguration = LayerConfiguration(
                baseLayerSourceId = "jilin1-satellite"
            )
        ),
        
        TestConfiguration(
            name = "天地图影像",
            description = "单独显示天地图卫星影像",
            layerConfiguration = LayerConfiguration(
                baseLayerSourceId = "tianditu-satellite"
            )
        ),
        
        TestConfiguration(
            name = "吉林一号 + 天地图路网",
            description = "吉林一号卫星图叠加天地图路网",
            layerConfiguration = LayerConfiguration(
                baseLayerSourceId = "jilin1-satellite",
                overlayLayers = listOf(
                    OverlayLayerConfig(
                        sourceId = "tianditu-road",
                        layerType = LayerType.ROAD_NETWORK,
                        opacity = 0.7f
                    )
                )
            )
        ),
        
        TestConfiguration(
            name = "天地图影像 + 路网",
            description = "天地图影像底图叠加路网",
            layerConfiguration = LayerConfiguration(
                baseLayerSourceId = "tianditu-satellite",
                overlayLayers = listOf(
                    OverlayLayerConfig(
                        sourceId = "tianditu-road",
                        layerType = LayerType.ROAD_NETWORK,
                        opacity = 0.8f
                    )
                )
            )
        ),
        
        TestConfiguration(
            name = "高德三图层组合",
            description = "高德卫星图 + 高德路网 + 高德标准图",
            layerConfiguration = LayerConfiguration(
                baseLayerSourceId = "amap-satellite",
                overlayLayers = listOf(
                    OverlayLayerConfig(
                        sourceId = "amap-road",
                        layerType = LayerType.ROAD_NETWORK,
                        opacity = 0.6f
                    ),
                    OverlayLayerConfig(
                        sourceId = "amap-standard",
                        layerType = LayerType.POI_ANNOTATIONS,
                        opacity = 0.5f
                    )
                )
            )
        ),
        
        TestConfiguration(
            name = "快速组合：卫星+路网",
            description = "使用快速组合API设置卫星图和路网",
            layerConfiguration = LayerConfiguration(
                enableSatelliteWithRoads = true
            )
        ),
        
        TestConfiguration(
            name = "混合叠加测试",
            description = "吉林一号 + 天地图路网 + 高德标注",
            layerConfiguration = LayerConfiguration(
                baseLayerSourceId = "jilin1-satellite",
                overlayLayers = listOf(
                    OverlayLayerConfig(
                        sourceId = "tianditu-road",
                        layerType = LayerType.ROAD_NETWORK,
                        opacity = 0.7f
                    ),
                    OverlayLayerConfig(
                        sourceId = "amap-standard",
                        layerType = LayerType.POI_ANNOTATIONS,
                        opacity = 0.6f
                    )
                )
            )
        )
    )
} 