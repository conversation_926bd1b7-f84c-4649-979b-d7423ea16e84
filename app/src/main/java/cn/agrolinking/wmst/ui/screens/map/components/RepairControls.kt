package cn.agrolinking.wmst.ui.screens.map.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AutoFixHigh
import androidx.compose.material.icons.filled.Build
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import cn.agrolinking.wmst.map.validation.GeometryRepairer

/**
 * 修复控制面板
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RepairControls(
    suggestions: List<GeometryRepairer.RepairSuggestion>,
    onQuickRepair: () -> Unit,
    onAggressiveRepair: () -> Unit,
    onApplySpecificRepair: (GeometryRepairer.RepairType) -> Unit,
    needsRepair: Boolean,
    repairableIssueCount: Int,
    modifier: Modifier = Modifier
) {
    if (!needsRepair && suggestions.isEmpty()) return
    
    Column(
        modifier = modifier
            .padding(8.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(MaterialTheme.colorScheme.surface)
            .padding(12.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 标题
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Build,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(20.dp)
            )
            
            Text(
                text = "几何体修复",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            if (repairableIssueCount > 0) {
                Badge(
                    containerColor = Color(0xFFFF9800)
                ) {
                    Text(
                        text = repairableIssueCount.toString(),
                        color = Color.White,
                        style = MaterialTheme.typography.labelSmall
                    )
                }
            }
        }
        
        // 快速操作按钮
        if (needsRepair) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                // 快速修复
                Button(
                    onClick = onQuickRepair,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF4CAF50)
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.AutoFixHigh,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("快速修复")
                }
                
                // 激进修复
                OutlinedButton(
                    onClick = onAggressiveRepair,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("激进修复")
                }
            }
        }
        
        // 具体修复建议列表
        if (suggestions.isNotEmpty()) {
            Divider(
                color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                modifier = Modifier.padding(vertical = 4.dp)
            )
            
            Text(
                text = "修复建议 (${suggestions.size})",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(4.dp),
                modifier = Modifier.heightIn(max = 200.dp)
            ) {
                items(suggestions) { suggestion ->
                    RepairSuggestionItem(
                        suggestion = suggestion,
                        onApply = { onApplySpecificRepair(suggestion.repairType) }
                    )
                }
            }
        }
    }
}

/**
 * 修复建议项
 */
@Composable
private fun RepairSuggestionItem(
    suggestion: GeometryRepairer.RepairSuggestion,
    onApply: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(4.dp))
            .background(
                if (suggestion.isAutoRepairable) {
                    MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                } else {
                    MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.1f)
                }
            )
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 优先级指示器
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(
                    when (suggestion.priority) {
                        in 1..3 -> Color(0xFFF44336) // 高优先级
                        in 4..6 -> Color(0xFFFF9800) // 中优先级
                        else -> Color(0xFF4CAF50)    // 低优先级
                    },
                    RoundedCornerShape(4.dp)
                )
        )
        
        // 描述信息
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = suggestion.description,
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Medium
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "优先级: ${suggestion.priority}",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                if (suggestion.estimatedTimeMs > 0) {
                    Text(
                        text = "耗时: ${suggestion.estimatedTimeMs}ms",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
        
        // 应用按钮
        if (suggestion.isAutoRepairable) {
            IconButton(
                onClick = onApply,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = "应用修复",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(16.dp)
                )
            }
        } else {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = "需要手动修复",
                tint = Color(0xFFFF9800),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 紧凑的修复状态指示器
 */
@Composable
fun CompactRepairIndicator(
    needsRepair: Boolean,
    repairableCount: Int,
    onQuickRepair: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (!needsRepair) return
    
    Row(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .background(Color(0xFFFF9800).copy(alpha = 0.1f))
            .clickable { onQuickRepair() }
            .padding(horizontal = 8.dp, vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = Icons.Default.AutoFixHigh,
            contentDescription = null,
            tint = Color(0xFFFF9800),
            modifier = Modifier.size(14.dp)
        )
        
        Text(
            text = "修复($repairableCount)",
            style = MaterialTheme.typography.labelSmall,
            color = Color(0xFFFF9800),
            fontWeight = FontWeight.Medium
        )
    }
} 