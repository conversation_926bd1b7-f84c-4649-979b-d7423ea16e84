package cn.agrolinking.wmst.ui.screens.map.components

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.agrolinking.wmst.map.model.TileSource
import cn.agrolinking.wmst.ui.screens.map.MapEvent
import cn.agrolinking.wmst.ui.screens.map.MapUiState

@Composable
fun LayerControlPanel(
    uiState: MapUiState,
    onEvent: (MapEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    // 横向图层选择面板 - 仿照HTML设计
    Row(
        modifier = modifier
            .height(56.dp)
            .background(
                color = Color.White.copy(alpha = 0.95f),
                shape = RoundedCornerShape(
                    topStart = 12.dp,
                    bottomStart = 12.dp,
                    topEnd = 6.dp,
                    bottomEnd = 6.dp
                )
            )
            .border(
                width = 3.dp,
                color = MaterialTheme.colorScheme.primary.copy(alpha = 0.3f),
                shape = RoundedCornerShape(
                    topStart = 12.dp,
                    bottomStart = 12.dp,
                    topEnd = 6.dp,
                    bottomEnd = 6.dp
                )
            )
            .padding(horizontal = 12.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 吉林一号
        LayerOptionButton(
            label = "吉林一号",
            preview = "吉",
            previewColor = Color(0xFF1e3c72),
            isSelected = uiState.selectedBaseLayer?.id == "jilin1-satellite",
            onClick = { onEvent(MapEvent.SatelliteSourceChanged("jilin1-satellite")) }
        )
        
        // 天地图
        LayerOptionButton(
            label = "天地图",
            preview = "天",
            previewColor = Color(0xFF667eea),
            isSelected = uiState.selectedBaseLayer?.id == "tianditu-satellite",
            onClick = { onEvent(MapEvent.SatelliteSourceChanged("tianditu-satellite")) }
        )
        
        // 高德卫星
        LayerOptionButton(
            label = "高德卫星",
            preview = "高",
            previewColor = Color(0xFFf093fb),
            isSelected = uiState.selectedBaseLayer?.id == "amap-satellite",
            onClick = { onEvent(MapEvent.SatelliteSourceChanged("amap-satellite")) }
        )
        
        // 高德标准
        LayerOptionButton(
            label = "高德标准",
            preview = "标",
            previewColor = Color(0xFF36d1dc),
            isSelected = uiState.selectedBaseLayer?.id == "amap-standard",
            onClick = { onEvent(MapEvent.SatelliteSourceChanged("amap-standard")) }
        )
    }
}

@Composable
private fun LayerOptionButton(
    label: String,
    preview: String,
    previewColor: Color,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .clip(RoundedCornerShape(6.dp))
            .clickable { onClick() }
            .background(
                if (isSelected) MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
                else Color.Transparent
            )
            .border(
                width = 1.dp,
                color = if (isSelected) MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)
                       else Color.Transparent,
                shape = RoundedCornerShape(6.dp)
            )
            .padding(horizontal = 6.dp, vertical = 3.dp)
            .height(44.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // 预览图标
        Box(
            modifier = Modifier
                .size(20.dp)
                .background(
                    color = previewColor,
                    shape = RoundedCornerShape(3.dp)
                )
                .border(
                    width = 1.dp,
                    color = Color.White.copy(alpha = 0.9f),
                    shape = RoundedCornerShape(3.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = preview,
                color = Color.White,
                fontSize = 8.sp,
                fontWeight = FontWeight.Bold
            )
        }
        
        Spacer(modifier = Modifier.height(2.dp))
        
        // 标签
        Text(
            text = label,
            fontSize = 9.sp,
            fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface,
            maxLines = 1
        )
    }
} 