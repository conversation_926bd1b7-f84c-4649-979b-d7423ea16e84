package cn.agrolinking.wmst.ui.screens.map

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import cn.agrolinking.wmst.map.manager.LayerCompositeManager
import cn.agrolinking.wmst.map.manager.TileSourceManager
import cn.agrolinking.wmst.map.model.TileSource
import cn.agrolinking.wmst.map.drawing.DrawingMode
import cn.agrolinking.wmst.domain.drawing.LandPlotInProgress
import cn.agrolinking.wmst.domain.drawing.PolygonInProgress
import cn.agrolinking.wmst.domain.geojson.LandPlotFeature
import cn.agrolinking.wmst.domain.geojson.LandPlotProperties
import cn.agrolinking.wmst.domain.geojson.MultiPolygonGeometry
import cn.agrolinking.wmst.repository.LandPlotRepository
import cn.agrolinking.wmst.map.validation.GeometryValidator
import cn.agrolinking.wmst.map.validation.GeometryRepairer
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.maplibre.android.geometry.LatLng
import java.util.UUID
import javax.inject.Inject



@HiltViewModel
class MapViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    val tileSourceManager: TileSourceManager,
    val layerCompositeManager: LayerCompositeManager,
    private val landPlotRepository: LandPlotRepository
) : ViewModel() {

    val scope = viewModelScope

    // 几何体验证器和修复器
    private val geometryValidator = GeometryValidator()
    private val geometryRepairer = GeometryRepairer()

    // 使用新的统一UI状态
    private val _uiState = MutableStateFlow(MapUiState())
    val uiState: StateFlow<MapUiState> = _uiState.asStateFlow()

    // 保持向后兼容的属性
    val baseLayerSources: StateFlow<List<TileSource>> = 
        MutableStateFlow<List<TileSource>>(emptyList()).apply {
            viewModelScope.launch {
                uiState.collect { state ->
                    value = state.baseLayerSources
                }
            }
        }.asStateFlow()

    val overlayLayerSources: StateFlow<List<TileSource>> = 
        MutableStateFlow<List<TileSource>>(emptyList()).apply {
            viewModelScope.launch {
                uiState.collect { state ->
                    value = state.overlayLayerSources
                }
            }
        }.asStateFlow()

    val selectedBaseLayer: StateFlow<TileSource?> = 
        MutableStateFlow<TileSource?>(null).apply {
            viewModelScope.launch {
                uiState.collect { state ->
                    value = state.selectedBaseLayer
                }
            }
        }.asStateFlow()

    val selectedOverlayLayer: StateFlow<TileSource?> = 
        MutableStateFlow<TileSource?>(null).apply {
            viewModelScope.launch {
                uiState.collect { state ->
                    value = state.selectedOverlayLayer
                }
            }
        }.asStateFlow()

    val isTileSourcesLoaded: StateFlow<Boolean> = 
        MutableStateFlow(false).apply {
            viewModelScope.launch {
                uiState.collect { state ->
                    value = state.isTileSourcesLoaded
                }
            }
        }.asStateFlow()

    init {
        loadTileSources()
        loadLandPlots()
    }

    private fun loadTileSources() {
        viewModelScope.launch {
            try {
                val sources = tileSourceManager.loadTileSources()
                
                // 分离底图和叠加层
                val baseLayers = sources.filter { 
                    it.id.contains("satellite") || it.id.contains("standard")
                }
                val overlayLayers = sources.filter { it.id.contains("road") }

                val selectedBase = if (_uiState.value.selectedBaseLayer == null && baseLayers.isNotEmpty()) {
                    baseLayers.first()
                } else {
                    _uiState.value.selectedBaseLayer
                }
                
                _uiState.value = _uiState.value.copy(
                    baseLayerSources = baseLayers,
                    overlayLayerSources = overlayLayers,
                    selectedBaseLayer = selectedBase,
                    isTileSourcesLoaded = true
                )
            } catch (e: Exception) {
                Log.e("MapViewModel", "加载瓦片源失败", e)
                _uiState.value = _uiState.value.copy(
                    isTileSourcesLoaded = false
                )
            }
        }
    }

    private fun loadLandPlots() {
        viewModelScope.launch {
            landPlotRepository.getAllPlots().collect { landPlots ->
                _uiState.value = _uiState.value.copy(
                    viewingState = _uiState.value.viewingState.copy(
                        loadedLandPlots = landPlots,
                        isLoading = false
                    )
                )
                Log.d("MapViewModel", "已加载 ${landPlots.size} 个地块")
            }
        }
    }

    // ========== 统一事件处理 ==========

    fun onEvent(event: MapEvent) {
        when (event) {
            // 图层事件
            is MapEvent.SatelliteSourceChanged -> {
                setLayerCombination(event.sourceId)
                hideLayerPanel()
            }
            is MapEvent.RoadLayerVisibilityChanged -> {
                handleRoadLayerVisibility(event.visible)
                hideLayerPanel()
            }
            is MapEvent.RoadLayerOpacityChanged -> {
                // TODO: 实现透明度控制
            }
            
            // 绘制事件
            is MapEvent.StartDrawing -> startDrawing(event.mode)
            MapEvent.CompleteCurrentRing -> completeCurrentRing()
            MapEvent.StartNewHole -> startNewHole()
            MapEvent.StartNewPolygon -> startNewPolygon()
            MapEvent.SaveCurrentLandPlot -> saveCurrentLandPlot()
            MapEvent.CancelDrawing -> cancelDrawing()
            
            // 查看编辑事件
            is MapEvent.SelectLandPlot -> selectLandPlot(event.landPlot)
            is MapEvent.StartEditingLandPlot -> startEditingLandPlot(event.landPlot)
            MapEvent.SaveEdits -> saveEdits()
            MapEvent.CancelEditing -> cancelEditing()
            
            // 交互事件
            is MapEvent.AddPoint -> addPoint(event.point)
            MapEvent.UndoLastPoint -> undoLastPointInternal()
            MapEvent.ClearCurrentDrawing -> clearCurrentDrawingInternal()
            is MapEvent.StartDrag -> startDragInternal(event.targetIndex, event.originalPoint)
            is MapEvent.UpdateDraggedPoint -> updateDraggedPointInternal(event.newPoint)
            MapEvent.EndDrag -> endDragInternal()
            is MapEvent.InsertMidpoint -> insertMidpointInternal(event.edgeStartIndex, event.midpoint)
            
            // 新增：统一交互事件
            is MapEvent.Click -> handleMapClick(event.latLng)
            is MapEvent.LongPress -> handleMapLongPress(event.latLng)
            is MapEvent.DragStart -> handleDragStart(event.vertexIndex, event.latLng)
            is MapEvent.DragUpdate -> handleDragUpdate(event.latLng)
            MapEvent.DragEnd -> handleDragEnd()
            is MapEvent.MidpointClick -> handleMidpointClick(event.edgeIndex, event.latLng)
            is MapEvent.DragPreview -> handleDragPreview(event.preview)
            
            // 兼容性事件
            is MapEvent.CameraPositionChanged -> handleCameraPositionChanged(event.latitude, event.longitude, event.zoom)
            is MapEvent.DrawingCompleted -> handleDrawingCompleted(event.landPlot)
            is MapEvent.DrawingModeManagerReady -> handleDrawingModeManagerReady(event.manager)
        }
    }

    // ========== 工具和模式管理 ==========

    fun onToolChanged(tool: MapTool) {
        when (tool) {
            MapTool.DRAW -> {
                // 切换到绘制工具，自动启动绘制模式
                startDrawing(DrawingMode.MANUAL_AREA)
                _uiState.value = _uiState.value.copy(
                    currentTool = tool,
                    showDrawingControls = true
                )
                Log.d("MapViewModel", "切换到绘制工具并启动绘制模式")
            }
            MapTool.VIEW -> {
                // 切换到浏览工具，清理绘制状态
                cancelDrawing()
                _uiState.value = _uiState.value.copy(
                    currentTool = tool,
                    showDrawingControls = false
                )
                Log.d("MapViewModel", "切换到浏览工具")
            }
        }
    }

    fun onModeChanged(mode: MapMode) {
        _uiState.value = _uiState.value.copy(currentMode = mode)
    }

    // ========== 绘制功能 ==========

    private fun startDrawing(mode: DrawingMode) {
        val newPlot = LandPlotInProgress(
            polygons = listOf(PolygonInProgress(
                exterior = emptyList(),
                holes = emptyList()
            )),
            activePolygonIndex = 0,
            activeRingIndex = 0
        )
        
        _uiState.value = _uiState.value.copy(
            currentTool = MapTool.DRAW,
            drawingState = DrawingState(
                mode = mode,
                workingLandPlot = newPlot,
                isActive = true,
                canUndo = false,
                canClear = false
            ),
            showDrawingControls = true
        )
        
        Log.d("MapViewModel", "开始绘制地块，模式: $mode")
    }

    private fun addPoint(point: LatLng) {
        val currentState = _uiState.value.drawingState
        val workingPlot = currentState.workingLandPlot ?: return
        
        if (!currentState.isActive) return
        
        val updatedPlot = workingPlot.addVertex(point)
        val currentRing = updatedPlot.getCurrentRing()
        val canUndo = currentRing.isNotEmpty()
        
        // 详细调试日志
        Log.d("MapViewModel", "=== 添加点后状态检查 ===")
        Log.d("MapViewModel", "当前环大小: ${currentRing.size}")
        Log.d("MapViewModel", "canUndo计算: ${currentRing.isNotEmpty()} = $canUndo")
        Log.d("MapViewModel", "活动多边形索引: ${updatedPlot.activePolygonIndex}")
        Log.d("MapViewModel", "活动环索引: ${updatedPlot.activeRingIndex}")
        
        // 执行自动验证
        val validationResult = if (currentState.enableAutoValidation) {
            performValidation(updatedPlot)
        } else null
        
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                workingLandPlot = updatedPlot,
                canUndo = canUndo,
                canClear = canUndo,
                validationResult = validationResult
            )
        )
        
        val ringSize = updatedPlot.getCurrentRing().size
        Log.d("MapViewModel", "添加点: (${"%.6f".format(point.latitude)}, ${"%.6f".format(point.longitude)}), 当前环有 $ringSize 个点")
        Log.d("MapViewModel", "UI状态更新: canUndo=$canUndo, canClear=$canUndo")
        
        // 输出验证结果
        if (validationResult != null && (validationResult.hasErrors || validationResult.hasWarnings)) {
            Log.d("MapViewModel", "验证问题: ${validationResult.summaryText}")
        }
    }

    private fun undoLastPointInternal() {
        val currentState = _uiState.value.drawingState
        val workingPlot = currentState.workingLandPlot ?: return
        
        if (workingPlot.getCurrentRing().isEmpty()) return
        
        val updatedPlot = workingPlot.removeLastVertex()
        val canUndo = updatedPlot.getCurrentRing().isNotEmpty()
        
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                workingLandPlot = updatedPlot,
                canUndo = canUndo,
                canClear = canUndo
            )
        )
        
        Log.d("MapViewModel", "撤销最后一个点")
    }

    private fun clearCurrentDrawingInternal() {
        val currentState = _uiState.value.drawingState
        val workingPlot = currentState.workingLandPlot ?: return
        
        val updatedPlot = workingPlot.clearCurrentRing()
        
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                workingLandPlot = updatedPlot,
                canUndo = false,
                canClear = false
            )
        )
        
        Log.d("MapViewModel", "清空当前绘制")
    }

    private fun completeCurrentRing() {
        val currentState = _uiState.value.drawingState
        val workingPlot = currentState.workingLandPlot ?: return
        
        if (workingPlot.getCurrentRing().size < 3) {
            Log.w("MapViewModel", "需要至少3个点才能完成环")
            return
        }
        
        val updatedPlot = workingPlot.completeCurrentRing()
        
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                workingLandPlot = updatedPlot,
                canUndo = false,
                canClear = false
            )
        )
        
        Log.d("MapViewModel", "完成当前环绘制")
    }

    private fun startNewHole() {
        val currentState = _uiState.value.drawingState
        val workingPlot = currentState.workingLandPlot ?: return
        
        if (!currentState.canAddHole) {
            Log.w("MapViewModel", "当前状态不能添加孔洞")
            return
        }
        
        val updatedPlot = workingPlot.startNewHole()
        
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                workingLandPlot = updatedPlot
            )
        )
        
        Log.d("MapViewModel", "开始绘制新孔洞")
    }

    private fun startNewPolygon() {
        val currentState = _uiState.value.drawingState
        val workingPlot = currentState.workingLandPlot ?: return
        
        if (!currentState.canAddNewPolygon) {
            Log.w("MapViewModel", "当前状态不能添加新多边形")
            return
        }
        
        val updatedPlot = workingPlot.startNewPolygon()
        
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                workingLandPlot = updatedPlot
            )
        )
        
        Log.d("MapViewModel", "开始绘制新多边形")
    }

    private fun saveCurrentLandPlot() {
        val currentState = _uiState.value.drawingState
        val workingPlot = currentState.workingLandPlot ?: return
        
        if (!currentState.canSaveLandPlot) {
            Log.w("MapViewModel", "当前地块数据不完整，无法保存")
            return
        }
        
        viewModelScope.launch {
            try {
                // 转换为GeoJSON格式
                val landPlotFeature = workingPlot.toGeoJsonFeature(
                    id = UUID.randomUUID().toString(),
                    properties = LandPlotProperties(
                        name = "新地块_${System.currentTimeMillis()}",
                        area = 0.0, // 将在保存时计算
                        perimeter = 0.0 // 将在保存时计算
                    )
                )
                
                // 保存到数据库
                val result = landPlotRepository.savePlot(landPlotFeature)
                
                if (result.isSuccess) {
                    Log.i("MapViewModel", "地块保存成功: ${result.getOrNull()}")
                    
                    // 重置绘制状态
                    _uiState.value = _uiState.value.copy(
                        currentTool = MapTool.VIEW,
                        drawingState = DrawingState(),
                        showDrawingControls = false
                    )
                } else {
                    Log.e("MapViewModel", "地块保存失败", result.exceptionOrNull())
                }
            } catch (e: Exception) {
                Log.e("MapViewModel", "保存地块时发生错误", e)
            }
        }
    }

    private fun cancelDrawing() {
        _uiState.value = _uiState.value.copy(
            currentTool = MapTool.VIEW,
            drawingState = DrawingState(),
            showDrawingControls = false
        )
        Log.d("MapViewModel", "取消绘制")
    }

    // ========== 查看和编辑功能 ==========

    private fun selectLandPlot(landPlot: LandPlotFeature) {
        _uiState.value = _uiState.value.copy(
            viewingState = _uiState.value.viewingState.copy(
                selectedLandPlot = landPlot
            ),
            showLandPlotInfo = true
        )
        Log.d("MapViewModel", "选择地块: ${landPlot.properties.name}")
    }

    private fun startEditingLandPlot(landPlot: LandPlotFeature) {
        // 将地块转换为编辑状态
        val workingPlot = LandPlotInProgress.fromGeoJsonFeature(landPlot)
        
        _uiState.value = _uiState.value.copy(
            editingState = EditingState(
                editingLandPlot = landPlot,
                workingLandPlot = workingPlot,
                isEditing = true,
                hasUnsavedChanges = false
            ),
            showDrawingControls = true
        )
        Log.d("MapViewModel", "开始编辑地块: ${landPlot.properties.name}")
    }

    private fun saveEdits() {
        val editingState = _uiState.value.editingState
        val workingPlot = editingState.workingLandPlot ?: return
        val originalPlot = editingState.editingLandPlot ?: return
        
        viewModelScope.launch {
            try {
                // 转换为更新后的GeoJSON
                val updatedPlot = workingPlot.toGeoJsonFeature(
                    id = originalPlot.id,
                    properties = originalPlot.properties.copy(
                        updatedAt = System.currentTimeMillis()
                    )
                )
                
                // 保存更新
                val result = landPlotRepository.savePlot(updatedPlot)
                
                if (result.isSuccess) {
                    Log.i("MapViewModel", "地块编辑保存成功")
                    
                    // 退出编辑模式
                    _uiState.value = _uiState.value.copy(
                        editingState = EditingState(),
                        showDrawingControls = false
                    )
                } else {
                    Log.e("MapViewModel", "地块编辑保存失败", result.exceptionOrNull())
                }
            } catch (e: Exception) {
                Log.e("MapViewModel", "保存编辑时发生错误", e)
            }
        }
    }

    private fun cancelEditing() {
        _uiState.value = _uiState.value.copy(
            editingState = EditingState(),
            showDrawingControls = false
        )
        Log.d("MapViewModel", "取消编辑")
    }

    // ========== 拖拽功能 ==========

    private fun startDragInternal(targetIndex: Int, originalPoint: LatLng) {
        val currentState = _uiState.value.drawingState
        val workingPlot = currentState.workingLandPlot
        
        // 为向后兼容，创建复杂拖拽目标
        val complexTarget = if (workingPlot != null) {
            ComplexDragTarget.Vertex(
                polygonIndex = workingPlot.activePolygonIndex,
                ringIndex = workingPlot.activeRingIndex,
                vertexIndex = targetIndex
            )
        } else null
        
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                isDragging = true,
                dragTargetIndex = targetIndex, // 保持兼容性
                complexDragTarget = complexTarget,
                dragOriginalPoint = originalPoint,
                isDragMode = true,
                showMidpoints = true
            )
        )
        Log.d("MapViewModel", "开始拖拽顶点: $targetIndex, 复杂目标: $complexTarget")
    }

    private fun updateDraggedPointInternal(newPoint: LatLng) {
        val currentState = _uiState.value.drawingState
        val workingPlot = currentState.workingLandPlot ?: return
        val dragTarget = currentState.complexDragTarget ?: return
        
        if (currentState.isDragging) {
            val updatedPlot = when (dragTarget) {
                is ComplexDragTarget.Vertex -> {
                    // 更新复杂几何体中的顶点
                    workingPlot.updateVertexInPolygon(
                        polygonIndex = dragTarget.polygonIndex,
                        ringIndex = dragTarget.ringIndex,
                        vertexIndex = dragTarget.vertexIndex,
                        newVertex = newPoint
                    )
                }
                is ComplexDragTarget.EdgeMidpoint -> {
                    // 中点拖拽转换为顶点插入
                    workingPlot.insertVertexInPolygon(
                        polygonIndex = dragTarget.polygonIndex,
                        ringIndex = dragTarget.ringIndex,
                        insertIndex = dragTarget.edgeEndIndex,
                        newVertex = newPoint
                    )
                }
            }
            
            _uiState.value = _uiState.value.copy(
                drawingState = currentState.copy(
                    workingLandPlot = updatedPlot
                ),
                editingState = _uiState.value.editingState.copy(
                    hasUnsavedChanges = true
                )
            )
        }
    }

    private fun endDragInternal() {
        val currentState = _uiState.value.drawingState
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                isDragging = false,
                dragTargetIndex = null,
                complexDragTarget = null,
                dragOriginalPoint = null
            )
        )
        Log.d("MapViewModel", "结束拖拽")
    }

    private fun insertMidpointInternal(edgeStartIndex: Int, midpoint: LatLng) {
        val currentState = _uiState.value.drawingState
        val workingPlot = currentState.workingLandPlot ?: return
        
        // 在当前环中插入中点
        val updatedPlot = workingPlot.insertVertexInPolygon(
            polygonIndex = workingPlot.activePolygonIndex,
            ringIndex = workingPlot.activeRingIndex,
            insertIndex = edgeStartIndex + 1,
            newVertex = midpoint
        )
        
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                workingLandPlot = updatedPlot,
                canUndo = true,
                canClear = true
            ),
            editingState = _uiState.value.editingState.copy(
                hasUnsavedChanges = true
            )
        )
        
        Log.d("MapViewModel", "插入中点: 多边形${workingPlot.activePolygonIndex}, 环${workingPlot.activeRingIndex}, 边$edgeStartIndex")
    }

    // ========== 图层管理 ==========

    fun showLayerPanel() {
        _uiState.value = _uiState.value.copy(showLayerPanel = true)
    }

    fun hideLayerPanel() {
        _uiState.value = _uiState.value.copy(showLayerPanel = false)
    }

    private fun handleRoadLayerVisibility(visible: Boolean) {
        if (visible && _uiState.value.overlayLayerSources.isNotEmpty()) {
                    toggleOverlayLayer(_uiState.value.overlayLayerSources.first())
                } else {
                    toggleOverlayLayer(null)
        }
    }

    private fun setLayerCombination(baseLayerSourceId: String) {
        val baseLayers = _uiState.value.baseLayerSources
        val overlayLayers = _uiState.value.overlayLayerSources
        
        val (selectedBase, selectedOverlay) = when (baseLayerSourceId) {
            "jilin1-satellite" -> {
                val baseLayer = baseLayers.find { it.id == "jilin1-satellite" }
                val overlayLayer = overlayLayers.find { it.id == "amap-road" }
                Pair(baseLayer, overlayLayer)
            }
            "tianditu-satellite" -> {
                val baseLayer = baseLayers.find { it.id == "tianditu-satellite" }
                val overlayLayer = overlayLayers.find { it.id == "tianditu-road" }
                Pair(baseLayer, overlayLayer)
            }
            "amap-satellite" -> {
                val baseLayer = baseLayers.find { it.id == "amap-satellite" }
                val overlayLayer = overlayLayers.find { it.id == "amap-road" }
                Pair(baseLayer, overlayLayer)
            }
            "amap-standard" -> {
                val baseLayer = baseLayers.find { it.id == "amap-standard" }
                Pair(baseLayer, null)
            }
            else -> {
                val baseLayer = baseLayers.find { it.id == baseLayerSourceId }
                Pair(baseLayer, null)
            }
        }
        
        _uiState.value = _uiState.value.copy(
            selectedBaseLayer = selectedBase,
            selectedOverlayLayer = selectedOverlay
        )
    }

    // ========== 向后兼容方法 ==========

    fun updateMapPosition(latitude: Double, longitude: Double, zoomLevel: Double) {
        _uiState.value = _uiState.value.copy(
            latitude = latitude,
            longitude = longitude,
            zoomLevel = zoomLevel
        )
    }

    fun selectBaseLayer(layer: TileSource) {
        _uiState.value = _uiState.value.copy(selectedBaseLayer = layer)
    }

    fun toggleOverlayLayer(layer: TileSource?) {
        _uiState.value = _uiState.value.copy(selectedOverlayLayer = layer)
    }

    // ========== 兼容旧MapScreen的方法 ==========

    fun addDrawingPoint(point: LatLng) {
        onEvent(MapEvent.AddPoint(point))
    }

    fun undoLastPoint() {
        onEvent(MapEvent.UndoLastPoint)
    }

    fun clearCurrentDrawing() {
        onEvent(MapEvent.ClearCurrentDrawing)
    }

    fun enableDragMode() {
        val currentState = _uiState.value.drawingState
        // 🔥 修复：与长按处理保持一致的条件
        if (currentState.currentRing.size >= 3 || (currentState.workingLandPlot?.isValidLandPlot() == true)) {
            _uiState.value = _uiState.value.copy(
                drawingState = currentState.copy(
                    isDragMode = true,
                    showMidpoints = true
                )
            )
            Log.d("MapViewModel", "拖拽模式已激活")
        } else {
            Log.d("MapViewModel", "拖拽模式激活失败：当前环点数=${currentState.currentRing.size}, 地块有效=${currentState.workingLandPlot?.isValidLandPlot()}")
        }
    }

    fun startDrag(targetIndex: Int, originalPoint: LatLng) {
        onEvent(MapEvent.StartDrag(targetIndex, originalPoint))
    }

    fun updateDraggedPoint(newPoint: LatLng) {
        onEvent(MapEvent.UpdateDraggedPoint(newPoint))
    }

    fun endDrag() {
        onEvent(MapEvent.EndDrag)
    }

    fun insertMidpoint(edgeStartIndex: Int, midpoint: LatLng) {
        onEvent(MapEvent.InsertMidpoint(edgeStartIndex, midpoint))
    }

    fun closeAndClearDrawing() {
        onEvent(MapEvent.CancelDrawing)
    }

    // ========== 统一交互事件处理函数 ==========

    private fun handleMapClick(latLng: LatLng) {
        val currentState = _uiState.value.drawingState
        if (currentState.isActive && !currentState.isDragMode) {
            // 绘制模式下的点击处理
            addPoint(latLng)
        }
        Log.d("MapViewModel", "地图点击: (${"%.6f".format(latLng.latitude)}, ${"%.6f".format(latLng.longitude)})")
    }

    private fun handleMapLongPress(latLng: LatLng) {
        val currentState = _uiState.value.drawingState
        if (currentState.isActive) {
            // 长按触发振动反馈
            triggerHapticFeedback()

            // 🔥 修复：优先检查是否可以进入拖拽模式
            if (currentState.currentRing.size >= 3 || (currentState.workingLandPlot != null && currentState.workingLandPlot.isValidLandPlot())) {
                Log.d("MapViewModel", "长按进入拖拽模式")
                enableDragMode()
            } else {
                Log.d("MapViewModel", "长按无法进入拖拽模式：当前环点数=${currentState.currentRing.size}, 地块有效=${currentState.workingLandPlot?.isValidLandPlot()}")
            }
        }
        Log.d("MapViewModel", "地图长按: (${"%.6f".format(latLng.latitude)}, ${"%.6f".format(latLng.longitude)})")
    }

    private fun handleDragStart(vertexIndex: Int, latLng: LatLng) {
        startDragInternal(vertexIndex, latLng)
        Log.d("MapViewModel", "开始拖拽顶点 $vertexIndex: (${"%.6f".format(latLng.latitude)}, ${"%.6f".format(latLng.longitude)})")
    }

    private fun handleDragUpdate(latLng: LatLng) {
        updateDraggedPointInternal(latLng)
    }

    private fun handleDragEnd() {
        endDragInternal()
        Log.d("MapViewModel", "拖拽结束")
    }

    private fun handleMidpointClick(edgeIndex: Int, latLng: LatLng) {
        insertMidpointInternal(edgeIndex, latLng)
        Log.d("MapViewModel", "中点插入在边 $edgeIndex: (${"%.6f".format(latLng.latitude)}, ${"%.6f".format(latLng.longitude)})")
    }
    
    private fun handleDragPreview(preview: cn.agrolinking.wmst.map.interaction.DragInteractionManager.DragPreview?) {
        if (preview != null) {
            Log.d("MapViewModel", """
                拖拽预览更新:
                - 原始位置: (${"%.6f".format(preview.originalPoint.latitude)}, ${"%.6f".format(preview.originalPoint.longitude)})
                - 当前位置: (${"%.6f".format(preview.currentPoint.latitude)}, ${"%.6f".format(preview.currentPoint.longitude)})
                - 预览位置: (${"%.6f".format(preview.previewPoint.latitude)}, ${"%.6f".format(preview.previewPoint.longitude)})
                - 吸附类型: ${preview.snapType::class.simpleName}
                - 几何体有效: ${preview.isValidGeometry}
                ${preview.validationMessage?.let { "- 验证消息: $it" } ?: ""}
            """.trimIndent())
            
            // 可以在这里触发UI更新，例如显示吸附提示或验证错误
            when (preview.snapType) {
                is cn.agrolinking.wmst.map.interaction.DragInteractionManager.SnapType.Vertex -> {
                    Log.d("MapViewModel", "顶点吸附激活")
                }
                is cn.agrolinking.wmst.map.interaction.DragInteractionManager.SnapType.Grid -> {
                    Log.d("MapViewModel", "网格吸附激活")
                }
                is cn.agrolinking.wmst.map.interaction.DragInteractionManager.SnapType.Angle -> {
                    Log.d("MapViewModel", "角度吸附激活: ${preview.snapType.angle}°")
                }
                is cn.agrolinking.wmst.map.interaction.DragInteractionManager.SnapType.None -> {
                    // 无吸附
                }
            }
            
            if (!preview.isValidGeometry) {
                Log.w("MapViewModel", "拖拽预览几何体无效: ${preview.validationMessage}")
            }
        } else {
            Log.d("MapViewModel", "拖拽预览已清除")
        }
    }
    
    // ========== 几何体验证功能 ==========
    
    /**
     * 执行几何体验证
     */
    private fun performValidation(landPlot: LandPlotInProgress): GeometryValidator.ValidationResult {
        return try {
            geometryValidator.validateLandPlot(landPlot)
        } catch (e: Exception) {
            Log.e("MapViewModel", "几何体验证失败", e)
            GeometryValidator.ValidationResult(
                isValid = false,
                errors = listOf(
                    GeometryValidator.ValidationError(
                        type = GeometryValidator.ErrorType.INVALID_TOPOLOGY,
                        message = "验证过程发生错误: ${e.message}"
                    )
                )
            )
        }
    }
    
    /**
     * 执行快速验证（仅检查关键错误）
     */
    private fun performQuickValidation(landPlot: LandPlotInProgress): GeometryValidator.ValidationResult {
        return try {
            geometryValidator.quickValidate(landPlot)
        } catch (e: Exception) {
            Log.e("MapViewModel", "快速验证失败", e)
            GeometryValidator.ValidationResult(
                isValid = false,
                errors = listOf(
                    GeometryValidator.ValidationError(
                        type = GeometryValidator.ErrorType.INVALID_TOPOLOGY,
                        message = "验证过程发生错误: ${e.message}"
                    )
                )
            )
        }
    }
    
    /**
     * 手动触发验证
     */
    fun triggerValidation() {
        val currentState = _uiState.value.drawingState
        val workingPlot = currentState.workingLandPlot
        
        if (workingPlot != null) {
            val validationResult = performValidation(workingPlot)
            
            _uiState.value = _uiState.value.copy(
                drawingState = currentState.copy(
                    validationResult = validationResult
                )
            )
            
            Log.i("MapViewModel", "手动验证完成: ${if (validationResult.isValid) "通过" else "失败"}")
            if (validationResult.hasErrors || validationResult.hasWarnings) {
                Log.d("MapViewModel", "验证详情:\n${validationResult.summaryText}")
            }
        }
    }
    
    /**
     * 切换自动验证
     */
    fun toggleAutoValidation() {
        val currentState = _uiState.value.drawingState
        val enableAutoValidation = !currentState.enableAutoValidation
        
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                enableAutoValidation = enableAutoValidation,
                validationResult = if (enableAutoValidation && currentState.workingLandPlot != null) {
                    performValidation(currentState.workingLandPlot)
                } else null
            )
        )
        
        Log.d("MapViewModel", "自动验证: ${if (enableAutoValidation) "开启" else "关闭"}")
    }
    
    /**
     * 切换验证指示器显示
     */
    fun toggleValidationIndicators() {
        val currentState = _uiState.value.drawingState
        val showIndicators = !currentState.showValidationIndicators
        
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                showValidationIndicators = showIndicators
            )
        )
        
        Log.d("MapViewModel", "验证指示器显示: ${if (showIndicators) "开启" else "关闭"}")
    }
    
    /**
     * 获取验证详情（用于UI显示）
     */
    fun getValidationDetails(): String? {
        return _uiState.value.drawingState.validationResult?.summaryText
    }
    
    /**
     * 清除验证结果
     */
    fun clearValidationResult() {
        val currentState = _uiState.value.drawingState
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                validationResult = null
            )
        )
        Log.d("MapViewModel", "验证结果已清除")
    }
    
    // ========== 几何体修复功能 ==========
    
    /**
     * 获取可修复问题数量
     */
    fun getRepairableIssueCount(): Int {
        // 暂时禁用修复功能，返回0
        return 0
        
        // 原有逻辑保留备用：
        // val currentState = _uiState.value.drawingState
        // val validationResult = currentState.validationResult
        // if (validationResult == null) return 0
        // val errorCount = validationResult.errors.size
        // val warningCount = validationResult.warnings.size
        // return errorCount + warningCount
    }

    /**
     * 自动修复当前地块
     */
    fun autoRepairCurrentLandPlot(strategy: GeometryRepairer.RepairStrategy = GeometryRepairer.RepairStrategy.DEFAULT) {
        // 暂时禁用修复功能
        Log.i("MapViewModel", "修复功能暂时禁用，后续版本将重新启用")
        return
        
        // 原有逻辑保留备用... 
    }
    
    /**
     * 快速修复（仅基础修复）
     */
    fun quickRepairCurrentLandPlot() {
        autoRepairCurrentLandPlot(GeometryRepairer.RepairStrategy.CONSERVATIVE)
    }
    
    /**
     * 激进修复（所有修复类型）
     */
    fun aggressiveRepairCurrentLandPlot() {
        autoRepairCurrentLandPlot(GeometryRepairer.RepairStrategy.AGGRESSIVE)
    }
    
    /**
     * 获取当前地块的修复建议
     */
    fun getRepairSuggestions(): List<GeometryRepairer.RepairSuggestion> {
        val currentState = _uiState.value.drawingState
        val validationResult = currentState.validationResult
        
        return if (validationResult != null) {
            geometryRepairer.getRepairSuggestions(validationResult)
        } else {
            emptyList()
        }
    }
    
    /**
     * 应用特定的修复类型
     */
    fun applySpecificRepair(repairType: GeometryRepairer.RepairType) {
        val strategy = GeometryRepairer.RepairStrategy(
            enabledRepairTypes = setOf(repairType),
            aggressiveMode = false,
            preserveOriginalShape = true
        )
        autoRepairCurrentLandPlot(strategy)
    }
    
    /**
     * 获取修复预览（不实际应用）
     */
    fun getRepairPreview(strategy: GeometryRepairer.RepairStrategy = GeometryRepairer.RepairStrategy.DEFAULT): GeometryRepairer.RepairResult? {
        val currentState = _uiState.value.drawingState
        val workingPlot = currentState.workingLandPlot ?: return null
        
        return try {
            geometryRepairer.repairLandPlot(workingPlot, strategy)
        } catch (e: Exception) {
            Log.e("MapViewModel", "获取修复预览失败", e)
            null
        }
    }
    
    /**
     * 检查是否需要修复
     */
    fun needsRepair(): Boolean {
        // 暂时禁用修复功能，后续优化时再启用
        return false
        
        // 原有逻辑保留备用：
        // val validationResult = currentState.validationResult
        // return validationResult?.hasErrors == true || validationResult?.hasWarnings == true
    }
    
    // ========== 兼容性事件处理 ==========
    
    /**
     * 处理相机位置变化事件
     */
    private fun handleCameraPositionChanged(latitude: Double, longitude: Double, zoom: Double) {
        _uiState.value = _uiState.value.copy(
            latitude = latitude,
            longitude = longitude,
            zoomLevel = zoom
        )
        Log.d("MapViewModel", "相机位置更新: (${"%.6f".format(latitude)}, ${"%.6f".format(longitude)}) zoom=${"%.2f".format(zoom)}")
    }
    
    /**
     * 处理绘制完成事件
     */
    private fun handleDrawingCompleted(landPlot: cn.agrolinking.wmst.domain.LandPlot) {
        Log.i("MapViewModel", "绘制完成: 地块面积=${landPlot.area}m²")
        // 这里可以添加保存到数据库或其他处理逻辑
    }
    
    /**
     * 处理绘制管理器就绪事件
     */
    private fun handleDrawingModeManagerReady(manager: cn.agrolinking.wmst.map.drawing.DrawingModeManager) {
        Log.d("MapViewModel", "绘制模式管理器已就绪")
        // 这里可以保存管理器引用或进行初始化配置
    }

    /**
     * 触发触觉反馈（振动）
     */
    private fun triggerHapticFeedback() {
        try {
            val vibrator = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as android.os.VibratorManager
                vibratorManager.defaultVibrator
            } else {
                @Suppress("DEPRECATION")
                context.getSystemService(Context.VIBRATOR_SERVICE) as android.os.Vibrator
            }
            
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                vibrator.vibrate(android.os.VibrationEffect.createOneShot(50, android.os.VibrationEffect.DEFAULT_AMPLITUDE))
            } else {
                @Suppress("DEPRECATION")
                vibrator.vibrate(50)
            }
            
            Log.d("MapViewModel", "触发振动反馈")
        } catch (e: Exception) {
            Log.w("MapViewModel", "无法触发振动反馈", e)
        }
    }
}

