package cn.agrolinking.wmst.ui.screens.map.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.agrolinking.wmst.map.validation.GeometryValidator

/**
 * 验证结果指示器组件
 */
@Composable
fun ValidationIndicator(
    validationResult: GeometryValidator.ValidationResult?,
    onToggleDetails: () -> Unit,
    showDetails: Boolean = false,
    modifier: Modifier = Modifier
) {
    if (validationResult == null) return
    
    Column(
        modifier = modifier
            .padding(8.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(MaterialTheme.colorScheme.surface)
            .border(1.dp, MaterialTheme.colorScheme.outline, RoundedCornerShape(8.dp))
    ) {
        // 状态指示器
        ValidationStatusBadge(
            validationResult = validationResult,
            onToggleDetails = onToggleDetails,
            modifier = Modifier.fillMaxWidth()
        )
        
        // 详细信息
        if (showDetails) {
            ValidationDetails(
                validationResult = validationResult,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 验证状态徽章
 */
@Composable
private fun ValidationStatusBadge(
    validationResult: GeometryValidator.ValidationResult,
    onToggleDetails: () -> Unit,
    modifier: Modifier = Modifier
) {
    val (icon, color, text) = when {
        validationResult.isValid && !validationResult.hasWarnings -> {
            Triple(Icons.Default.CheckCircle, Color(0xFF4CAF50), "几何体有效")
        }
        validationResult.isValid && validationResult.hasWarnings -> {
            Triple(Icons.Default.Warning, Color(0xFFFF9800), "有效但有${validationResult.warnings.size}个警告")
        }
        else -> {
            Triple(Icons.Default.Error, Color(0xFFF44336), "${validationResult.errors.size}个错误")
        }
    }
    
    Row(
        modifier = modifier
            .clickable { onToggleDetails() }
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = color,
            modifier = Modifier.size(20.dp)
        )
        
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.weight(1f)
        )
        
        if (validationResult.hasErrors || validationResult.hasWarnings) {
            Icon(
                imageVector = Icons.Default.Info,
                contentDescription = "查看详情",
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 验证详细信息
 */
@Composable
private fun ValidationDetails(
    validationResult: GeometryValidator.ValidationResult,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .padding(horizontal = 12.dp, vertical = 8.dp)
            .background(
                MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f),
                RoundedCornerShape(4.dp)
            )
            .padding(8.dp),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // 错误信息
        if (validationResult.errors.isNotEmpty()) {
            Text(
                text = "错误 (${validationResult.errors.size}):",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold,
                color = Color(0xFFF44336)
            )
            
            validationResult.errors.forEach { error ->
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    Text(
                        text = "•",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFFF44336)
                    )
                    Text(
                        text = error.message,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
        
        // 警告信息
        if (validationResult.warnings.isNotEmpty()) {
            if (validationResult.errors.isNotEmpty()) {
                Spacer(modifier = Modifier.height(4.dp))
            }
            
            Text(
                text = "警告 (${validationResult.warnings.size}):",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold,
                color = Color(0xFFFF9800)
            )
            
            validationResult.warnings.forEach { warning ->
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    Text(
                        text = "•",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFFFF9800)
                    )
                    Text(
                        text = warning.message,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
        
        // 元数据信息
        validationResult.metadata?.let { metadata ->
            Spacer(modifier = Modifier.height(4.dp))
            Divider(color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = "几何体信息:",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "面积: ${String.format("%.2f", metadata.totalArea)}m²",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "顶点: ${metadata.totalVertexCount}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            if (metadata.holeCount > 0) {
                Text(
                    text = "孔洞: ${metadata.holeCount}个",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            if (metadata.complexityScore > 0) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text(
                        text = "复杂度: ${String.format("%.0f", metadata.complexityScore)}/100",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    // 复杂度指示器
                    Box(
                        modifier = Modifier
                            .width(40.dp)
                            .height(4.dp)
                            .background(
                                MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                                RoundedCornerShape(2.dp)
                            )
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxHeight()
                                .fillMaxWidth((metadata.complexityScore / 100.0).toFloat().coerceIn(0f, 1f))
                                .background(
                                    when {
                                        metadata.complexityScore < 30 -> Color(0xFF4CAF50)
                                        metadata.complexityScore < 70 -> Color(0xFFFF9800)
                                        else -> Color(0xFFF44336)
                                    },
                                    RoundedCornerShape(2.dp)
                                )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 紧凑的验证状态指示器（仅显示状态图标）
 */
@Composable
fun CompactValidationIndicator(
    validationResult: GeometryValidator.ValidationResult?,
    modifier: Modifier = Modifier
) {
    if (validationResult == null) return
    
    val (icon, color) = when {
        validationResult.isValid && !validationResult.hasWarnings -> {
            Pair(Icons.Default.CheckCircle, Color(0xFF4CAF50))
        }
        validationResult.isValid && validationResult.hasWarnings -> {
            Pair(Icons.Default.Warning, Color(0xFFFF9800))
        }
        else -> {
            Pair(Icons.Default.Error, Color(0xFFF44336))
        }
    }
    
    Icon(
        imageVector = icon,
        contentDescription = "验证状态",
        tint = color,
        modifier = modifier.size(16.dp)
    )
} 