package cn.agrolinking.wmst.ui.screens.records

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Camera
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.NoteAdd
import androidx.compose.material.icons.filled.Report
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExtendedFloatingActionButton
import androidx.compose.material3.FilterChip
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cn.agrolinking.wmst.ui.theme.WmstTheme

/**
 * 记录页面 - 作业记录、巡田记录、问题上报
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RecordsScreen(
    modifier: Modifier = Modifier
) {
    var selectedFilter by remember { mutableStateOf("全部") }
    val filters = listOf("全部", "作业记录", "巡田记录", "问题上报")
    
    Scaffold(
        modifier = modifier,
        floatingActionButton = {
            ExtendedFloatingActionButton(
                onClick = { /* TODO: 新建记录 */ },
                containerColor = MaterialTheme.colorScheme.primary,
                contentColor = Color.White
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "新建记录"
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("新建记录")
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // 页面标题
            Text(
                text = "我的记录",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 筛选器
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                filters.forEach { filter ->
                    FilterChip(
                        onClick = { selectedFilter = filter },
                        label = { Text(filter) },
                        selected = selectedFilter == filter
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 记录列表
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(getSampleRecords().filter { record ->
                    selectedFilter == "全部" || record.type == selectedFilter
                }) { record ->
                    RecordCard(record = record)
                }
            }
        }
    }
}

@Composable
private fun RecordCard(
    record: Record
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 记录标题和类型
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = record.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.weight(1f)
                )
                RecordTypeChip(type = record.type)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 记录内容
            Text(
                text = record.content,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 记录信息
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.LocationOn,
                    contentDescription = "位置",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = record.location,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Icon(
                    imageVector = Icons.Default.Schedule,
                    contentDescription = "时间",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = record.timestamp,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            // 附件信息
            if (record.attachments.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "附件：",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                    record.attachments.forEach { attachment ->
                        AttachmentChip(attachment = attachment)
                        Spacer(modifier = Modifier.width(4.dp))
                    }
                }
            }
        }
    }
}

@Composable
private fun RecordTypeChip(type: String) {
    val (color, icon) = when (type) {
        "作业记录" -> Pair(Color(0xFF4CAF50), Icons.Default.NoteAdd)
        "巡田记录" -> Pair(Color(0xFF2196F3), Icons.Default.LocationOn)
        "问题上报" -> Pair(Color(0xFFFF5722), Icons.Default.Report)
        else -> Pair(MaterialTheme.colorScheme.outline, Icons.Default.NoteAdd)
    }
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = type,
                tint = color,
                modifier = Modifier.size(12.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = type,
                style = MaterialTheme.typography.labelSmall,
                color = color,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun AttachmentChip(attachment: String) {
    val (icon, color) = when (attachment) {
        "照片" -> Pair(Icons.Default.Camera, Color(0xFF4CAF50))
        "语音" -> Pair(Icons.Default.Mic, Color(0xFF2196F3))
        "问题" -> Pair(Icons.Default.Warning, Color(0xFFFF5722))
        else -> Pair(Icons.Default.NoteAdd, MaterialTheme.colorScheme.outline)
    }
    
    Icon(
        imageVector = icon,
        contentDescription = attachment,
        tint = color,
        modifier = Modifier.size(16.dp)
    )
}

// 示例数据
data class Record(
    val id: String,
    val title: String,
    val content: String,
    val location: String,
    val timestamp: String,
    val type: String,
    val attachments: List<String>
)

private fun getSampleRecords(): List<Record> {
    return listOf(
        Record(
            id = "1",
            title = "东区玉米田除草完成",
            content = "已完成东区A-15地块的除草作业，共清除杂草约200平方米，幼苗生长良好",
            location = "东区A-15地块",
            timestamp = "今天 15:30",
            type = "作业记录",
            attachments = listOf("照片")
        ),
        Record(
            id = "2",
            title = "南区水稻田病虫害发现",
            content = "在南区B-08地块发现稻飞虱，建议及时喷洒农药防治",
            location = "南区B-08地块",
            timestamp = "今天 10:15",
            type = "问题上报",
            attachments = listOf("照片", "语音")
        ),
        Record(
            id = "3",
            title = "西区大豆田巡查",
            content = "西区C-22地块大豆长势良好，土壤湿度适中，无明显病虫害",
            location = "西区C-22地块",
            timestamp = "昨天 16:45",
            type = "巡田记录",
            attachments = listOf("照片")
        ),
        Record(
            id = "4",
            title = "北区小麦收割作业",
            content = "北区D-05地块小麦收割完成，产量预估每亩450公斤，质量良好",
            location = "北区D-05地块",
            timestamp = "昨天 18:00",
            type = "作业记录",
            attachments = listOf("照片", "语音")
        ),
        Record(
            id = "5",
            title = "灌溉设备异常",
            content = "中心区域3号灌溉设备出现漏水问题，需要维修",
            location = "中心区域",
            timestamp = "前天 14:20",
            type = "问题上报",
            attachments = listOf("照片", "问题")
        )
    )
}

@Preview(showBackground = true)
@Composable
fun RecordsScreenPreview() {
    WmstTheme {
        RecordsScreen()
    }
} 