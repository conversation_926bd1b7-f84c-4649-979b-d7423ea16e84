package cn.agrolinking.wmst.ui.screens.map

import android.util.Log
import androidx.compose.animation.*
import androidx.compose.foundation.layout.*

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import cn.agrolinking.wmst.map.manager.LayerCompositeManager
import cn.agrolinking.wmst.map.manager.TileSourceManager
import cn.agrolinking.wmst.map.model.LayerType
import cn.agrolinking.wmst.map.util.CoordinateSystemValidator
import cn.agrolinking.wmst.ui.screens.map.components.*
import cn.agrolinking.wmst.map.drawing.DrawingMode
import cn.agrolinking.wmst.map.drawing.DrawingModeManager
import cn.agrolinking.wmst.map.validation.GeometryRepairer

@Composable
fun MapScreen(
    modifier: Modifier = Modifier,
    viewModel: MapViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    
    // 通过Hilt注入依赖
    val tileSourceManager: TileSourceManager = viewModel.tileSourceManager
    val layerCompositeManager: LayerCompositeManager = viewModel.layerCompositeManager
    
    // 使用新的统一状态
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    // 创建定位管理器
    val locationManager = remember { LocationManager(context, viewModel.scope) }
    val locationState by locationManager.locationState.collectAsStateWithLifecycle()
    
    // 验证相关状态
    var showValidationDialog by remember { mutableStateOf(false) }
    val coordinateValidator = remember { CoordinateSystemValidator(tileSourceManager) }
    
    // 修复控件状态
    var showRepairPanel by remember { mutableStateOf(false) }
    
    // 从ViewModel获取绘制状态
    val drawingState = uiState.drawingState
    
    // 检查是否需要显示修复指示器
    val needsRepair = viewModel.needsRepair()
    val repairSuggestions = remember(drawingState.workingLandPlot) {
        if (needsRepair) viewModel.getRepairSuggestions() else emptyList()
    }
    
    // 添加状态变化日志
    LaunchedEffect(drawingState.isActive) {
        Log.d("MapScreen", "绘制状态变化 - isActive: ${drawingState.isActive}, mode: ${drawingState.mode}")
    }
    
    // 构建多图层配置
    val layerConfiguration = remember(uiState.selectedBaseLayer, uiState.selectedOverlayLayer) {
        LayerConfiguration(
            baseLayerSourceId = uiState.selectedBaseLayer?.id,
            overlayLayers = uiState.selectedOverlayLayer?.let { overlay ->
                listOf(
                    OverlayLayerConfig(
                        sourceId = overlay.id,
                        layerType = LayerType.ROAD_NETWORK,
                        opacity = 0.8f
                    )
                )
            } ?: emptyList()
        )
    }

    Box(modifier = modifier.fillMaxSize()) {
        MapView(
            modifier = Modifier.fillMaxSize(),
            layerConfiguration = if (uiState.isTileSourcesLoaded) layerConfiguration else null,
            layerCompositeManager = if (uiState.isTileSourcesLoaded) layerCompositeManager else null,
            locationManager = locationManager,
            mapUiState = uiState,
            onMapEvent = viewModel::onEvent,
            isDrawingMode = drawingState.isActive,
            onMapPositionChanged = { lat, lng, zoom ->
                viewModel.updateMapPosition(lat, lng, zoom)
            },
            onDrawingCompleted = { _ ->
                // 绘制完成后自动退出绘制模式
                viewModel.onToolChanged(MapTool.VIEW)
                // TODO: 处理绘制结果，比如保存到数据库或显示结果对话框
            }
        )
        
        // 左上角坐标信息面板
        CoordinateInfoPanel(
            longitude = uiState.longitude,
            latitude = uiState.latitude,
            zoomLevel = uiState.zoomLevel.toInt(),
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(start = 20.dp, top = 60.dp)
        )
        
        // 右上角工具按钮区
        Column(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = 100.dp, end = 20.dp),
            horizontalAlignment = Alignment.End,
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            LayerControlButton(
                onClick = { 
                    if (uiState.showLayerPanel) {
                        viewModel.hideLayerPanel()
                    } else {
                        viewModel.showLayerPanel()
                    }
                },
                isExpanded = uiState.showLayerPanel
            )
            DrawToolButton(
                currentTool = uiState.currentTool,
                onToolChanged = viewModel::onToolChanged
            )
            
            // 修复控件快速访问按钮 - 仅在有地块需要修复时显示
            if (needsRepair) {
                CompactRepairIndicator(
                    needsRepair = needsRepair,
                    repairableCount = viewModel.getRepairableIssueCount(),
                    onQuickRepair = { 
                        viewModel.autoRepairCurrentLandPlot(GeometryRepairer.RepairStrategy.DEFAULT)
                        showRepairPanel = !showRepairPanel 
                    }
                )
            }
            
            // 坐标系验证按钮 (已隐藏)
            // ValidationButton(
            //     onClick = { showValidationDialog = true }
            // )
        }

        // 右下角定位按钮 - 动态调整位置以避开操作栏
        LocationButton(
            onLocationClick = { locationManager.locateToMyPosition() },
            isLoading = locationState == LocationState.LOADING,
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(
                    end = 20.dp, 
                    bottom = if (drawingState.isActive || drawingState.isDragMode || showRepairPanel) 160.dp else 76.dp // 操作栏或修复面板显示时浮在上方
                )
        )

        // 图层控制面板
        AnimatedVisibility(
            visible = uiState.showLayerPanel,
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(end = 84.dp, top = 100.dp),
            enter = slideInHorizontally(initialOffsetX = { it }) + fadeIn(),
            exit = slideOutHorizontally(targetOffsetX = { it }) + fadeOut()
        ) {
            LayerControlPanel(
                uiState = uiState,
                onEvent = viewModel::onEvent
            )
        }
        
        // 修复控件面板
        AnimatedVisibility(
            visible = showRepairPanel,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(
                    start = 16.dp, 
                    end = 16.dp, 
                    bottom = 100.dp
                ),
            enter = slideInVertically(initialOffsetY = { it }) + fadeIn(),
            exit = slideOutVertically(targetOffsetY = { it }) + fadeOut()
        ) {
            RepairControls(
                suggestions = repairSuggestions,
                onQuickRepair = {
                    viewModel.autoRepairCurrentLandPlot(GeometryRepairer.RepairStrategy.DEFAULT)
                },
                onAggressiveRepair = {
                    viewModel.autoRepairCurrentLandPlot(GeometryRepairer.RepairStrategy.AGGRESSIVE)
                },
                onApplySpecificRepair = { repairType ->
                    viewModel.applySpecificRepair(repairType)
                },
                needsRepair = needsRepair,
                repairableIssueCount = viewModel.getRepairableIssueCount()
            )
        }
        
        // 状态文本显示 - 在绘制模式或拖拽模式时显示
        AnimatedVisibility(
            visible = drawingState.isActive || drawingState.isDragMode,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(top = 80.dp),
            enter = slideInVertically(initialOffsetY = { -it }) + fadeIn(),
            exit = slideOutVertically(targetOffsetY = { -it }) + fadeOut()
        ) {
            Card(
                modifier = Modifier.padding(horizontal = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                    contentColor = MaterialTheme.colorScheme.onSurface
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Text(
                    text = drawingState.statusText.ifEmpty { 
                        when {
                            drawingState.isActive && drawingState.workingLandPlot == null -> "开始绘制地块"
                            drawingState.isDragMode -> "拖拽模式 - 可拖拽顶点调整形状"
                            else -> "绘制模式"
                        }
                    },
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
        
        // 绘制操作栏 - 显示条件扩展到包括拖拽模式
        AnimatedVisibility(
            visible = (drawingState.isActive || drawingState.isDragMode) && !showRepairPanel,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(
                    start = 16.dp, 
                    end = 16.dp, 
                    bottom = 100.dp // 底部导航栏上方
                ),
            enter = slideInVertically(initialOffsetY = { it }) + fadeIn(),
            exit = slideOutVertically(targetOffsetY = { it }) + fadeOut()
        ) {
            // 调试UI传递的状态
            Log.d("MapScreen", "=== 绘制操作栏状态 ===")
            Log.d("MapScreen", "UI传递状态: canUndo=${drawingState.canUndo}, canClear=${drawingState.canClear}")
            Log.d("MapScreen", "当前环大小: ${drawingState.currentRing.size}")
            Log.d("MapScreen", "绘制活跃: ${drawingState.isActive}")
            Log.d("MapScreen", "状态文本: ${uiState.statusText}")
            
            DrawingOperationBar(
                canUndo = drawingState.canUndo,
                canClear = drawingState.canClear,
                onUndo = viewModel::undoLastPoint,
                onClear = viewModel::clearCurrentDrawing,
                onClose = { 
                    // 关闭并清理所有绘制数据（无论是绘制模式还是拖拽模式）
                    viewModel.closeAndClearDrawing()
                    Log.d("MapScreen", "🧹 用户点击关闭，清理所有绘制数据")
                },
                // 不显示完成按钮，让用户直接长按进入拖拽模式
                showFinishButton = false
            )
        }
        
        // 坐标系验证对话框
        if (showValidationDialog) {
            CoordinateValidationDialog(
                onDismiss = { showValidationDialog = false },
                onStartValidation = {
                    coordinateValidator.performFullValidation()
                }
            )
        }
    }
}

