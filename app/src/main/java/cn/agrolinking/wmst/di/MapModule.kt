package cn.agrolinking.wmst.di

import android.content.Context
import cn.agrolinking.wmst.map.manager.LocationManager
import cn.agrolinking.wmst.map.manager.TileSourceManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object MapModule {
    
    @Provides
    @Singleton
    fun provideTileSourceManager(
        @ApplicationContext context: Context
    ): TileSourceManager {
        return TileSourceManager(context)
    }
    
    @Provides
    @Singleton
    fun provideLocationManager(
        @ApplicationContext context: Context
    ): LocationManager {
        return LocationManager(context)
    }
} 