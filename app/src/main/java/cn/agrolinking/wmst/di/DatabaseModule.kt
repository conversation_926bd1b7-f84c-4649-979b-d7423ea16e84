package cn.agrolinking.wmst.di

import android.content.Context
import androidx.room.Room
import cn.agrolinking.wmst.database.UsersDao
import cn.agrolinking.wmst.database.AppDatabase
import cn.agrolinking.wmst.database.dao.LandPlotDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@InstallIn(SingletonComponent::class)
@Module
object DatabaseModule {
    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext appContext: Context): AppDatabase {
        return Room.databaseBuilder(
            appContext,
            AppDatabase::class.java,
            "WMST_Database"  // 更改数据库名称以反映新的架构
        ).fallbackToDestructiveMigration().build()
    }

    @Provides
    fun provideUsersDao(appDatabase: AppDatabase): UsersDao {
        return appDatabase.usersDao
    }

    @Provides
    fun provideLandPlotDao(appDatabase: AppDatabase): LandPlotDao {
        return appDatabase.landPlotDao
    }
}