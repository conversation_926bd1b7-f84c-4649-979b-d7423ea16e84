package cn.agrolinking.wmst.map.drawing

import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.Style
import timber.log.Timber

class DrawingModeManager(
    private val mapView: MapView,
    private val mapLibreMap: MapLibreMap
) {
    private var currentMode = DrawingMode.NONE
    private var drawingHandler: DrawingHandler? = null
    private val listeners = mutableListOf<(DrawingMode) -> Unit>()
    
    fun setDrawingMode(mode: DrawingMode, style: Style) {
        Timber.d("切换绘制模式: $currentMode -> $mode")
        
        // 清理当前绘制状态
        drawingHandler?.cleanup()
        
        currentMode = mode
        drawingHandler = when (mode) {
            DrawingMode.MANUAL_AREA -> ManualDrawingHandler(mapView, mapLibreMap, style)
            DrawingMode.GPS_AREA -> GpsTrackingHandler(mapView, mapLibreMap, style)
            DrawingMode.MANUAL_LINE -> ManualDrawingHandler(mapView, mapLibreMap, style)
            DrawingMode.GPS_LINE -> GpsTrackingHandler(mapView, mapLibreMap, style)
            DrawingMode.POINT_ANNOTATION -> ManualDrawingHandler(mapView, mapLibreMap, style)
            DrawingMode.NONE -> null
        }
        
        notifyModeChanged(mode)
    }
    
    fun getCurrentMode() = currentMode
    fun getCurrentHandler() = drawingHandler
    
    fun addModeChangeListener(listener: (DrawingMode) -> Unit) {
        listeners.add(listener)
    }
    
    fun removeModeChangeListener(listener: (DrawingMode) -> Unit) {
        listeners.remove(listener)
    }
    
    private fun notifyModeChanged(mode: DrawingMode) {
        listeners.forEach { it(mode) }
    }
    
    fun cleanup() {
        drawingHandler?.cleanup()
        drawingHandler = null
        currentMode = DrawingMode.NONE
        listeners.clear()
    }
} 