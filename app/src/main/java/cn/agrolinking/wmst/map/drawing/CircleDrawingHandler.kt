package cn.agrolinking.wmst.map.drawing

import cn.agrolinking.wmst.domain.LandPlot
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.Style
import timber.log.Timber

class CircleDrawingHandler(
    private val mapView: MapView,
    private val mapLibreMap: MapLibreMap,
    private val style: Style
) : DrawingHandler {
    
    override var onDrawingCompleted: ((LandPlot) -> Unit)? = null
    override var onDrawingCancelled: (() -> Unit)? = null
    
    init {
        Timber.d("CircleDrawingHandler 初始化完成 - 占位符实现")
    }
    
    override fun cleanup() {
        Timber.d("CircleDrawingHandler 清理完成")
    }
    
    override fun undo(): Boolean {
        Timber.d("CircleDrawingHandler 撤销操作")
        return false
    }
    
    override fun clear() {
        Timber.d("CircleDrawingHandler 清除操作")
    }
    
    override fun canUndo(): Boolean = false
} 