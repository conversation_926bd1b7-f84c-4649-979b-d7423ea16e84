package cn.agrolinking.wmst.map.validation

import android.util.Log
import cn.agrolinking.wmst.domain.drawing.LandPlotInProgress
import cn.agrolinking.wmst.domain.drawing.PolygonInProgress
import cn.agrolinking.wmst.map.util.GeometryUtils
import org.maplibre.android.geometry.LatLng
import kotlin.math.*

/**
 * 几何体验证引擎
 * 提供完整的GIS标准几何体验证功能
 */
class GeometryValidator {
    
    companion object {
        private const val TAG = "GeometryValidator"
        
        // 验证配置常量
        const val MIN_POLYGON_AREA_SQM = 1.0                    // 最小多边形面积（平方米）
        const val MAX_POLYGON_AREA_SQM = 1000000.0              // 最大多边形面积（平方米）
        const val MIN_VERTEX_COUNT = 3                          // 最小顶点数
        const val MAX_VERTEX_COUNT = 1000                       // 最大顶点数
        const val MIN_EDGE_LENGTH_M = 0.1                       // 最小边长（米）
        const val MAX_EDGE_LENGTH_M = 10000.0                   // 最大边长（米）
        const val MIN_ANGLE_DEGREES = 10.0                      // 最小内角（度）
        const val MAX_ASPECT_RATIO = 100.0                      // 最大长宽比
        const val COORDINATE_PRECISION_DIGITS = 6               // 坐标精度位数
        const val DUPLICATE_VERTEX_TOLERANCE_M = 0.01           // 重复顶点容差（米）
    }
    
    /**
     * 验证结果
     */
    data class ValidationResult(
        val isValid: Boolean,
        val errors: List<ValidationError> = emptyList(),
        val warnings: List<ValidationWarning> = emptyList(),
        val metadata: ValidationMetadata? = null
    ) {
        /**
         * 是否有错误
         */
        val hasErrors: Boolean get() = errors.isNotEmpty()
        
        /**
         * 是否有警告
         */
        val hasWarnings: Boolean get() = warnings.isNotEmpty()
        
        /**
         * 验证通过且无警告
         */
        val isPerfect: Boolean get() = isValid && !hasWarnings
        
        /**
         * 获取所有问题的摘要文本
         */
        val summaryText: String
            get() = buildString {
                if (hasErrors) {
                    appendLine("错误 (${errors.size}):")
                    errors.forEach { error ->
                        appendLine("  • ${error.message}")
                    }
                }
                if (hasWarnings) {
                    if (hasErrors) appendLine()
                    appendLine("警告 (${warnings.size}):")
                    warnings.forEach { warning ->
                        appendLine("  • ${warning.message}")
                    }
                }
            }
    }
    
    /**
     * 验证错误
     */
    data class ValidationError(
        val type: ErrorType,
        val message: String,
        val location: LatLng? = null,
        val polygonIndex: Int? = null,
        val ringIndex: Int? = null,
        val vertexIndex: Int? = null
    )
    
    /**
     * 验证警告
     */
    data class ValidationWarning(
        val type: WarningType,
        val message: String,
        val location: LatLng? = null,
        val polygonIndex: Int? = null,
        val ringIndex: Int? = null,
        val vertexIndex: Int? = null
    )
    
    /**
     * 验证元数据
     */
    data class ValidationMetadata(
        val totalArea: Double,                    // 总面积（平方米）
        val totalPerimeter: Double,               // 总周长（米）
        val polygonCount: Int,                    // 多边形数量
        val totalVertexCount: Int,                // 总顶点数
        val holeCount: Int,                       // 孔洞数量
        val complexityScore: Double,              // 复杂度评分 (0-100)
        val coordinateBounds: CoordinateBounds,   // 坐标边界
        val aspectRatio: Double                   // 长宽比
    )
    
    /**
     * 坐标边界
     */
    data class CoordinateBounds(
        val minLat: Double,
        val maxLat: Double,
        val minLng: Double,
        val maxLng: Double
    ) {
        val latSpan: Double get() = maxLat - minLat
        val lngSpan: Double get() = maxLng - minLng
        val center: LatLng get() = LatLng((minLat + maxLat) / 2, (minLng + maxLng) / 2)
    }
    
    /**
     * 错误类型
     */
    enum class ErrorType {
        INSUFFICIENT_VERTICES,      // 顶点不足
        EXCESSIVE_VERTICES,         // 顶点过多
        AREA_TOO_SMALL,            // 面积过小
        AREA_TOO_LARGE,            // 面积过大
        SELF_INTERSECTION,         // 自相交
        INVALID_TOPOLOGY,          // 拓扑无效
        HOLE_OUTSIDE_BOUNDARY,     // 孔洞超出边界
        HOLE_INTERSECTION,         // 孔洞相交
        INVALID_COORDINATES,       // 坐标无效
        EDGE_TOO_SHORT,           // 边长过短
        EDGE_TOO_LONG,            // 边长过长
        ACUTE_ANGLE,              // 锐角过小
        DUPLICATE_VERTICES,       // 重复顶点
        COLLINEAR_VERTICES,       // 共线顶点
        INVALID_RING_ORIENTATION  // 环方向错误
    }
    
    /**
     * 警告类型
     */
    enum class WarningType {
        HIGH_VERTEX_DENSITY,      // 顶点密度过高
        LOW_VERTEX_DENSITY,       // 顶点密度过低
        HIGH_ASPECT_RATIO,        // 长宽比过大
        SMALL_ANGLE,              // 角度较小
        COMPLEX_GEOMETRY,         // 几何体复杂
        PRECISION_LOSS,           // 精度损失
        NEAR_DUPLICATE_VERTICES,  // 接近重复的顶点
        IRREGULAR_SHAPE,          // 形状不规则
        EXCESSIVE_HOLES           // 孔洞过多
    }
    
    /**
     * 验证完整的地块
     */
    fun validateLandPlot(landPlot: LandPlotInProgress): ValidationResult {
        Log.d(TAG, "开始验证地块: ${landPlot.polygons.size}个多边形")
        
        val errors = mutableListOf<ValidationError>()
        val warnings = mutableListOf<ValidationWarning>()
        
        if (landPlot.polygons.isEmpty()) {
            errors.add(ValidationError(
                ErrorType.INSUFFICIENT_VERTICES,
                "地块不能为空"
            ))
            return ValidationResult(false, errors, warnings)
        }
        
        // 验证每个多边形
        landPlot.polygons.forEachIndexed { polygonIndex, polygon ->
            val polygonResult = validatePolygon(polygon, polygonIndex)
            errors.addAll(polygonResult.errors)
            warnings.addAll(polygonResult.warnings)
        }
        
        // 验证多边形间的关系
        validatePolygonRelationships(landPlot).let { relationshipResult ->
            errors.addAll(relationshipResult.errors)
            warnings.addAll(relationshipResult.warnings)
        }
        
        // 计算验证元数据
        val metadata = if (errors.isEmpty()) {
            calculateValidationMetadata(landPlot)
        } else null
        
        val isValid = errors.isEmpty()
        Log.d(TAG, "验证完成: ${if (isValid) "通过" else "失败"}, 错误${errors.size}个, 警告${warnings.size}个")
        
        return ValidationResult(isValid, errors, warnings, metadata)
    }
    
    /**
     * 验证单个多边形
     */
    fun validatePolygon(polygon: PolygonInProgress, polygonIndex: Int = 0): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        val warnings = mutableListOf<ValidationWarning>()
        
        // 验证外边界
        validateRing(polygon.exterior, polygonIndex, 0, true).let { result ->
            errors.addAll(result.errors)
            warnings.addAll(result.warnings)
        }
        
        // 验证孔洞
        polygon.holes.forEachIndexed { holeIndex, hole ->
            validateRing(hole, polygonIndex, holeIndex + 1, false).let { result ->
                errors.addAll(result.errors)
                warnings.addAll(result.warnings)
            }
            
            // 验证孔洞是否在外边界内
            validateHoleInBoundary(hole, polygon.exterior, polygonIndex, holeIndex).let { result ->
                errors.addAll(result.errors)
                warnings.addAll(result.warnings)
            }
        }
        
        // 验证孔洞间的关系
        validateHoleRelationships(polygon.holes, polygonIndex).let { result ->
            errors.addAll(result.errors)
            warnings.addAll(result.warnings)
        }
        
        return ValidationResult(errors.isEmpty(), errors, warnings)
    }
    
    /**
     * 验证环（外边界或孔洞）
     */
    private fun validateRing(
        ring: List<LatLng>, 
        polygonIndex: Int, 
        ringIndex: Int, 
        isExterior: Boolean
    ): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        val warnings = mutableListOf<ValidationWarning>()
        
        // 1. 检查顶点数量
        when {
            ring.size < MIN_VERTEX_COUNT -> {
                errors.add(ValidationError(
                    ErrorType.INSUFFICIENT_VERTICES,
                    "${if (isExterior) "外边界" else "孔洞"}顶点数不足: ${ring.size} < $MIN_VERTEX_COUNT",
                    polygonIndex = polygonIndex,
                    ringIndex = ringIndex
                ))
            }
            ring.size > MAX_VERTEX_COUNT -> {
                errors.add(ValidationError(
                    ErrorType.EXCESSIVE_VERTICES,
                    "${if (isExterior) "外边界" else "孔洞"}顶点数过多: ${ring.size} > $MAX_VERTEX_COUNT",
                    polygonIndex = polygonIndex,
                    ringIndex = ringIndex
                ))
            }
            ring.size > 100 -> {
                warnings.add(ValidationWarning(
                    WarningType.HIGH_VERTEX_DENSITY,
                    "${if (isExterior) "外边界" else "孔洞"}顶点数较多: ${ring.size}",
                    polygonIndex = polygonIndex,
                    ringIndex = ringIndex
                ))
            }
        }
        
        if (ring.size < MIN_VERTEX_COUNT) {
            return ValidationResult(false, errors, warnings)
        }
        
        // 2. 检查坐标有效性
        ring.forEachIndexed { vertexIndex, vertex ->
            if (!isValidCoordinate(vertex)) {
                errors.add(ValidationError(
                    ErrorType.INVALID_COORDINATES,
                    "坐标无效: (${vertex.latitude}, ${vertex.longitude})",
                    location = vertex,
                    polygonIndex = polygonIndex,
                    ringIndex = ringIndex,
                    vertexIndex = vertexIndex
                ))
            }
        }
        
        // 3. 检查重复顶点
        validateDuplicateVertices(ring, polygonIndex, ringIndex).let { result ->
            errors.addAll(result.errors)
            warnings.addAll(result.warnings)
        }
        
        // 4. 检查边长
        validateEdgeLengths(ring, polygonIndex, ringIndex).let { result ->
            errors.addAll(result.errors)
            warnings.addAll(result.warnings)
        }
        
        // 5. 检查自相交
        if (GeometryUtils.hasLinesIntersect(ring)) {
            errors.add(ValidationError(
                ErrorType.SELF_INTERSECTION,
                "${if (isExterior) "外边界" else "孔洞"}存在自相交",
                polygonIndex = polygonIndex,
                ringIndex = ringIndex
            ))
        }
        
        // 6. 检查面积
        if (isExterior) {
            validateArea(ring, polygonIndex, ringIndex).let { result ->
                errors.addAll(result.errors)
                warnings.addAll(result.warnings)
            }
        }
        
        // 7. 检查角度
        validateAngles(ring, polygonIndex, ringIndex).let { result ->
            errors.addAll(result.errors)
            warnings.addAll(result.warnings)
        }
        
        // 8. 检查环方向（外边界应逆时针，孔洞应顺时针）
        validateRingOrientation(ring, isExterior, polygonIndex, ringIndex).let { result ->
            errors.addAll(result.errors)
            warnings.addAll(result.warnings)
        }
        
        return ValidationResult(errors.isEmpty(), errors, warnings)
    }
    
    // ========== 具体验证方法 ==========
    
    /**
     * 检查坐标有效性
     */
    private fun isValidCoordinate(coord: LatLng): Boolean {
        return coord.latitude.isFinite() && 
               coord.longitude.isFinite() &&
               coord.latitude in -90.0..90.0 &&
               coord.longitude in -180.0..180.0
    }
    
    /**
     * 验证重复顶点
     */
    private fun validateDuplicateVertices(
        ring: List<LatLng>,
        polygonIndex: Int,
        ringIndex: Int
    ): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        val warnings = mutableListOf<ValidationWarning>()
        
        for (i in ring.indices) {
            for (j in i + 1 until ring.size) {
                val distance = GeometryUtils.distanceBetween(ring[i], ring[j])
                
                when {
                    distance < DUPLICATE_VERTEX_TOLERANCE_M -> {
                        errors.add(ValidationError(
                            ErrorType.DUPLICATE_VERTICES,
                            "重复顶点: 顶点$i 和 顶点$j 距离仅${String.format("%.3f", distance)}米",
                            location = ring[i],
                            polygonIndex = polygonIndex,
                            ringIndex = ringIndex,
                            vertexIndex = i
                        ))
                    }
                    distance < 1.0 -> {
                        warnings.add(ValidationWarning(
                            WarningType.NEAR_DUPLICATE_VERTICES,
                            "顶点过于接近: 顶点$i 和 顶点$j 距离${String.format("%.2f", distance)}米",
                            location = ring[i],
                            polygonIndex = polygonIndex,
                            ringIndex = ringIndex,
                            vertexIndex = i
                        ))
                    }
                }
            }
        }
        
        return ValidationResult(errors.isEmpty(), errors, warnings)
    }
    
    /**
     * 验证边长
     */
    private fun validateEdgeLengths(
        ring: List<LatLng>,
        polygonIndex: Int,
        ringIndex: Int
    ): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        val warnings = mutableListOf<ValidationWarning>()
        
        for (i in ring.indices) {
            val nextIndex = (i + 1) % ring.size
            val edgeLength = GeometryUtils.distanceBetween(ring[i], ring[nextIndex])
            
            when {
                edgeLength < MIN_EDGE_LENGTH_M -> {
                    errors.add(ValidationError(
                        ErrorType.EDGE_TOO_SHORT,
                        "边长过短: 边$i 长度${String.format("%.3f", edgeLength)}米 < $MIN_EDGE_LENGTH_M 米",
                        location = ring[i],
                        polygonIndex = polygonIndex,
                        ringIndex = ringIndex,
                        vertexIndex = i
                    ))
                }
                edgeLength > MAX_EDGE_LENGTH_M -> {
                    errors.add(ValidationError(
                        ErrorType.EDGE_TOO_LONG,
                        "边长过长: 边$i 长度${String.format("%.1f", edgeLength)}米 > $MAX_EDGE_LENGTH_M 米",
                        location = ring[i],
                        polygonIndex = polygonIndex,
                        ringIndex = ringIndex,
                        vertexIndex = i
                    ))
                }
            }
        }
        
        return ValidationResult(errors.isEmpty(), errors, warnings)
    }
    
    /**
     * 验证面积
     */
    private fun validateArea(
        ring: List<LatLng>,
        polygonIndex: Int,
        ringIndex: Int
    ): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        val warnings = mutableListOf<ValidationWarning>()
        
        val area = GeometryUtils.calculatePolygonArea(ring)
        
        when {
            area < MIN_POLYGON_AREA_SQM -> {
                errors.add(ValidationError(
                    ErrorType.AREA_TOO_SMALL,
                    "面积过小: ${String.format("%.2f", area)}平方米 < $MIN_POLYGON_AREA_SQM 平方米",
                    polygonIndex = polygonIndex,
                    ringIndex = ringIndex
                ))
            }
            area > MAX_POLYGON_AREA_SQM -> {
                errors.add(ValidationError(
                    ErrorType.AREA_TOO_LARGE,
                    "面积过大: ${String.format("%.0f", area)}平方米 > $MAX_POLYGON_AREA_SQM 平方米",
                    polygonIndex = polygonIndex,
                    ringIndex = ringIndex
                ))
            }
        }
        
        return ValidationResult(errors.isEmpty(), errors, warnings)
    }
    
    /**
     * 验证角度
     */
    private fun validateAngles(
        ring: List<LatLng>,
        polygonIndex: Int,
        ringIndex: Int
    ): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        val warnings = mutableListOf<ValidationWarning>()
        
        if (ring.size < 3) return ValidationResult(true, errors, warnings)
        
        for (i in ring.indices) {
            val prevIndex = if (i == 0) ring.size - 1 else i - 1
            val nextIndex = (i + 1) % ring.size
            
            val angle = calculateInteriorAngle(ring[prevIndex], ring[i], ring[nextIndex])
            val angleDegrees = Math.toDegrees(angle)
            
            when {
                angleDegrees < MIN_ANGLE_DEGREES -> {
                    errors.add(ValidationError(
                        ErrorType.ACUTE_ANGLE,
                        "内角过小: 顶点$i 角度${String.format("%.1f", angleDegrees)}° < $MIN_ANGLE_DEGREES°",
                        location = ring[i],
                        polygonIndex = polygonIndex,
                        ringIndex = ringIndex,
                        vertexIndex = i
                    ))
                }
                angleDegrees < 30.0 -> {
                    warnings.add(ValidationWarning(
                        WarningType.SMALL_ANGLE,
                        "内角较小: 顶点$i 角度${String.format("%.1f", angleDegrees)}°",
                        location = ring[i],
                        polygonIndex = polygonIndex,
                        ringIndex = ringIndex,
                        vertexIndex = i
                    ))
                }
            }
            
            // 检查共线顶点
            if (angleDegrees < 1.0 || abs(angleDegrees - 180.0) < 1.0) {
                errors.add(ValidationError(
                    ErrorType.COLLINEAR_VERTICES,
                    "顶点共线: 顶点$i 角度${String.format("%.2f", angleDegrees)}°",
                    location = ring[i],
                    polygonIndex = polygonIndex,
                    ringIndex = ringIndex,
                    vertexIndex = i
                ))
            }
        }
        
        return ValidationResult(errors.isEmpty(), errors, warnings)
    }
    
    /**
     * 验证环方向
     */
    private fun validateRingOrientation(
        ring: List<LatLng>,
        isExterior: Boolean,
        polygonIndex: Int,
        ringIndex: Int
    ): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        val warnings = mutableListOf<ValidationWarning>()
        
        val isCounterClockwise = calculateRingOrientation(ring)
        val expectedOrientation = isExterior // 外边界应逆时针，孔洞应顺时针
        
        if (isCounterClockwise != expectedOrientation) {
            warnings.add(ValidationWarning(
                WarningType.IRREGULAR_SHAPE,
                "${if (isExterior) "外边界" else "孔洞"}方向不标准: " +
                "${if (isCounterClockwise) "逆时针" else "顺时针"}",
                polygonIndex = polygonIndex,
                ringIndex = ringIndex
            ))
        }
        
        return ValidationResult(true, errors, warnings)
    }
    
    /**
     * 验证孔洞是否在外边界内
     */
    private fun validateHoleInBoundary(
        hole: List<LatLng>,
        boundary: List<LatLng>,
        polygonIndex: Int,
        holeIndex: Int
    ): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        val warnings = mutableListOf<ValidationWarning>()
        
        // 检查孔洞的每个顶点是否都在外边界内
        hole.forEachIndexed { vertexIndex, vertex ->
            if (!GeometryUtils.isPointInPolygon(vertex, boundary)) {
                errors.add(ValidationError(
                    ErrorType.HOLE_OUTSIDE_BOUNDARY,
                    "孔洞顶点超出外边界: 孔洞$holeIndex 顶点$vertexIndex",
                    location = vertex,
                    polygonIndex = polygonIndex,
                    ringIndex = holeIndex + 1,
                    vertexIndex = vertexIndex
                ))
            }
        }
        
        return ValidationResult(errors.isEmpty(), errors, warnings)
    }
    
    /**
     * 验证孔洞间关系
     */
    private fun validateHoleRelationships(
        holes: List<List<LatLng>>,
        polygonIndex: Int
    ): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        val warnings = mutableListOf<ValidationWarning>()
        
        // 检查孔洞数量
        if (holes.size > 10) {
            warnings.add(ValidationWarning(
                WarningType.EXCESSIVE_HOLES,
                "孔洞数量较多: ${holes.size}个",
                polygonIndex = polygonIndex
            ))
        }
        
        // 检查孔洞间是否相交
        for (i in holes.indices) {
            for (j in i + 1 until holes.size) {
                if (doHolesIntersect(holes[i], holes[j])) {
                    errors.add(ValidationError(
                        ErrorType.HOLE_INTERSECTION,
                        "孔洞相交: 孔洞$i 与 孔洞$j",
                        polygonIndex = polygonIndex,
                        ringIndex = i + 1
                    ))
                }
            }
        }
        
        return ValidationResult(errors.isEmpty(), errors, warnings)
    }
    
    /**
     * 验证多边形间关系
     */
    private fun validatePolygonRelationships(landPlot: LandPlotInProgress): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        val warnings = mutableListOf<ValidationWarning>()
        
        // 目前简单实现，主要检查是否有重叠
        // 可以根据需要扩展更复杂的拓扑关系检查
        
        if (landPlot.polygons.size > 5) {
            warnings.add(ValidationWarning(
                WarningType.COMPLEX_GEOMETRY,
                "多边形数量较多: ${landPlot.polygons.size}个"
            ))
        }
        
        return ValidationResult(errors.isEmpty(), errors, warnings)
    }
    
    /**
     * 计算验证元数据
     */
    private fun calculateValidationMetadata(landPlot: LandPlotInProgress): ValidationMetadata {
        var totalArea = 0.0
        var totalPerimeter = 0.0
        var totalVertexCount = 0
        var holeCount = 0
        
        var minLat = Double.MAX_VALUE
        var maxLat = Double.MIN_VALUE
        var minLng = Double.MAX_VALUE
        var maxLng = Double.MIN_VALUE
        
        landPlot.polygons.forEach { polygon ->
            // 计算外边界
            totalArea += GeometryUtils.calculatePolygonArea(polygon.exterior)
            totalPerimeter += calculatePerimeter(polygon.exterior)
            totalVertexCount += polygon.exterior.size
            
            // 更新边界
            polygon.exterior.forEach { vertex ->
                minLat = minOf(minLat, vertex.latitude)
                maxLat = maxOf(maxLat, vertex.latitude)
                minLng = minOf(minLng, vertex.longitude)
                maxLng = maxOf(maxLng, vertex.longitude)
            }
            
            // 计算孔洞
            polygon.holes.forEach { hole ->
                totalArea -= GeometryUtils.calculatePolygonArea(hole) // 孔洞减少面积
                totalPerimeter += calculatePerimeter(hole)
                totalVertexCount += hole.size
                holeCount++
                
                hole.forEach { vertex ->
                    minLat = minOf(minLat, vertex.latitude)
                    maxLat = maxOf(maxLat, vertex.latitude)
                    minLng = minOf(minLng, vertex.longitude)
                    maxLng = maxOf(maxLng, vertex.longitude)
                }
            }
        }
        
        val bounds = CoordinateBounds(minLat, maxLat, minLng, maxLng)
        val aspectRatio = bounds.lngSpan / bounds.latSpan
        
        // 计算复杂度评分 (0-100)
        val complexityScore = calculateComplexityScore(
            landPlot.polygons.size,
            totalVertexCount,
            holeCount,
            aspectRatio
        )
        
        return ValidationMetadata(
            totalArea = totalArea,
            totalPerimeter = totalPerimeter,
            polygonCount = landPlot.polygons.size,
            totalVertexCount = totalVertexCount,
            holeCount = holeCount,
            complexityScore = complexityScore,
            coordinateBounds = bounds,
            aspectRatio = aspectRatio
        )
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 计算内角
     */
    private fun calculateInteriorAngle(p1: LatLng, vertex: LatLng, p2: LatLng): Double {
        // 将地理坐标转换为平面坐标进行角度计算
        val dx1 = p1.longitude - vertex.longitude
        val dy1 = p1.latitude - vertex.latitude
        val dx2 = p2.longitude - vertex.longitude
        val dy2 = p2.latitude - vertex.latitude
        
        val angle1 = atan2(dy1, dx1)
        val angle2 = atan2(dy2, dx2)
        
        var angle = abs(angle2 - angle1)
        if (angle > PI) {
            angle = 2 * PI - angle
        }
        
        return angle
    }
    
    /**
     * 计算环的方向（顺时针或逆时针）
     */
    private fun calculateRingOrientation(ring: List<LatLng>): Boolean {
        if (ring.size < 3) return true
        
        // 使用Shoelace公式计算有符号面积
        var signedArea = 0.0
        for (i in ring.indices) {
            val j = (i + 1) % ring.size
            signedArea += (ring[j].longitude - ring[i].longitude) * (ring[j].latitude + ring[i].latitude)
        }
        
        return signedArea < 0 // 负值表示逆时针
    }
    
    /**
     * 计算周长
     */
    private fun calculatePerimeter(ring: List<LatLng>): Double {
        if (ring.size < 2) return 0.0
        
        var perimeter = 0.0
        for (i in ring.indices) {
            val nextIndex = (i + 1) % ring.size
            perimeter += GeometryUtils.distanceBetween(ring[i], ring[nextIndex])
        }
        
        return perimeter
    }
    
    /**
     * 检查两个孔洞是否相交
     */
    private fun doHolesIntersect(hole1: List<LatLng>, hole2: List<LatLng>): Boolean {
        // 简化实现：检查是否有顶点互相包含
        for (vertex in hole1) {
            if (GeometryUtils.isPointInPolygon(vertex, hole2)) {
                return true
            }
        }
        
        for (vertex in hole2) {
            if (GeometryUtils.isPointInPolygon(vertex, hole1)) {
                return true
            }
        }
        
        // 可以进一步检查边的相交，这里简化处理
        return false
    }
    
    /**
     * 计算复杂度评分
     */
    private fun calculateComplexityScore(
        polygonCount: Int,
        vertexCount: Int,
        holeCount: Int,
        aspectRatio: Double
    ): Double {
        var score = 0.0
        
        // 多边形数量贡献 (0-30)
        score += when {
            polygonCount == 1 -> 0.0
            polygonCount <= 3 -> 10.0
            polygonCount <= 5 -> 20.0
            else -> 30.0
        }
        
        // 顶点数量贡献 (0-30)
        score += when {
            vertexCount <= 10 -> 0.0
            vertexCount <= 50 -> 10.0
            vertexCount <= 100 -> 20.0
            else -> 30.0
        }
        
        // 孔洞数量贡献 (0-25)
        score += when {
            holeCount == 0 -> 0.0
            holeCount <= 2 -> 10.0
            holeCount <= 5 -> 15.0
            else -> 25.0
        }
        
        // 长宽比贡献 (0-15)
        score += when {
            aspectRatio <= 2.0 -> 0.0
            aspectRatio <= 5.0 -> 5.0
            aspectRatio <= 10.0 -> 10.0
            else -> 15.0
        }
        
        return score.coerceIn(0.0, 100.0)
    }
    
    /**
     * 快速验证（仅检查关键错误）
     */
    fun quickValidate(landPlot: LandPlotInProgress): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        
        if (landPlot.polygons.isEmpty()) {
            errors.add(ValidationError(
                ErrorType.INSUFFICIENT_VERTICES,
                "地块不能为空"
            ))
            return ValidationResult(false, errors)
        }
        
        landPlot.polygons.forEachIndexed { polygonIndex, polygon ->
            if (polygon.exterior.size < MIN_VERTEX_COUNT) {
                errors.add(ValidationError(
                    ErrorType.INSUFFICIENT_VERTICES,
                    "多边形$polygonIndex 顶点数不足: ${polygon.exterior.size}",
                    polygonIndex = polygonIndex
                ))
            }
            
            if (GeometryUtils.hasLinesIntersect(polygon.exterior)) {
                errors.add(ValidationError(
                    ErrorType.SELF_INTERSECTION,
                    "多边形$polygonIndex 存在自相交",
                    polygonIndex = polygonIndex
                ))
            }
            
            val area = GeometryUtils.calculatePolygonArea(polygon.exterior)
            if (area < MIN_POLYGON_AREA_SQM) {
                errors.add(ValidationError(
                    ErrorType.AREA_TOO_SMALL,
                    "多边形$polygonIndex 面积过小: ${String.format("%.2f", area)}平方米",
                    polygonIndex = polygonIndex
                ))
            }
        }
        
        return ValidationResult(errors.isEmpty(), errors)
    }
    
    /**
     * 验证单个坐标点
     */
    fun validateCoordinate(coord: LatLng): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        
        if (!isValidCoordinate(coord)) {
            errors.add(ValidationError(
                ErrorType.INVALID_COORDINATES,
                "坐标无效: (${coord.latitude}, ${coord.longitude})",
                location = coord
            ))
        }
        
        return ValidationResult(errors.isEmpty(), errors)
    }
} 