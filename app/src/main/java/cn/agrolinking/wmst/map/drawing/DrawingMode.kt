package cn.agrolinking.wmst.map.drawing

/**
 * 绘制模式枚举
 * 设计思路：圈地、划线、打点三种基本绘制类型
 */
enum class DrawingMode(
    val displayName: String,
    val description: String
) {
    NONE("无", "不绘制"),
    
    // 圈地模式
    MANUAL_AREA("手工圈地", "手动点击绘制多边形区域"),
    GPS_AREA("GPS圈地", "使用GPS轨迹绘制区域边界"),
    
    // 划线模式  
    MANUAL_LINE("手工划线", "手动点击绘制路径线条"),
    GPS_LINE("GPS划线", "使用GPS轨迹绘制路径"),
    
    // 打点模式
    POINT_ANNOTATION("打点", "标注位置点");
    
    /**
     * 判断是否为圈地模式
     */
    fun isAreaMode(): Boolean = this in listOf(MANUAL_AREA, GPS_AREA)
    
    /**
     * 判断是否为划线模式
     */
    fun isLineMode(): Boolean = this in listOf(MANUAL_LINE, GPS_LINE)
    
    /**
     * 判断是否为打点模式
     */
    fun isPointMode(): Boolean = this == POINT_ANNOTATION
    
    /**
     * 判断是否为GPS模式
     */
    fun isGpsMode(): Boolean = this in listOf(GPS_AREA, GPS_LINE)
    
    /**
     * 判断是否为手工模式
     */
    fun isManualMode(): Boolean = this in listOf(MANUAL_AREA, MANUAL_LINE, POINT_ANNOTATION)
    
    /**
     * 获取绘制类型
     */
    fun getDrawingType(): DrawingType = when {
        isAreaMode() -> DrawingType.AREA
        isLineMode() -> DrawingType.LINE
        isPointMode() -> DrawingType.POINT
        else -> DrawingType.NONE
    }
}

/**
 * 绘制类型分类
 */
enum class DrawingType(val displayName: String) {
    NONE("无"),
    AREA("圈地"),
    LINE("划线"),
    POINT("打点")
} 