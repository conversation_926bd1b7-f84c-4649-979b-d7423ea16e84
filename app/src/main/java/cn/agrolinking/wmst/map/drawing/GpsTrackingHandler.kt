package cn.agrolinking.wmst.map.drawing

import cn.agrolinking.wmst.domain.LandPlot
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.Style
import timber.log.Timber

class GpsTrackingHandler(
    private val mapView: MapView,
    private val mapLibreMap: MapLibreMap,
    private val style: Style
) : DrawingHandler {
    
    override var onDrawingCompleted: ((LandPlot) -> Unit)? = null
    override var onDrawingCancelled: (() -> Unit)? = null
    
    init {
        Timber.d("GpsTrackingHandler 初始化完成 - 占位符实现")
    }
    
    override fun cleanup() {
        Timber.d("GpsTrackingHandler 清理完成")
    }
    
    override fun undo(): Boolean {
        Timber.d("GpsTrackingHandler 撤销操作")
        return false
    }
    
    override fun clear() {
        Timber.d("GpsTrackingHandler 清除操作")
    }
    
    override fun canUndo(): Boolean = false
} 