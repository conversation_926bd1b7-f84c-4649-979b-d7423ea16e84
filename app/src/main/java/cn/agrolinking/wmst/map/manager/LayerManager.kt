package cn.agrolinking.wmst.map.manager

import android.util.Log
import cn.agrolinking.wmst.map.model.LayerType
import org.maplibre.android.style.layers.Layer
import org.maplibre.android.style.layers.Property
import org.maplibre.android.style.layers.PropertyFactory
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.Style

/**
 * 图层管理器
 * 负责管理地图上的各种图层，包括添加、删除、可见性控制和顺序管理
 */
class LayerManager {
    
    companion object {
        private const val TAG = "LayerManager"
    }
    
    private var mapLibreMap: MapLibreMap? = null
    private var style: Style? = null
    private val activeLayers = mutableMapOf<LayerType, Layer>()
    private val layerVisibility = mutableMapOf<LayerType, Boolean>()
    
    /**
     * 初始化图层管理器
     */
    fun initialize(mapLibreMap: MapLibreMap, style: Style) {
        this.mapLibreMap = mapLibreMap
        this.style = style
    }
    
    /**
     * 添加图层到地图样式中
     */
    fun addLayer(style: Style, layerType: LayerType, layer: Layer): Boolean {
        return try {
            Log.d(TAG, "添加图层: ${layerType.displayName} (${layerType.id})")
            
            // 按 zIndex 顺序添加图层
            val beforeLayerId = findBeforeLayerId(layerType)
            
            if (beforeLayerId != null) {
                style.addLayerBelow(layer, beforeLayerId)
                Log.d(TAG, "图层 ${layerType.id} 添加在 $beforeLayerId 之下")
            } else {
                style.addLayer(layer)
                Log.d(TAG, "图层 ${layerType.id} 添加到顶层")
            }
            
            activeLayers[layerType] = layer
            layerVisibility[layerType] = true
            
            Log.d(TAG, "图层 ${layerType.displayName} 添加成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "添加图层失败: ${layerType.displayName}", e)
            false
        }
    }
    
    /**
     * 从地图样式中移除图层
     */
    fun removeLayer(style: Style, layerType: LayerType): Boolean {
        return try {
            activeLayers[layerType]?.let { layer ->
                Log.d(TAG, "移除图层: ${layerType.displayName} (${layerType.id})")
                
                style.removeLayer(layer.id)
                activeLayers.remove(layerType)
                layerVisibility.remove(layerType)
                
                Log.d(TAG, "图层 ${layerType.displayName} 移除成功")
                true
            } ?: run {
                Log.w(TAG, "尝试移除不存在的图层: ${layerType.displayName}")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "移除图层失败: ${layerType.displayName}", e)
            false
        }
    }
    
    /**
     * 设置图层可见性
     */
    fun setLayerVisibility(layerType: LayerType, visible: Boolean) {
        try {
            activeLayers[layerType]?.let { layer ->
                Log.d(TAG, "设置图层 ${layerType.displayName} 可见性: $visible")
                
                layer.setProperties(
                    PropertyFactory.visibility(
                        if (visible) Property.VISIBLE else Property.NONE
                    )
                )
                layerVisibility[layerType] = visible
                
                Log.d(TAG, "图层 ${layerType.displayName} 可见性设置成功")
            } ?: run {
                Log.w(TAG, "尝试设置不存在图层的可见性: ${layerType.displayName}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置图层可见性失败: ${layerType.displayName}", e)
        }
    }
    
    /**
     * 获取图层是否可见
     */
    fun isLayerVisible(layerType: LayerType): Boolean {
        return layerVisibility[layerType] ?: false
    }
    
    /**
     * 获取所有活跃的图层类型
     */
    fun getActiveLayerTypes(): Set<LayerType> = activeLayers.keys
    
    /**
     * 获取指定类型的图层
     */
    fun getLayer(layerType: LayerType): Layer? = activeLayers[layerType]
    
    /**
     * 检查图层是否存在
     */
    fun hasLayer(layerType: LayerType): Boolean = activeLayers.containsKey(layerType)
    
    /**
     * 切换图层可见性
     */
    fun toggleLayerVisibility(layerType: LayerType) {
        val currentVisibility = isLayerVisible(layerType)
        setLayerVisibility(layerType, !currentVisibility)
    }
    
    /**
     * 显示指定类别的所有图层
     */
    fun showLayerCategory(category: List<LayerType>) {
        category.forEach { layerType ->
            if (hasLayer(layerType)) {
                setLayerVisibility(layerType, true)
            }
        }
    }
    
    /**
     * 隐藏指定类别的所有图层
     */
    fun hideLayerCategory(category: List<LayerType>) {
        category.forEach { layerType ->
            if (hasLayer(layerType)) {
                setLayerVisibility(layerType, false)
            }
        }
    }
    
    /**
     * 清理所有图层
     */
    fun clearAllLayers(style: Style) {
        Log.d(TAG, "清理所有图层")
        
        val layersToRemove = activeLayers.keys.toList()
        layersToRemove.forEach { layerType ->
            removeLayer(style, layerType)
        }
        
        Log.d(TAG, "所有图层清理完成")
    }
    
    /**
     * 根据zIndex找到应该插入的位置（在哪个图层之前）
     */
    private fun findBeforeLayerId(layerType: LayerType): String? {
        // 找到第一个 zIndex 大于当前图层的图层ID
        return activeLayers.entries
            .filter { it.key.zIndex > layerType.zIndex }
            .minByOrNull { it.key.zIndex }
            ?.value?.id
    }
    
    /**
     * 获取图层管理器状态信息
     */
    fun getLayerInfo(): String {
        return buildString {
            append("LayerManager状态:\n")
            append("- 活跃图层数: ${activeLayers.size}\n")
            append("- 可见图层数: ${layerVisibility.values.count { it }}\n")
            append("- 图层详情:\n")
            
            LayerType.getAllSorted().forEach { layerType ->
                val exists = hasLayer(layerType)
                val visible = isLayerVisible(layerType)
                append("  * ${layerType.displayName}: ")
                append(if (exists) "存在" else "不存在")
                if (exists) {
                    append(", ${if (visible) "可见" else "隐藏"}")
                }
                append("\n")
            }
        }
    }
} 