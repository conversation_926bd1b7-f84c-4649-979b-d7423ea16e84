package cn.agrolinking.wmst.map.config

/**
 * 地图配置常量
 */
object MapConfig {
    /**
     * 天地图配置
     */
    object TianDiTu {
        const val API_KEY = "3e052e21dfd0684da0862867aaf5145e"
        
        // 瓦片服务器域名
        const val TILE_DOMAIN = "t{0-7}.tianditu.gov.cn"
        
        // 卫星影像瓦片
        const val SATELLITE_TILE_URL = "https://$TILE_DOMAIN/img_w/wmts?" +
            "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&" +
            "TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=$API_KEY"
        
        // 路网瓦片
        const val ROAD_TILE_URL = "https://$TILE_DOMAIN/cia_w/wmts?" +
            "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&" +
            "TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=$API_KEY"
    }

    /**
     * 高德地图配置
     */
    object AMap {
        const val API_KEY = "91a8d4cde0a464af42024f4a24fc9318"
        
        // 瓦片服务器域名
        const val TILE_DOMAIN = "wprd0{1-4}.is.autonavi.com"
        
        // 卫星影像瓦片
        const val SATELLITE_TILE_URL = "https://$TILE_DOMAIN/appmaptile?style=6&x={x}&y={y}&z={z}&key=$API_KEY"
        
        // 路网瓦片
        const val ROAD_TILE_URL = "https://$TILE_DOMAIN/appmaptile?style=8&x={x}&y={y}&z={z}&key=$API_KEY"
        
        // 标准地图瓦片
        const val STANDARD_TILE_URL = "https://$TILE_DOMAIN/appmaptile?style=7&x={x}&y={y}&z={z}&key=$API_KEY"
    }
} 