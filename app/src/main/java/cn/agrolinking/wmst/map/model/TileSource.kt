package cn.agrolinking.wmst.map.model

import com.google.gson.annotations.SerializedName

/**
 * 瓦片源类型
 */
enum class TileSourceType {
    RASTER,    // 栅格瓦片
    VECTOR     // 矢量瓦片
}

/**
 * 认证类型
 */
enum class AuthType {
    NONE,           // 无需认证
    API_KEY,        // API密钥认证
    BEARER_TOKEN,   // Bearer Token认证
    CUSTOM_HEADER,  // 自定义请求头认证
    URL_PARAMETER   // URL参数认证
}

/**
 * 坐标系类型
 */
enum class CoordinateSystem {
    @SerializedName("WGS84") WGS84,           // GPS 原始坐标系
    @SerializedName("GCJ02") GCJ02,           // 火星坐标系 (中国国测局标准)
    @SerializedName("BD09") BD09,             // 百度坐标系
    @SerializedName("EPSG:3857") EPSG_3857,   // Web Mercator (基于WGS84)
    @SerializedName("EPSG:4326") EPSG_4326    // WGS84 地理坐标系
}

/**
 * 瓦片图层源
 */
data class TileSource(
    @SerializedName("id") val id: String,                              // 瓦片源唯一标识
    @SerializedName("name") val name: String,                            // 瓦片源名称
    @SerializedName("type") val type: TileSourceType,                    // 瓦片类型
    @SerializedName("url") val url: String,                             // 瓦片URL模板
    @SerializedName("minZoom") val minZoom: Int = 0,                        // 最小缩放级别
    @SerializedName("maxZoom") val maxZoom: Int = 18,                       // 最大缩放级别
    @SerializedName("tileSize") val tileSize: Int = 256,                     // 瓦片大小
    @SerializedName("attribution") val attribution: String = "",                // 版权信息
    @SerializedName("headers") val headers: Map<String, String>? = null, // 自定义请求头
    @SerializedName("requiresAuth") val requiresAuth: Boolean = false,           // 是否需要认证
    @SerializedName("authType") val authType: AuthType = AuthType.NONE,      // 认证类型
    @SerializedName("isActive") val isActive: Boolean = true,                 // 是否激活
    @SerializedName("scheme") val scheme: String = "xyz",
    @SerializedName("opacity") val opacity: Float = 1.0f,
    @SerializedName("zIndex") val zIndex: Int = 0,
    @SerializedName("isVisible") val isVisible: Boolean = true,
    @SerializedName("groupId") val groupId: String? = null,
    
    // 坐标系相关字段
    @SerializedName("coordinateSystem") val coordinateSystem: CoordinateSystem = CoordinateSystem.GCJ02,  // 坐标系类型
    @SerializedName("needsConversion") val needsConversion: Boolean = false,                              // 是否需要坐标转换
    @SerializedName("targetSystem") val targetSystem: CoordinateSystem = CoordinateSystem.GCJ02          // 目标坐标系
) 