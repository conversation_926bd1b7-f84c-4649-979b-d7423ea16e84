package cn.agrolinking.wmst.map.util

import org.maplibre.android.geometry.LatLng
import kotlin.math.*

/**
 * 几何计算工具类
 * 用于地图拖拽功能的数学计算
 */
object GeometryUtils {
    
    /**
     * 计算屏幕坐标中两点之间的距离
     */
    fun distanceBetweenScreenPoints(x1: Float, y1: Float, x2: Float, y2: Float): Float {
        val dx = x2 - x1
        val dy = y2 - y1
        return sqrt(dx * dx + dy * dy)
    }
    
    /**
     * 计算两个地理坐标之间的距离（米）
     */
    fun distanceBetweenLatLng(point1: LatLng, point2: LatLng): Double {
        val earthRadius = 6371000.0 // 地球半径（米）
        
        val lat1Rad = Math.toRadians(point1.latitude)
        val lat2Rad = Math.toRadians(point2.latitude)
        val deltaLatRad = Math.toRadians(point2.latitude - point1.latitude)
        val deltaLngRad = Math.toRadians(point2.longitude - point1.longitude)
        
        val a = sin(deltaLatRad / 2) * sin(deltaLatRad / 2) +
                cos(lat1Rad) * cos(lat2Rad) *
                sin(deltaLngRad / 2) * sin(deltaLngRad / 2)
        val c = 2 * atan2(sqrt(a), sqrt(1 - a))
        
        return earthRadius * c
    }
    
    /**
     * 计算点到线段的最短距离（屏幕坐标）
     * @param px 点的x坐标
     * @param py 点的y坐标  
     * @param x1 线段起点x坐标
     * @param y1 线段起点y坐标
     * @param x2 线段终点x坐标
     * @param y2 线段终点y坐标
     * @return 距离值
     */
    fun distancePointToLineSegment(
        px: Float, py: Float,
        x1: Float, y1: Float,
        x2: Float, y2: Float
    ): Float {
        val A = px - x1
        val B = py - y1
        val C = x2 - x1
        val D = y2 - y1
        
        val dot = A * C + B * D
        val lenSq = C * C + D * D
        
        if (lenSq == 0f) {
            // 线段退化为点
            return distanceBetweenScreenPoints(px, py, x1, y1)
        }
        
        var param = dot / lenSq
        
        val xx: Float
        val yy: Float
        
        if (param < 0) {
            xx = x1
            yy = y1
        } else if (param > 1) {
            xx = x2
            yy = y2
        } else {
            xx = x1 + param * C
            yy = y1 + param * D
        }
        
        return distanceBetweenScreenPoints(px, py, xx, yy)
    }
    
    /**
     * 计算线段的中点
     */
    fun midPoint(point1: LatLng, point2: LatLng): LatLng {
        val lat = (point1.latitude + point2.latitude) / 2
        val lng = (point1.longitude + point2.longitude) / 2
        return LatLng(lat, lng)
    }
    
    /**
     * 计算点到线段的最近点在线段上的参数位置
     * @return 0.0表示在起点，1.0表示在终点，0.5表示在中点
     */
    fun getClosestPointParameter(
        px: Float, py: Float,
        x1: Float, y1: Float,
        x2: Float, y2: Float
    ): Float {
        val A = px - x1
        val B = py - y1
        val C = x2 - x1
        val D = y2 - y1
        
        val dot = A * C + B * D
        val lenSq = C * C + D * D
        
        if (lenSq == 0f) return 0f
        
        return (dot / lenSq).coerceIn(0f, 1f)
    }
    
    /**
     * 根据参数位置计算线段上的点
     */
    fun getPointOnLineSegment(
        point1: LatLng, 
        point2: LatLng, 
        parameter: Float
    ): LatLng {
        val lat = point1.latitude + parameter * (point2.latitude - point1.latitude)
        val lng = point1.longitude + parameter * (point2.longitude - point1.longitude)
        return LatLng(lat, lng)
    }
    
    /**
     * 计算两个地理坐标之间的距离（米）- 别名方法
     */
    fun distanceBetween(point1: LatLng, point2: LatLng): Double {
        return distanceBetweenLatLng(point1, point2)
    }
    
    /**
     * 计算多边形面积（平方米）
     * 使用Shoelace公式
     */
    fun calculatePolygonArea(vertices: List<LatLng>): Double {
        if (vertices.size < 3) return 0.0
        
        var area = 0.0
        val n = vertices.size
        
        for (i in 0 until n) {
            val j = (i + 1) % n
            val xi = vertices[i].longitude
            val yi = vertices[i].latitude
            val xj = vertices[j].longitude
            val yj = vertices[j].latitude
            
            area += xi * yj - xj * yi
        }
        
        area = abs(area) / 2.0
        
        // 转换为平方米（近似）
        // 1度纬度 ≈ 111000米
        // 1度经度 ≈ 111000 * cos(纬度) 米
        val avgLat = vertices.map { it.latitude }.average()
        val latToMeter = 111000.0
        val lngToMeter = 111000.0 * cos(Math.toRadians(avgLat))
        
        return area * latToMeter * lngToMeter
    }
    
    /**
     * 检查多边形是否有自相交线段
     */
    fun hasLinesIntersect(vertices: List<LatLng>): Boolean {
        if (vertices.size < 4) return false
        
        val edges = mutableListOf<Pair<LatLng, LatLng>>()
        for (i in vertices.indices) {
            val start = vertices[i]
            val end = vertices[(i + 1) % vertices.size]
            edges.add(Pair(start, end))
        }
        
        // 检查每条边是否与其他边相交（除了相邻边）
        for (i in edges.indices) {
            for (j in i + 2 until edges.size) {
                // 跳过最后一条边与第一条边的检查（它们共享顶点）
                if (i == 0 && j == edges.size - 1) continue
                
                if (doLinesIntersect(edges[i].first, edges[i].second, edges[j].first, edges[j].second)) {
                    return true
                }
            }
        }
        
        return false
    }
    
    /**
     * 检查两条线段是否相交
     */
    private fun doLinesIntersect(p1: LatLng, q1: LatLng, p2: LatLng, q2: LatLng): Boolean {
        fun orientation(p: LatLng, q: LatLng, r: LatLng): Int {
            val value = (q.latitude - p.latitude) * (r.longitude - q.longitude) - 
                       (q.longitude - p.longitude) * (r.latitude - q.latitude)
            return when {
                abs(value) < 1e-10 -> 0 // 共线
                value > 0 -> 1 // 顺时针
                else -> 2 // 逆时针
            }
        }
        
        fun onSegment(p: LatLng, q: LatLng, r: LatLng): Boolean {
            return q.longitude <= maxOf(p.longitude, r.longitude) &&
                   q.longitude >= minOf(p.longitude, r.longitude) &&
                   q.latitude <= maxOf(p.latitude, r.latitude) &&
                   q.latitude >= minOf(p.latitude, r.latitude)
        }
        
        val o1 = orientation(p1, q1, p2)
        val o2 = orientation(p1, q1, q2)
        val o3 = orientation(p2, q2, p1)
        val o4 = orientation(p2, q2, q1)
        
        // 一般情况
        if (o1 != o2 && o3 != o4) return true
        
        // 特殊情况：点在线段上
        if (o1 == 0 && onSegment(p1, p2, q1)) return true
        if (o2 == 0 && onSegment(p1, q2, q1)) return true
        if (o3 == 0 && onSegment(p2, p1, q2)) return true
        if (o4 == 0 && onSegment(p2, q1, q2)) return true
        
        return false
    }
    
    /**
     * 检查点是否在多边形内部（射线投射算法）
     */
    fun isPointInPolygon(point: LatLng, polygon: List<LatLng>): Boolean {
        if (polygon.size < 3) return false
        
        var intersections = 0
        val n = polygon.size
        
        for (i in 0 until n) {
            val p1 = polygon[i]
            val p2 = polygon[(i + 1) % n]
            
            // 检查水平射线是否与边相交
            if (((p1.latitude > point.latitude) != (p2.latitude > point.latitude)) &&
                (point.longitude < (p2.longitude - p1.longitude) * (point.latitude - p1.latitude) / 
                (p2.latitude - p1.latitude) + p1.longitude)) {
                intersections++
            }
        }
        
        return intersections % 2 == 1
    }
} 