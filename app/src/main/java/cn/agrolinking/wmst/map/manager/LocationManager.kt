package cn.agrolinking.wmst.map.manager

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.os.Looper
import android.util.Log
import androidx.core.content.ContextCompat
import com.google.android.gms.location.*
import com.google.android.gms.tasks.Task
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 位置管理器
 * 负责处理位置权限、位置更新和位置服务管理
 */
@Singleton
class LocationManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "LocationManager"
        private const val LOCATION_UPDATE_INTERVAL = 5000L // 5秒
        private const val LOCATION_FASTEST_INTERVAL = 2000L // 2秒
        private const val LOCATION_DISPLACEMENT = 10f // 10米
    }

    private val fusedLocationClient: FusedLocationProviderClient = 
        LocationServices.getFusedLocationProviderClient(context)
    
    private val locationRequest = LocationRequest.Builder(
        Priority.PRIORITY_HIGH_ACCURACY,
        LOCATION_UPDATE_INTERVAL
    ).apply {
        setMinUpdateIntervalMillis(LOCATION_FASTEST_INTERVAL)
        setMinUpdateDistanceMeters(LOCATION_DISPLACEMENT)
    }.build()

    // 位置状态
    private val _currentLocation = MutableStateFlow<Location?>(null)
    val currentLocation: StateFlow<Location?> = _currentLocation.asStateFlow()

    private val _isLocationEnabled = MutableStateFlow(false)
    val isLocationEnabled: StateFlow<Boolean> = _isLocationEnabled.asStateFlow()

    private val _locationPermissionGranted = MutableStateFlow(false)
    val locationPermissionGranted: StateFlow<Boolean> = _locationPermissionGranted.asStateFlow()

    // 位置更新回调
    private val locationCallback = object : LocationCallback() {
        override fun onLocationResult(locationResult: LocationResult) {
            locationResult.lastLocation?.let { location ->
                Log.d(TAG, "位置更新: ${location.latitude}, ${location.longitude}")
                _currentLocation.value = location
            }
        }

        override fun onLocationAvailability(locationAvailability: LocationAvailability) {
            val isAvailable = locationAvailability.isLocationAvailable
            Log.d(TAG, "位置服务可用性: $isAvailable")
            _isLocationEnabled.value = isAvailable
        }
    }

    /**
     * 检查位置权限
     */
    fun checkLocationPermission(): Boolean {
        val fineLocationGranted = ContextCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        
        val coarseLocationGranted = ContextCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        val granted = fineLocationGranted || coarseLocationGranted
        _locationPermissionGranted.value = granted
        
        Log.d(TAG, "位置权限检查: $granted (精确: $fineLocationGranted, 粗略: $coarseLocationGranted)")
        return granted
    }

    /**
     * 获取需要请求的位置权限
     */
    fun getRequiredPermissions(): Array<String> {
        return arrayOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )
    }

    /**
     * 开始位置更新
     */
    fun startLocationUpdates() {
        if (!checkLocationPermission()) {
            Log.w(TAG, "位置权限未授权，无法开始位置更新")
            return
        }

        try {
            fusedLocationClient.requestLocationUpdates(
                locationRequest,
                locationCallback,
                Looper.getMainLooper()
            ).addOnSuccessListener {
                Log.d(TAG, "位置更新请求成功")
                _isLocationEnabled.value = true
            }.addOnFailureListener { exception ->
                Log.e(TAG, "位置更新请求失败", exception)
                _isLocationEnabled.value = false
            }
        } catch (securityException: SecurityException) {
            Log.e(TAG, "位置权限异常", securityException)
        }
    }

    /**
     * 停止位置更新
     */
    fun stopLocationUpdates() {
        fusedLocationClient.removeLocationUpdates(locationCallback)
        _isLocationEnabled.value = false
        Log.d(TAG, "已停止位置更新")
    }

    /**
     * 获取最后已知位置
     */
    fun getLastKnownLocation(callback: (Location?) -> Unit) {
        if (!checkLocationPermission()) {
            Log.w(TAG, "位置权限未授权，无法获取最后位置")
            callback(null)
            return
        }

        try {
            fusedLocationClient.lastLocation
                .addOnSuccessListener { location ->
                    Log.d(TAG, "获取最后位置成功: $location")
                    _currentLocation.value = location
                    callback(location)
                }
                .addOnFailureListener { exception ->
                    Log.e(TAG, "获取最后位置失败", exception)
                    callback(null)
                }
        } catch (securityException: SecurityException) {
            Log.e(TAG, "位置权限异常", securityException)
            callback(null)
        }
    }

    /**
     * 权限授权后的处理
     */
    fun onPermissionGranted() {
        Log.d(TAG, "位置权限已授权")
        _locationPermissionGranted.value = true
        
        // 获取最后已知位置
        getLastKnownLocation { location ->
            if (location != null) {
                Log.d(TAG, "获取到最后已知位置，开始位置更新")
                startLocationUpdates()
            } else {
                Log.d(TAG, "无最后已知位置，直接开始位置更新")
                startLocationUpdates()
            }
        }
    }

    /**
     * 权限被拒绝后的处理
     */
    fun onPermissionDenied() {
        Log.w(TAG, "位置权限被拒绝")
        _locationPermissionGranted.value = false
        _isLocationEnabled.value = false
        _currentLocation.value = null
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        stopLocationUpdates()
        Log.d(TAG, "位置管理器资源已清理")
    }
} 