package cn.agrolinking.wmst.map.util

import org.maplibre.android.geometry.LatLng
import kotlin.math.*

/**
 * 地图工具类
 */
object MapUtils {
    
    /**
     * 计算两点之间的距离（米）
     */
    fun calculateDistance(point1: LatLng, point2: LatLng): Double {
        val earthRadius = 6371000.0 // 地球半径（米）
        
        val lat1Rad = Math.toRadians(point1.latitude)
        val lat2Rad = Math.toRadians(point2.latitude)
        val deltaLatRad = Math.toRadians(point2.latitude - point1.latitude)
        val deltaLngRad = Math.toRadians(point2.longitude - point1.longitude)
        
        val a = sin(deltaLatRad / 2).pow(2) + 
                cos(lat1Rad) * cos(lat2Rad) * sin(deltaLngRad / 2).pow(2)
        val c = 2 * atan2(sqrt(a), sqrt(1 - a))
        
        return earthRadius * c
    }
    
    /**
     * 计算多边形面积（平方米）
     */
    fun calculatePolygonArea(points: List<LatLng>): Double {
        if (points.size < 3) return 0.0
        
        // TODO: 实现多边形面积计算
        return 0.0
    }
    
    /**
     * 判断点是否在多边形内
     */
    fun isPointInPolygon(
        @Suppress("UNUSED_PARAMETER") point: LatLng, 
        @Suppress("UNUSED_PARAMETER") polygon: List<LatLng>
    ): Boolean {
        // TODO: 实现点在多边形内判断
        return false
    }
    
    /**
     * 格式化坐标显示
     */
    fun formatCoordinate(latLng: LatLng): String {
        return String.format("%.6f, %.6f", latLng.latitude, latLng.longitude)
    }
} 