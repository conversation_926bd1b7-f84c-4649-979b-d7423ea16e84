package cn.agrolinking.wmst.map.model

/**
 * 地图配置数据模型
 */
data class MapConfig(
    val defaultZoom: Double = 10.0,
    val minZoom: Double = 1.0,
    val maxZoom: Double = 20.0,
    val defaultLatitude: Double = 39.9042,  // 北京坐标
    val defaultLongitude: Double = 116.4074,
    val enableRotation: Boolean = true,
    val enableTilt: Boolean = true,
    val enableZoom: Boolean = true,
    val enableScroll: Boolean = true,
    val styleUrl: String = "https://demotiles.maplibre.org/style.json"
) 