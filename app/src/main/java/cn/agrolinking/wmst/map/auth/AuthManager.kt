package cn.agrolinking.wmst.map.auth

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 认证响应数据模型
 */
data class AuthResponse(
    val success: Boolean,
    val token: String?,
    val expiresIn: Long,
    val refreshToken: String?,
    val errorMessage: String?
)

/**
 * 认证配置
 */
data class AuthConfig(
    val apiKey: String,
    val apiSecret: String? = null,
    val baseUrl: String,
    val tokenEndpoint: String
)

/**
 * 认证提供者接口
 */
interface AuthProvider {
    suspend fun authenticate(config: AuthConfig): AuthResponse
    suspend fun refreshToken(refreshToken: String): AuthResponse
    fun getAuthHeaders(token: String): Map<String, String>
}

/**
 * 认证管理器
 * 负责管理各种认证方式和token生命周期
 */
@Singleton
class AuthManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "AuthManager"
        private const val PREFS_NAME = "auth_prefs"
        private const val TOKEN_REFRESH_THRESHOLD = 300000L // 提前5分钟刷新
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val authProviders = mutableMapOf<String, AuthProvider>()
    private val authListeners = mutableListOf<(String, Boolean) -> Unit>()
    private val refreshJobs = mutableMapOf<String, Job>()
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    init {
        // 注册默认认证提供者
        authProviders["jilin1"] = JilinAuthProvider()
        authProviders["tianditu"] = TiandituAuthProvider()
    }

    /**
     * 执行认证
     */
    suspend fun authenticate(providerId: String, config: AuthConfig): Boolean {
        val provider = authProviders[providerId] ?: run {
            Log.e(TAG, "未找到认证提供者: $providerId")
            return false
        }
        
        return try {
            val response = provider.authenticate(config)
            
            if (response.success && response.token != null) {
                saveAuthToken(providerId, response.token, response.expiresIn, response.refreshToken)
                notifyAuthListeners(providerId, true)
                
                // 启动自动刷新调度器
                startTokenRefreshScheduler(providerId)
                
                Log.i(TAG, "认证成功: $providerId")
                true
            } else {
                Log.e(TAG, "认证失败: $providerId - ${response.errorMessage}")
                notifyAuthListeners(providerId, false)
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "认证异常: $providerId", e)
            notifyAuthListeners(providerId, false)
            false
        }
    }

    /**
     * 确保有效的token
     */
    suspend fun ensureValidToken(providerId: String): String? {
        val currentToken = getValidToken(providerId)
        if (currentToken != null) {
            return currentToken
        }
        
        // Token已过期，尝试刷新
        return refreshTokenIfNeeded(providerId)
    }

    /**
     * 获取认证头
     */
    fun getAuthHeaders(providerId: String): Map<String, String>? {
        val token = getValidToken(providerId) ?: return null
        val provider = authProviders[providerId] ?: return null
        return provider.getAuthHeaders(token)
    }

    /**
     * 检查是否已认证
     */
    fun isAuthenticated(providerId: String): Boolean {
        return getValidToken(providerId) != null
    }

    /**
     * 添加认证状态监听器
     */
    fun addAuthListener(listener: (String, Boolean) -> Unit) {
        authListeners.add(listener)
    }

    /**
     * 移除认证状态监听器
     */
    fun removeAuthListener(listener: (String, Boolean) -> Unit) {
        authListeners.remove(listener)
    }

    /**
     * 登出
     */
    fun logout(providerId: String) {
        clearAuthToken(providerId)
        refreshJobs[providerId]?.cancel()
        refreshJobs.remove(providerId)
        notifyAuthListeners(providerId, false)
        Log.i(TAG, "已登出: $providerId")
    }

    /**
     * 获取认证状态信息
     */
    fun getAuthInfo(): String {
        return buildString {
            append("AuthManager状态:\n")
            append("- 可用提供者: ${authProviders.keys.joinToString()}\n")
            append("- 活跃刷新任务: ${refreshJobs.size}个\n")
            append("认证状态:\n")
            authProviders.keys.forEach { providerId ->
                val isAuth = isAuthenticated(providerId)
                val tokenExpiry = getTokenExpiry(providerId)
                append("  $providerId: ${if (isAuth) "已认证" else "未认证"}")
                if (isAuth && tokenExpiry > 0) {
                    val remaining = (tokenExpiry - System.currentTimeMillis()) / 1000
                    append(" (剩余${remaining}秒)")
                }
                append("\n")
            }
        }
    }

    // =================== 私有方法 ===================

    /**
     * 刷新token
     */
    private suspend fun refreshTokenIfNeeded(providerId: String): String? {
        // 防止重复刷新
        if (refreshJobs.containsKey(providerId)) {
            refreshJobs[providerId]?.join()
            return getValidToken(providerId)
        }
        
        val refreshJob = coroutineScope.launch {
            try {
                val refreshToken = prefs.getString("${providerId}_refresh_token", null)
                if (refreshToken != null) {
                    val provider = authProviders[providerId]
                    val response = provider?.refreshToken(refreshToken)
                    
                    if (response?.success == true && response.token != null) {
                        saveAuthToken(providerId, response.token, response.expiresIn, response.refreshToken)
                        notifyAuthListeners(providerId, true)
                        Log.i(TAG, "Token刷新成功: $providerId")
                    } else {
                        // 刷新失败，需要重新认证
                        clearAuthToken(providerId)
                        notifyAuthListeners(providerId, false)
                        Log.w(TAG, "Token刷新失败: $providerId")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Token刷新异常: $providerId", e)
                notifyAuthListeners(providerId, false)
            } finally {
                refreshJobs.remove(providerId)
            }
        }
        
        refreshJobs[providerId] = refreshJob
        refreshJob.join()
        
        return getValidToken(providerId)
    }

    /**
     * 启动token刷新调度器
     */
    private fun startTokenRefreshScheduler(providerId: String) {
        // 取消现有的调度器
        refreshJobs[providerId]?.cancel()
        
        val job = coroutineScope.launch {
            while (true) {
                delay(60000) // 每分钟检查一次
                
                val expiryTime = prefs.getLong("${providerId}_expiry", 0)
                val currentTime = System.currentTimeMillis()
                
                // 提前刷新token
                if (expiryTime - currentTime < TOKEN_REFRESH_THRESHOLD) {
                    refreshTokenIfNeeded(providerId)
                    break // 刷新后退出循环，由新的调度器接管
                }
            }
        }
        
        refreshJobs[providerId] = job
    }

    /**
     * 保存认证token
     */
    private fun saveAuthToken(providerId: String, token: String, expiresIn: Long, refreshToken: String?) {
        val expiryTime = System.currentTimeMillis() + (expiresIn * 1000)
        prefs.edit()
            .putString("${providerId}_token", token)
            .putLong("${providerId}_expiry", expiryTime)
            .apply {
                if (refreshToken != null) {
                    putString("${providerId}_refresh_token", refreshToken)
                }
            }
            .apply()
    }

    /**
     * 获取有效的token
     */
    private fun getValidToken(providerId: String): String? {
        val token = prefs.getString("${providerId}_token", null)
        val expiryTime = prefs.getLong("${providerId}_expiry", 0)
        
        return if (token != null && System.currentTimeMillis() < expiryTime) {
            token
        } else {
            null
        }
    }

    /**
     * 获取token过期时间
     */
    private fun getTokenExpiry(providerId: String): Long {
        return prefs.getLong("${providerId}_expiry", 0)
    }

    /**
     * 清除认证token
     */
    private fun clearAuthToken(providerId: String) {
        prefs.edit()
            .remove("${providerId}_token")
            .remove("${providerId}_expiry")
            .remove("${providerId}_refresh_token")
            .apply()
    }

    /**
     * 通知认证状态监听器
     */
    private fun notifyAuthListeners(providerId: String, success: Boolean) {
        authListeners.forEach { it(providerId, success) }
    }
} 