package cn.agrolinking.wmst.map.model

/**
 * 图层组类型
 */
enum class LayerGroupType {
    BASE_MAP,    // 底图组
    OVERLAY,     // 叠加层组
    VECTOR,      // 矢量图层组
    CUSTOM       // 自定义图层组
}

/**
 * 图层组
 */
data class LayerGroup(
    val id: String,
    val name: String,
    val type: LayerGroupType,
    val description: String,
    val layers: List<TileSource>,
    val isExpanded: <PERSON>olean = false,
    val isVisible: Boolean = true,
    val order: Int = 0
) 