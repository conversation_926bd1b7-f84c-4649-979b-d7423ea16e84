package cn.agrolinking.wmst.map.manager

import android.util.Log
import cn.agrolinking.wmst.map.model.LayerType
import cn.agrolinking.wmst.map.model.TileSource
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.Style
import org.maplibre.android.style.layers.Property
import org.maplibre.android.style.layers.PropertyFactory
import org.maplibre.android.style.layers.RasterLayer
import org.maplibre.android.style.sources.RasterSource
import org.maplibre.android.style.sources.TileSet
import javax.inject.Inject
import javax.inject.Singleton

private const val TAG = "LayerCompositeManager"

/**
 * 图层组合管理器
 * 负责管理多图层的叠加显示，支持透明度、顺序控制
 */
@Singleton
class LayerCompositeManager @Inject constructor(
    val tileSourceManager: TileSourceManager
) {
    private var mapLibreMap: MapLibreMap? = null
    private var style: Style? = null

    // 当前活跃的图层配置
    private val activeLayerConfigs = mutableMapOf<LayerType, LayerConfig>()
    
    data class LayerConfig(
        val tileSource: TileSource,
        val opacity: Float = 1.0f,
        val isVisible: Boolean = true,
        val layerType: LayerType
    )
    
    /**
     * 初始化管理器
     */
    fun initialize(mapLibreMap: MapLibreMap, style: Style) {
        this.mapLibreMap = mapLibreMap
        this.style = style
        Log.i(TAG, "LayerCompositeManager 初始化完成")
    }
    
    /**
     * 检查Style是否可用
     */
    private fun isStyleReady(): Boolean {
        val currentStyle = style ?: return false
        return currentStyle.isFullyLoaded
    }
    
    /**
     * 设置底图图层（卫星图或标准地图）
     */
    fun setBaseLayer(tileSourceId: String, opacity: Float = 1.0f): Boolean {
        if (!isStyleReady()) {
            Log.w(TAG, "Style未准备就绪，跳过底图设置")
            return false
        }
        
        val tileSource = tileSourceManager.getTileSource(tileSourceId)
        if (tileSource == null) {
            Log.e(TAG, "未找到瓦片源: $tileSourceId")
            return false
        }
        
        return try {
            // 移除现有底图
            removeLayer(LayerType.SATELLITE_BASE)
            
            // 添加新底图
            addLayer(
                LayerConfig(
                    tileSource = tileSource,
                    opacity = opacity,
                    isVisible = true,
                    layerType = LayerType.SATELLITE_BASE
                )
            )
            
            Log.i(TAG, "底图切换成功: ${tileSource.name}")
            
            // 刷新LocationComponent确保可见性
            refreshLocationComponent()
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置底图失败", e)
            false
        }
    }
    
    /**
     * 添加叠加图层（路网、标注等）
     */
    fun addOverlayLayer(
        tileSourceId: String, 
        layerType: LayerType, 
        opacity: Float = 0.8f
    ): Boolean {
        if (!isStyleReady()) {
            Log.w(TAG, "Style未准备就绪，跳过叠加图层添加")
            return false
        }
        
        val tileSource = tileSourceManager.getTileSource(tileSourceId)
        if (tileSource == null) {
            Log.e(TAG, "未找到瓦片源: $tileSourceId")
            return false
        }
        
        return try {
            addLayer(
                LayerConfig(
                    tileSource = tileSource,
                    opacity = opacity,
                    isVisible = true,
                    layerType = layerType
                )
            )
            
            Log.i(TAG, "叠加图层添加成功: ${tileSource.name} -> ${layerType.displayName}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "添加叠加图层失败", e)
            false
        }
    }
    
    /**
     * 快速图层组合：卫星图 + 路网
     */
    fun setSatelliteWithRoads(
        satelliteSourceId: String, 
        roadSourceId: String,
        satelliteOpacity: Float = 1.0f,
        roadOpacity: Float = 0.7f
    ): Boolean {
        return try {
            // 设置卫星底图
            if (!setBaseLayer(satelliteSourceId, satelliteOpacity)) {
                return false
            }
            
            // 添加路网叠加
            addOverlayLayer(roadSourceId, LayerType.ROAD_NETWORK, roadOpacity)
        } catch (e: Exception) {
            Log.e(TAG, "设置卫星图+路网组合失败", e)
            false
        }
    }
    
    /**
     * 设置图层透明度
     */
    fun setLayerOpacity(layerType: LayerType, opacity: Float): Boolean {
        if (!isStyleReady()) {
            Log.w(TAG, "Style未准备就绪，跳过图层透明度设置")
            return false
        }
        
        val currentStyle = style ?: return false
        val config = activeLayerConfigs[layerType] ?: return false
        
        return try {
            val layerId = getLayerId(layerType, config.tileSource.id)
            val layer = currentStyle.getLayer(layerId) as? RasterLayer
            
            layer?.setProperties(PropertyFactory.rasterOpacity(opacity))
            
            // 更新配置
            activeLayerConfigs[layerType] = config.copy(opacity = opacity)
            
            Log.i(TAG, "图层透明度更新: ${layerType.displayName} -> $opacity")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置图层透明度失败", e)
            false
        }
    }
    
    /**
     * 切换图层可见性
     */
    fun toggleLayerVisibility(layerType: LayerType): Boolean {
        val config = activeLayerConfigs[layerType] ?: return false
        return setLayerVisibility(layerType, !config.isVisible)
    }
    
    /**
     * 设置图层可见性
     */
    fun setLayerVisibility(layerType: LayerType, visible: Boolean): Boolean {
        if (!isStyleReady()) {
            Log.w(TAG, "Style未准备就绪，跳过图层可见性设置")
            return false
        }
        
        val currentStyle = style ?: return false
        val config = activeLayerConfigs[layerType] ?: return false
        
        return try {
            val layerId = getLayerId(layerType, config.tileSource.id)
            val layer = currentStyle.getLayer(layerId)
            
            layer?.setProperties(
                PropertyFactory.visibility(
                    if (visible) Property.VISIBLE else Property.NONE
                )
            )
            
            // 更新配置
            activeLayerConfigs[layerType] = config.copy(isVisible = visible)
            
            Log.i(TAG, "图层可见性更新: ${layerType.displayName} -> $visible")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置图层可见性失败", e)
            false
        }
    }
    
    /**
     * 移除图层
     */
    fun removeLayer(layerType: LayerType): Boolean {
        if (!isStyleReady()) {
            Log.w(TAG, "Style未准备就绪，跳过图层移除")
            return false
        }
        
        val currentStyle = style ?: return false
        val config = activeLayerConfigs[layerType] ?: return true // 如果不存在，认为移除成功
        
        return try {
            val layerId = getLayerId(layerType, config.tileSource.id)
            val sourceId = getSourceId(layerType, config.tileSource.id)
            
            // 移除图层
            if (currentStyle.getLayer(layerId) != null) {
                currentStyle.removeLayer(layerId)
            }
            
            // 移除源
            if (currentStyle.getSource(sourceId) != null) {
                currentStyle.removeSource(sourceId)
            }
            
            activeLayerConfigs.remove(layerType)
            
            Log.i(TAG, "图层移除成功: ${layerType.displayName}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "移除图层失败", e)
            false
        }
    }
    
    /**
     * 获取所有活跃图层信息
     */
    fun getActiveLayersInfo(): String {
        return buildString {
            append("LayerCompositeManager状态:\n")
            append("- 活跃图层数: ${activeLayerConfigs.size}\n")
            append("- 图层详情:\n")
            
            activeLayerConfigs.entries
                .sortedBy { it.key.zIndex }
                .forEach { (layerType, config) ->
                    val visibilityIcon = if (config.isVisible) "👁️" else "🙈"
                    append("  $visibilityIcon ${layerType.displayName}: ${config.tileSource.name} (透明度: ${config.opacity})\n")
                }
        }
    }
    
    /**
     * 清除所有图层
     */
    fun clearAllLayers(): Boolean {
        return try {
            val layerTypes = activeLayerConfigs.keys.toList()
            layerTypes.forEach { removeLayer(it) }
            
            Log.i(TAG, "所有图层清除完成")
            true
        } catch (e: Exception) {
            Log.e(TAG, "清除所有图层失败", e)
            false
        }
    }
    
    /**
     * 刷新LocationComponent（简化版本，避免崩溃）
     */
    fun refreshLocationComponent() {
        // 不再强制刷新LocationComponent，让它保持稳定状态
    }
    
    // =================== 私有方法 ===================
    
    /**
     * 添加图层到地图
     */
    private fun addLayer(config: LayerConfig): Boolean {
        if (!isStyleReady()) {
            Log.w(TAG, "Style未准备就绪，跳过图层添加")
            return false
        }
        
        val currentStyle = style ?: return false
        
        return try {
            val sourceId = getSourceId(config.layerType, config.tileSource.id)
            val layerId = getLayerId(config.layerType, config.tileSource.id)
            
            // 处理不同的瓦片索引方案
            val processedUrl = when {
                config.tileSource.url.contains("{-y}") -> {
                    // TMS 方案：Y轴翻转 
                    config.tileSource.url.replace("{-y}", "{y}")
                }
                else -> {
                    // 对于WMTS和XYZ方案，保持原始URL格式
                    // MapLibre会根据scheme参数正确处理不同的瓦片索引方案
                    config.tileSource.url
                }
            }
            
            // 创建瓦片集，根据方案类型设置scheme
            val finalScheme = when (config.tileSource.scheme) {
                "tms" -> "tms"
                "wmts" -> "tms"  // 天地图WMTS使用TMS方案（Y轴翻转）
                else -> "xyz"
            }
            
            Log.d(TAG, "瓦片源配置: ${config.tileSource.name}")
            Log.d(TAG, "  - 原始URL: ${config.tileSource.url}")
            Log.d(TAG, "  - 处理后URL: $processedUrl")
            Log.d(TAG, "  - 原始scheme: ${config.tileSource.scheme}")
            Log.d(TAG, "  - 最终scheme: $finalScheme")
            
            val tileSet = TileSet("2.2.0", processedUrl).apply {
                minZoom = config.tileSource.minZoom.toFloat()
                maxZoom = config.tileSource.maxZoom.toFloat()
                scheme = finalScheme
            }
            
            // 创建源
            val rasterSource = RasterSource(sourceId, tileSet, config.tileSource.tileSize)
            currentStyle.addSource(rasterSource)
            
            // 创建图层
            val rasterLayer = RasterLayer(layerId, sourceId).apply {
                setProperties(
                    PropertyFactory.rasterOpacity(config.opacity),
                    PropertyFactory.visibility(if (config.isVisible) Property.VISIBLE else Property.NONE)
                )
            }
            
            // 确保所有瓦片图层都添加到最底层，避免覆盖LocationComponent
            currentStyle.addLayerAt(rasterLayer, 0)
            
            // 保存配置
            activeLayerConfigs[config.layerType] = config
            
            Log.i(TAG, "图层添加成功: ${config.layerType.displayName} (${config.tileSource.name})")
            true
        } catch (e: Exception) {
            Log.e(TAG, "添加图层失败: ${config.layerType.displayName}", e)
            false
        }
    }
    
    /**
     * 生成源ID
     */
    private fun getSourceId(layerType: LayerType, tileSourceId: String): String {
        return "${layerType.id}_source_$tileSourceId"
    }
    
    /**
     * 生成图层ID
     */
    private fun getLayerId(layerType: LayerType, tileSourceId: String): String {
        return "${layerType.id}_layer_$tileSourceId"
    }
    

} 