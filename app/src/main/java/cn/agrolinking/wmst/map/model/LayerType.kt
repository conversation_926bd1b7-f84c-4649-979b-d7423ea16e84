package cn.agrolinking.wmst.map.model

/**
 * 地图图层类型枚举
 * 定义不同类型的地图图层及其显示顺序
 */
enum class LayerType(val id: String, val zIndex: Int, val displayName: String) {
    /**
     * 卫星底图
     */
    SATELLITE_BASE("satellite-base", 0, "卫星底图"),
    
    /**
     * 路网图层
     */
    ROAD_NETWORK("road-network", 1, "路网"),
    
    /**
     * 农田边界
     */
    FARM_BOUNDARY("farm-boundary", 2, "农田边界"),
    
    /**
     * POI标注
     */
    POI_ANNOTATIONS("poi-annotations", 3, "POI标注"),
    
    /**
     * 用户位置
     */
    USER_LOCATION("user-location", 4, "用户位置"),
    
    /**
     * 测量工具
     */
    MEASUREMENT_TOOLS("measurement-tools", 5, "测量工具");
    
    companion object {
        /**
         * 根据ID查找图层类型
         */
        fun fromId(id: String): LayerType? = values().find { it.id == id }
        
        /**
         * 获取所有图层类型按zIndex排序
         */
        fun getAllSorted(): List<LayerType> = values().sortedBy { it.zIndex }
        
        /**
         * 获取基础图层（底图类型）
         */
        fun getBaseLayers(): List<LayerType> = listOf(SATELLITE_BASE, ROAD_NETWORK)
        
        /**
         * 获取业务图层
         */
        fun getBusinessLayers(): List<LayerType> = listOf(FARM_BOUNDARY, POI_ANNOTATIONS)
        
        /**
         * 获取工具图层
         */
        fun getToolLayers(): List<LayerType> = listOf(USER_LOCATION, MEASUREMENT_TOOLS)
    }
} 