package cn.agrolinking.wmst.map.manager

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import cn.agrolinking.wmst.map.model.AuthType
import cn.agrolinking.wmst.map.model.TileSource
import cn.agrolinking.wmst.map.model.TileSourceType
import cn.agrolinking.wmst.map.model.CoordinateSystem
import cn.agrolinking.wmst.map.coordinate.CoordinateConverter
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.lang.reflect.Type
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton
import org.maplibre.android.style.sources.RasterSource
import org.maplibre.android.style.sources.Source
import org.maplibre.android.style.sources.VectorSource
import org.maplibre.android.maps.Style

/**
 * 坐标系枚举反序列化器
 * 解决JSON字符串到枚举的映射问题
 */
class CoordinateSystemDeserializer : JsonDeserializer<CoordinateSystem> {
    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): CoordinateSystem {
        val value = json?.asString
        Log.d("CoordinateSystemDeserializer", "反序列化坐标系: '$value'")
        
        return when (value) {
            "EPSG:3857" -> {
                Log.d("CoordinateSystemDeserializer", "映射 EPSG:3857 -> EPSG_3857")
                CoordinateSystem.EPSG_3857
            }
            "EPSG:4326" -> {
                Log.d("CoordinateSystemDeserializer", "映射 EPSG:4326 -> EPSG_4326")
                CoordinateSystem.EPSG_4326
            }
            "GCJ02" -> {
                Log.d("CoordinateSystemDeserializer", "映射 GCJ02 -> GCJ02")
                CoordinateSystem.GCJ02
            }
            "WGS84" -> {
                Log.d("CoordinateSystemDeserializer", "映射 WGS84 -> WGS84")
                CoordinateSystem.WGS84
            }
            "BD09" -> {
                Log.d("CoordinateSystemDeserializer", "映射 BD09 -> BD09")
                CoordinateSystem.BD09
            }
            else -> {
                Log.w("CoordinateSystemDeserializer", "未知坐标系: '$value', 使用默认GCJ02")
                CoordinateSystem.GCJ02
            }
        }
    }
}

/**
 * 瓦片源配置
 */
data class TileSourceConfig(
    @SerializedName("version") val version: String,
    @SerializedName("tileSources") val tileSources: List<TileSource>
)

/**
 * 瓦片源管理器
 * 负责加载、验证和管理瓦片源
 */
@Singleton
class TileSourceManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val TAG = "TileSourceManager"
    private val gson = GsonBuilder()
        .registerTypeAdapter(CoordinateSystem::class.java, CoordinateSystemDeserializer())
        .create()
    
    // SharedPreferences用于缓存
    private val prefs: SharedPreferences = context.getSharedPreferences(
        "tile_source_cache", Context.MODE_PRIVATE
    )
    
    // 已注册的瓦片源
    private val registeredSources = mutableMapOf<String, TileSource>()
    
    // 验证结果缓存 (sourceId -> isValid)
    private val validationCache = mutableMapOf<String, Boolean>()
    
    // HTTP客户端
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .build()

    /**
     * 从配置文件加载瓦片源
     */
    suspend fun loadTileSources(): List<TileSource> {
        return try {
            // 首先尝试从缓存加载
            val cachedSources = loadFromCache()
            if (cachedSources.isNotEmpty()) {
                Log.i(TAG, "从缓存加载瓦片源: ${cachedSources.size}个")
                // 注册缓存的瓦片源
                cachedSources.forEach { registerTileSource(it) }
                return cachedSources
            }
            
            // 从配置文件加载
            val inputStream = context.assets.open("tile_sources_config.json")
            val json = inputStream.bufferedReader().use { it.readText() }
            Log.d(TAG, "JSON配置内容前200字符: ${json.take(200)}...")
            
            val config = gson.fromJson(json, TileSourceConfig::class.java)
            Log.d(TAG, "解析后的配置: 版本=${config.version}, 瓦片源数量=${config.tileSources.size}")
            
            // 设置默认值
            val sources = config.tileSources.map { source ->
                source.copy(
                    opacity = 1.0f,
                    zIndex = if (source.id.contains("road")) 1 else 0,
                    isVisible = true,
                    scheme = if (source.url.contains("{-y}")) "tms" else "xyz"
                )
            }
            
            // 保存到缓存
            saveToCache(sources)
            
            // 注册所有瓦片源并检查坐标系
            sources.forEach { source ->
                Log.d(TAG, "瓦片源 ${source.id}: 坐标系=${source.coordinateSystem}, 需要转换=${source.needsConversion}")
                registerTileSource(source)
            }
            
            Log.i(TAG, "从配置文件加载瓦片源: ${sources.size}个")
            sources
            
        } catch (e: Exception) {
            Log.e(TAG, "加载瓦片源配置失败", e)
            emptyList()
        }
    }

    /**
     * 批量验证瓦片源
     */
    suspend fun validateAllSources(): Map<String, Boolean> {
        val results = mutableMapOf<String, Boolean>()
        
        registeredSources.values.forEach { source ->
            // 检查缓存
            val cachedResult = getValidationFromCache(source.id)
            if (cachedResult != null) {
                results[source.id] = cachedResult
                validationCache[source.id] = cachedResult
            } else {
                // 执行验证
                val isValid = validateTileSource(source)
                results[source.id] = isValid
                validationCache[source.id] = isValid
                saveValidationToCache(source.id, isValid)
            }
        }
        
        Log.i(TAG, "批量验证完成: ${results.size}个瓦片源")
        return results
    }

    /**
     * 验证瓦片源是否可用
     */
    suspend fun validateTileSource(source: TileSource): Boolean {
        // 检查内存缓存
        validationCache[source.id]?.let { return it }
        
        // 检查持久化缓存
        getValidationFromCache(source.id)?.let { cachedResult ->
            validationCache[source.id] = cachedResult
            return cachedResult
        }
        
        return withContext(Dispatchers.IO) {
            try {
                // 构建测试URL（使用最小缩放级别的中心瓦片）
                val testUrl = source.url
                    .replace("{z}", source.minZoom.toString())
                    .replace("{x}", "0")
                    .replace("{y}", "0")
                    .replace("{0-7}", "0") // 对天地图的特殊处理
                
                val request = Request.Builder()
                    .url(testUrl)
                    .apply {
                        // 安全地添加自定义请求头，防止 headers 为 null
                        val headers = source.headers ?: emptyMap()
                        headers.forEach { (key, value) ->
                            addHeader(key, value)
                        }
                    }
                    .build()
                
                val response = client.newCall(request).execute()
                val isValid = response.isSuccessful
                
                // 保存验证结果到缓存
                validationCache[source.id] = isValid
                saveValidationToCache(source.id, isValid)
                
                if (isValid) {
                    Log.i(TAG, "瓦片源验证成功: ${source.id}")
                } else {
                    Log.w(TAG, "瓦片源验证失败: ${source.id}, 状态码: ${response.code}")
                }
                
                isValid
            } catch (e: Exception) {
                Log.e(TAG, "验证瓦片源失败: ${source.id}", e)
                
                // 缓存失败结果
                validationCache[source.id] = false
                saveValidationToCache(source.id, false)
                
                false
            }
        }
    }

    /**
     * 清除验证缓存
     */
    fun clearValidationCache() {
        validationCache.clear()
        prefs.edit().apply {
            val keys = prefs.all.keys.filter { it.startsWith("validation_") }
            keys.forEach { remove(it) }
            apply()
        }
        Log.i(TAG, "验证缓存已清除")
    }

    /**
     * 重新加载配置
     */
    suspend fun reloadConfig(): List<TileSource> {
        clearCache()
        clearValidationCache()
        registeredSources.clear()
        return loadTileSources()
    }

    /**
     * 注册新的瓦片源
     */
    fun registerTileSource(tileSource: TileSource) {
        registeredSources[tileSource.id] = tileSource
        Log.i(TAG, "注册瓦片源: ${tileSource.id}")
    }

    /**
     * 获取已注册的瓦片源
     */
    fun getRegisteredSources(): Map<String, TileSource> = registeredSources.toMap()

    /**
     * 根据ID获取瓦片源
     */
    fun getTileSource(id: String): TileSource? = registeredSources[id]

    /**
     * 获取所有激活的瓦片源
     */
    fun getActiveSources(): List<TileSource> = 
        registeredSources.values.filter { it.isActive }

    /**
     * 获取特定类型的瓦片源
     */
    fun getSourcesByType(type: TileSourceType): List<TileSource> =
        registeredSources.values.filter { it.type == type }

    /**
     * 获取瓦片源信息
     */
    fun getTileSourceInfo(): String {
        return buildString {
            append("TileSourceManager状态:\n")
            append("- 已注册源: ${registeredSources.size}个\n")
            append("- 验证缓存: ${validationCache.size}个\n")
            append("- 活跃源: ${getActiveSources().size}个\n")
            append("详细信息:\n")
            registeredSources.values.forEach { source ->
                val validStatus = validationCache[source.id]?.let { 
                    if (it) "✓" else "✗"
                } ?: "?"
                val conversionInfo = if (source.needsConversion) {
                    " (${source.coordinateSystem}→${source.targetSystem})"
                } else {
                    " (${source.coordinateSystem})"
                }
                append("  $validStatus ${source.name} (${source.id})$conversionInfo\n")
            }
        }
    }

    /**
     * 检查坐标系配置状态
     */
    fun getCoordinateSystemReport(): String {
        return buildString {
            append("坐标系配置报告:\n")
            registeredSources.values.forEach { source ->
                append("【${source.name}】\n")
                append("  - 坐标系: ${source.coordinateSystem}\n")
                append("  - 需要转换: ${if (source.needsConversion) "是" else "否"}\n")
                if (source.needsConversion) {
                    append("  - 目标坐标系: ${source.targetSystem}\n")
                }
                append("\n")
            }
            
            // 统计需要转换的瓦片源
            val needConversion = registeredSources.values.count { it.needsConversion }
            val sameSystem = registeredSources.values.count { !it.needsConversion }
            append("统计:\n")
            append("- 需要坐标转换: ${needConversion}个\n")
            append("- 无需转换: ${sameSystem}个\n")
        }
    }

    /**
     * 转换坐标点（用于地图点击、绘制等功能）
     */
    fun convertCoordinate(
        longitude: Double,
        latitude: Double,
        fromSourceId: String,
        toTargetSystem: CoordinateSystem = CoordinateSystem.GCJ02
    ): Pair<Double, Double> {
        val source = getTileSource(fromSourceId) ?: return Pair(longitude, latitude)
        
        return if (source.needsConversion) {
            CoordinateConverter.convert(
                longitude, latitude,
                source.coordinateSystem, toTargetSystem
            )
        } else {
            Pair(longitude, latitude)
        }
    }

    /**
     * 批量转换坐标点
     */
    fun convertCoordinates(
        coordinates: List<Pair<Double, Double>>,
        fromSourceId: String,
        toTargetSystem: CoordinateSystem = CoordinateSystem.GCJ02
    ): List<Pair<Double, Double>> {
        val source = getTileSource(fromSourceId) ?: return coordinates
        
        return if (source.needsConversion) {
            coordinates.map { (lon, lat) ->
                CoordinateConverter.convert(lon, lat, source.coordinateSystem, toTargetSystem)
            }
        } else {
            coordinates
        }
    }

    // =================== 私有缓存方法 ===================

    /**
     * 从缓存加载瓦片源
     */
    private fun loadFromCache(): List<TileSource> {
        return try {
            val cachedJson = prefs.getString("tile_sources", null) ?: return emptyList()
            val type = object : TypeToken<List<TileSource>>() {}.type
            gson.fromJson<List<TileSource>>(cachedJson, type) ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "从缓存加载失败", e)
            emptyList()
        }
    }

    /**
     * 保存瓦片源到缓存
     */
    private fun saveToCache(sources: List<TileSource>) {
        try {
            val json = gson.toJson(sources)
            prefs.edit()
                .putString("tile_sources", json)
                .putLong("cache_timestamp", System.currentTimeMillis())
                .apply()
        } catch (e: Exception) {
            Log.e(TAG, "保存到缓存失败", e)
        }
    }

    /**
     * 清除瓦片源缓存
     */
    private fun clearCache() {
        prefs.edit()
            .remove("tile_sources")
            .remove("cache_timestamp")
            .apply()
    }

    /**
     * 从缓存获取验证结果
     */
    private fun getValidationFromCache(sourceId: String): Boolean? {
        return try {
            val key = "validation_$sourceId"
            if (prefs.contains(key)) {
                prefs.getBoolean(key, false)
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 保存验证结果到缓存
     */
    private fun saveValidationToCache(sourceId: String, isValid: Boolean) {
        try {
            prefs.edit()
                .putBoolean("validation_$sourceId", isValid)
                .putLong("validation_timestamp_$sourceId", System.currentTimeMillis())
                .apply()
        } catch (e: Exception) {
            Log.e(TAG, "保存验证结果失败", e)
        }
    }
} 