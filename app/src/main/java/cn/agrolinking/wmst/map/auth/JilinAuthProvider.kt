package cn.agrolinking.wmst.map.auth

import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonObject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request
import java.util.concurrent.TimeUnit

/**
 * 吉林一号认证提供者
 * 实现吉林一号卫星数据API的认证流程
 */
class JilinAuthProvider : AuthProvider {
    
    companion object {
        private const val TAG = "JilinAuthProvider"
        private const val DEFAULT_TOKEN_ENDPOINT = "/oauth/token"
        private const val DEFAULT_EXPIRES_IN = 3600L // 默认1小时
    }
    
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    private val gson = Gson()
    
    override suspend fun authenticate(config: AuthConfig): AuthResponse {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始吉林一号认证: ${config.baseUrl}")
                
                // 构建请求
                val request = Request.Builder()
                    .url("${config.baseUrl}${config.tokenEndpoint}")
                    .post(FormBody.Builder()
                        .add("api_key", config.apiKey)
                        .apply {
                            config.apiSecret?.let { add("api_secret", it) }
                        }
                        .add("grant_type", "client_credentials")
                        .build())
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .addHeader("Accept", "application/json")
                    .build()
                
                // 执行请求
                val response = client.newCall(request).execute()
                val responseBody = response.body?.string()
                
                if (response.isSuccessful && responseBody != null) {
                    parseAuthResponse(responseBody)
                } else {
                    Log.w(TAG, "认证请求失败: ${response.code} - ${responseBody}")
                    AuthResponse(
                        success = false,
                        token = null,
                        expiresIn = 0,
                        refreshToken = null,
                        errorMessage = "认证请求失败: ${response.code}"
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "认证异常", e)
                AuthResponse(
                    success = false,
                    token = null,
                    expiresIn = 0,
                    refreshToken = null,
                    errorMessage = e.message
                )
            }
        }
    }
    
    override suspend fun refreshToken(refreshToken: String): AuthResponse {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "刷新吉林一号token")
                
                // 注意：这里需要实际的刷新端点URL
                // 由于没有具体的刷新端点，返回需要重新认证的响应
                Log.w(TAG, "吉林一号暂不支持token刷新，需要重新认证")
                AuthResponse(
                    success = false,
                    token = null,
                    expiresIn = 0,
                    refreshToken = null,
                    errorMessage = "需要重新认证"
                )
            } catch (e: Exception) {
                Log.e(TAG, "刷新token异常", e)
                AuthResponse(
                    success = false,
                    token = null,
                    expiresIn = 0,
                    refreshToken = null,
                    errorMessage = e.message
                )
            }
        }
    }
    
    override fun getAuthHeaders(token: String): Map<String, String> {
        return mapOf(
            "Authorization" to "Bearer $token",
            "Accept" to "application/json"
        )
    }
    
    /**
     * 解析认证响应
     */
    private fun parseAuthResponse(responseBody: String): AuthResponse {
        return try {
            val jsonObject = gson.fromJson(responseBody, JsonObject::class.java)
            
            // 检查是否有错误
            if (jsonObject.has("error")) {
                val error = jsonObject.get("error").asString
                val errorDescription = jsonObject.get("error_description")?.asString ?: ""
                
                Log.w(TAG, "认证失败: $error - $errorDescription")
                return AuthResponse(
                    success = false,
                    token = null,
                    expiresIn = 0,
                    refreshToken = null,
                    errorMessage = "$error: $errorDescription"
                )
            }
            
            // 解析成功响应
            val accessToken = jsonObject.get("access_token")?.asString
            val expiresIn = jsonObject.get("expires_in")?.asLong ?: DEFAULT_EXPIRES_IN
            val refreshToken = jsonObject.get("refresh_token")?.asString
            val tokenType = jsonObject.get("token_type")?.asString ?: "Bearer"
            
            if (accessToken != null) {
                Log.i(TAG, "认证成功，token类型: $tokenType, 有效期: ${expiresIn}秒")
                AuthResponse(
                    success = true,
                    token = accessToken,
                    expiresIn = expiresIn,
                    refreshToken = refreshToken,
                    errorMessage = null
                )
            } else {
                Log.w(TAG, "响应中缺少access_token")
                AuthResponse(
                    success = false,
                    token = null,
                    expiresIn = 0,
                    refreshToken = null,
                    errorMessage = "响应中缺少access_token"
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "解析认证响应失败", e)
            AuthResponse(
                success = false,
                token = null,
                expiresIn = 0,
                refreshToken = null,
                errorMessage = "解析认证响应失败: ${e.message}"
            )
        }
    }
} 