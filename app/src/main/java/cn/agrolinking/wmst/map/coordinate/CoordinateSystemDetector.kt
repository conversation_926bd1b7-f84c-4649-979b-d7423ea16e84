package cn.agrolinking.wmst.map.coordinate

import cn.agrolinking.wmst.map.model.CoordinateSystem
import cn.agrolinking.wmst.map.model.TileSource
import kotlin.math.*

/**
 * 坐标系偏移检测器
 * 用于验证不同瓦片源之间的坐标偏移情况
 */
object CoordinateSystemDetector {
    
    /**
     * 偏移检测结果
     */
    data class OffsetResult(
        val sourceA: String,
        val sourceB: String,
        val offsetMeters: Double,
        val offsetDirection: String,
        val isSignificant: Boolean,
        val recommendations: List<String>
    )
    
    /**
     * 测试点位置 (经纬度)
     * 选择几个中国境内的代表性位置进行测试
     */
    private val testPoints = listOf(
        Pair(116.4074, 39.9042),  // 北京天安门
        Pair(121.4737, 31.2304),  // 上海外滩  
        Pair(113.2644, 23.1291),  // 广州塔
        Pair(126.6194, 45.7570),  // 哈尔滨市中心
        Pair(108.9486, 34.2573)   // 西安钟楼
    )
    
    /**
     * 检测两个瓦片源之间的坐标偏移
     */
    fun detectOffset(sourceA: TileSource, sourceB: TileSource): OffsetResult {
        
        val offsets = mutableListOf<Double>()
        
        // 对每个测试点计算偏移
        testPoints.forEach { (lon, lat) ->
            // 将坐标转换到统一坐标系 (GCJ02)
            val pointA = convertToUnifiedSystem(lon, lat, sourceA)
            val pointB = convertToUnifiedSystem(lon, lat, sourceB)
            
            // 计算两点之间的距离
            val distance = calculateDistance(pointA, pointB)
            offsets.add(distance)
        }
        
        // 计算平均偏移距离
        val avgOffset = offsets.average()
        
        // 判断偏移是否显著 (超过100米认为显著)
        val isSignificant = avgOffset > 100.0
        
        // 生成偏移方向描述
        val direction = when {
            avgOffset < 10 -> "基本重合"
            avgOffset < 100 -> "轻微偏移"
            avgOffset < 500 -> "中等偏移"
            else -> "严重偏移"
        }
        
        // 生成建议
        val recommendations = generateRecommendations(sourceA, sourceB, avgOffset)
        
        return OffsetResult(
            sourceA = sourceA.name,
            sourceB = sourceB.name,
            offsetMeters = avgOffset,
            offsetDirection = direction,
            isSignificant = isSignificant,
            recommendations = recommendations
        )
    }
    
    /**
     * 批量检测多个瓦片源之间的偏移
     */
    fun detectAllOffsets(sources: List<TileSource>): List<OffsetResult> {
        val results = mutableListOf<OffsetResult>()
        
        // 两两比较所有瓦片源
        for (i in sources.indices) {
            for (j in i + 1 until sources.size) {
                val result = detectOffset(sources[i], sources[j])
                results.add(result)
            }
        }
        
        return results
    }
    
    /**
     * 生成偏移检测报告
     */
    fun generateOffsetReport(sources: List<TileSource>): String {
        val results = detectAllOffsets(sources)
        
        return buildString {
            append("=== 坐标系偏移检测报告 ===\n\n")
            
            append("检测配置:\n")
            append("- 测试点数量: ${testPoints.size}个\n")
            append("- 瓦片源数量: ${sources.size}个\n")
            append("- 比较组合: ${results.size}个\n\n")
            
            append("偏移检测结果:\n")
            results.forEach { result ->
                append("【${result.sourceA} ↔ ${result.sourceB}】\n")
                append("  - 平均偏移: ${String.format("%.2f", result.offsetMeters)}米\n")
                append("  - 偏移程度: ${result.offsetDirection}\n")
                append("  - 需要处理: ${if (result.isSignificant) "是" else "否"}\n")
                
                if (result.recommendations.isNotEmpty()) {
                    append("  - 建议: ${result.recommendations.joinToString(", ")}\n")
                }
                append("\n")
            }
            
            // 总结统计
            val significantOffsets = results.count { it.isSignificant }
            val totalComparisons = results.size
            
            append("总结:\n")
            append("- 显著偏移组合: $significantOffsets/$totalComparisons\n")
            append("- 偏移状况: ${
                when {
                    significantOffsets == 0 -> "所有图层对齐良好"
                    significantOffsets < totalComparisons / 2 -> "大部分图层对齐良好"
                    else -> "存在较多偏移问题，需要坐标转换"
                }
            }\n")
        }
    }
    
    /**
     * 将坐标转换到统一坐标系进行比较
     */
    private fun convertToUnifiedSystem(
        longitude: Double, 
        latitude: Double, 
        source: TileSource
    ): Pair<Double, Double> {
        return if (source.needsConversion) {
            CoordinateConverter.convert(
                longitude, latitude,
                source.coordinateSystem, source.targetSystem
            )
        } else {
            Pair(longitude, latitude)
        }
    }
    
    /**
     * 计算两个坐标点之间的距离 (米)
     * 使用 Haversine 公式
     */
    private fun calculateDistance(
        pointA: Pair<Double, Double>,
        pointB: Pair<Double, Double>
    ): Double {
        val (lon1, lat1) = pointA
        val (lon2, lat2) = pointB
        
        val R = 6371000.0 // 地球半径 (米)
        
        val dLat = Math.toRadians(lat2 - lat1)
        val dLon = Math.toRadians(lon2 - lon1)
        
        val a = sin(dLat / 2) * sin(dLat / 2) +
                cos(Math.toRadians(lat1)) * cos(Math.toRadians(lat2)) *
                sin(dLon / 2) * sin(dLon / 2)
        
        val c = 2 * atan2(sqrt(a), sqrt(1 - a))
        
        return R * c
    }
    
    /**
     * 根据偏移情况生成建议
     */
    private fun generateRecommendations(
        sourceA: TileSource,
        sourceB: TileSource,
        offsetMeters: Double
    ): List<String> {
        val recommendations = mutableListOf<String>()
        
        when {
            offsetMeters < 10 -> {
                recommendations.add("无需处理，对齐良好")
            }
            
            offsetMeters < 100 -> {
                recommendations.add("偏移较小，可忽略")
            }
            
            offsetMeters < 500 -> {
                // 检查是否有转换配置错误
                if (sourceA.coordinateSystem != sourceB.coordinateSystem) {
                    if (!sourceA.needsConversion && !sourceB.needsConversion) {
                        recommendations.add("需要为其中一个瓦片源配置坐标转换")
                    }
                }
                recommendations.add("建议检查坐标转换配置")
            }
            
            else -> {
                recommendations.add("严重偏移，必须进行坐标转换")
                
                // 具体的转换建议
                when {
                    sourceA.coordinateSystem == CoordinateSystem.EPSG_3857 && 
                    sourceB.coordinateSystem == CoordinateSystem.GCJ02 -> {
                        recommendations.add("${sourceA.name}需要EPSG:3857→GCJ02转换")
                    }
                    
                    sourceA.coordinateSystem == CoordinateSystem.WGS84 && 
                    sourceB.coordinateSystem == CoordinateSystem.GCJ02 -> {
                        recommendations.add("${sourceA.name}需要WGS84→GCJ02转换")
                    }
                    
                    else -> {
                        recommendations.add("检查两个瓦片源的坐标系配置")
                    }
                }
            }
        }
        
        return recommendations
    }
} 