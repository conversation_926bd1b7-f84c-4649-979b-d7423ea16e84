package cn.agrolinking.wmst.map.auth

import android.util.Log

/**
 * 天地图认证提供者
 * 天地图使用静态API Key认证，不需要动态token
 */
class TiandituAuthProvider : AuthProvider {
    
    companion object {
        private const val TAG = "TiandituAuthProvider"
        private const val STATIC_TOKEN_EXPIRES = 86400L * 365 // 1年有效期（模拟永久）
    }
    
    override suspend fun authenticate(config: AuthConfig): AuthResponse {
        return try {
            Log.d(TAG, "天地图API Key认证")
            
            // 验证API Key格式（简单校验）
            if (config.apiKey.isBlank()) {
                AuthResponse(
                    success = false,
                    token = null,
                    expiresIn = 0,
                    refreshToken = null,
                    errorMessage = "天地图API Key不能为空"
                )
            } else if (config.apiKey.length < 10) {
                AuthResponse(
                    success = false,
                    token = null,
                    expiresIn = 0,
                    refreshToken = null,
                    errorMessage = "天地图API Key格式不正确"
                )
            } else {
                // 天地图使用静态API Key，直接返回成功
                Log.i(TAG, "天地图API Key验证成功")
                AuthResponse(
                    success = true,
                    token = config.apiKey, // 直接使用API Key作为token
                    expiresIn = STATIC_TOKEN_EXPIRES,
                    refreshToken = null, // 不需要刷新token
                    errorMessage = null
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "天地图认证异常", e)
            AuthResponse(
                success = false,
                token = null,
                expiresIn = 0,
                refreshToken = null,
                errorMessage = e.message
            )
        }
    }
    
    override suspend fun refreshToken(refreshToken: String): AuthResponse {
        // 天地图不需要刷新token
        Log.d(TAG, "天地图无需刷新token")
        return AuthResponse(
            success = false,
            token = null,
            expiresIn = 0,
            refreshToken = null,
            errorMessage = "天地图API Key无需刷新"
        )
    }
    
    override fun getAuthHeaders(token: String): Map<String, String> {
        // 天地图的API Key通常在URL参数中，不在请求头
        // 但为了统一接口，可以返回一个标识头
        return mapOf(
            "X-TianDiTu-Key" to token,
            "Accept" to "application/json"
        )
    }
} 