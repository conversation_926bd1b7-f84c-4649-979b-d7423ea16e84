package cn.agrolinking.wmst.map.drawing

import android.graphics.Color
import cn.agrolinking.wmst.domain.LandPlot
import cn.agrolinking.wmst.map.util.GeometryCalculator
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.Style
import org.maplibre.android.plugins.annotation.Circle
import org.maplibre.android.plugins.annotation.CircleManager
import org.maplibre.android.plugins.annotation.CircleOptions
import org.maplibre.android.plugins.annotation.Fill
import org.maplibre.android.plugins.annotation.FillManager
import org.maplibre.android.plugins.annotation.FillOptions
import timber.log.Timber
import kotlin.math.pow
import kotlin.math.sqrt

class ManualDrawingHandler(
    private val mapView: MapView,
    private val mapLibreMap: MapLibreMap,
    private val style: Style
) : Drawing<PERSON><PERSON>ler {
    
    override var onDrawingCompleted: ((LandPlot) -> Unit)? = null
    override var onDrawingCancelled: (() -> Unit)? = null
    
    private val vertices = mutableListOf<LatLng>()
    private var fillManager: FillManager? = null
    private var circleManager: CircleManager? = null
    private var currentPolygon: Fill? = null
    private val vertexCircles = mutableListOf<Circle>()
    private var mapClickListener: MapLibreMap.OnMapClickListener? = null
    
    private val geometryCalculator = GeometryCalculator()
    
    companion object {
        private const val VERTEX_RADIUS = 8f
        private const val VERTEX_COLOR = "#FFFF0000" // 红色
        private const val VERTEX_STROKE_COLOR = "#FFFFFFFF" // 白色
        private const val VERTEX_STROKE_WIDTH = 2f
        private const val POLYGON_COLOR = "#4285F4"
        private const val POLYGON_OPACITY = 0.3f
        private const val CLOSURE_THRESHOLD = 50.0 // 米
    }
    
    init {
        setupAnnotationManagers()
        setupClickListener()
        Timber.d("ManualDrawingHandler 初始化完成")
    }
    
    private fun setupAnnotationManagers() {
        try {
            // 创建 FillManager 用于多边形显示
            fillManager = FillManager(mapView, mapLibreMap, style)
            
            // 创建 CircleManager 用于顶点显示
            circleManager = CircleManager(mapView, mapLibreMap, style)
            
            Timber.d("注释管理器创建成功")
        } catch (e: Exception) {
            Timber.e(e, "创建注释管理器失败")
        }
    }
    
    private fun setupClickListener() {
        mapClickListener = MapLibreMap.OnMapClickListener { point ->
            addVertex(point)
            true
        }
        mapLibreMap.addOnMapClickListener(mapClickListener!!)
        Timber.d("地图点击监听器设置完成")
    }
    
    private fun addVertex(point: LatLng) {
        vertices.add(point)
        Timber.d("添加顶点: ${point.latitude}, ${point.longitude}, 总计: ${vertices.size}")
        
        updateVertexDisplay()
        updatePolygonDisplay()
        
        // 检查是否可以闭合多边形
        if (vertices.size >= 3) {
            checkForPolygonClosure(point)
        }
    }
    
    private fun updateVertexDisplay() {
        try {
            val newVertex = vertices.last()
            val circle = circleManager?.create(CircleOptions()
                .withLatLng(newVertex)
                .withCircleRadius(VERTEX_RADIUS)
                .withCircleColor(VERTEX_COLOR)
                .withCircleStrokeColor(VERTEX_STROKE_COLOR)
                .withCircleStrokeWidth(VERTEX_STROKE_WIDTH)
            )
            circle?.let { vertexCircles.add(it) }
            Timber.d("顶点显示更新，当前顶点数: ${vertexCircles.size}")
        } catch (e: Exception) {
            Timber.e(e, "更新顶点显示失败")
        }
    }
    
    private fun updatePolygonDisplay() {
        if (vertices.size >= 3) {
            try {
                // 删除旧的多边形
                currentPolygon?.let { fillManager?.delete(it) }
                
                // 创建闭合的多边形坐标列表
                val closedVertices = vertices.toMutableList().apply { add(vertices.first()) }
                
                // 创建新的多边形
                currentPolygon = fillManager?.create(FillOptions()
                    .withLatLngs(listOf(closedVertices))
                    .withFillOpacity(POLYGON_OPACITY)
                    .withFillColor(POLYGON_COLOR)
                    .withFillOutlineColor(POLYGON_COLOR)
                )
                
                Timber.d("多边形显示更新，顶点数: ${vertices.size}")
            } catch (e: Exception) {
                Timber.e(e, "更新多边形显示失败")
            }
        }
    }
    
    private fun checkForPolygonClosure(clickPoint: LatLng) {
        if (vertices.size < 3) return
        
        val firstVertex = vertices.first()
        val distance = geometryCalculator.calculateDistance(clickPoint, firstVertex)
        
        // 如果点击位置距离第一个顶点很近，则闭合多边形
        if (distance < CLOSURE_THRESHOLD) {
            Timber.d("检测到多边形闭合，距离: ${distance}m")
            finishDrawing()
        }
    }
    
    private fun finishDrawing() {
        if (vertices.size >= 3) {
            try {
                val landPlot = createLandPlot(vertices)
                Timber.d("地块绘制完成: 面积=${geometryCalculator.formatArea(landPlot.area)}")
                onDrawingCompleted?.invoke(landPlot)
            } catch (e: Exception) {
                Timber.e(e, "完成绘制失败")
                onDrawingCancelled?.invoke()
            }
        }
        cleanup()
    }
    
    private fun createLandPlot(vertices: List<LatLng>): LandPlot {
        val area = geometryCalculator.calculatePolygonArea(vertices)
        val perimeter = geometryCalculator.calculatePolygonPerimeter(vertices)
        val centroid = calculateCentroid(vertices)
        
        return LandPlot(
            name = "地块_${System.currentTimeMillis()}",
            description = "手工圈划地块",
            vertices = vertices,
            area = area,
            perimeter = perimeter,
            centroid = centroid,
            atlasId = "default", // 临时使用默认图册ID
            createdBy = "system" // 临时使用系统创建
        )
    }
    
    private fun calculateCentroid(vertices: List<LatLng>): LatLng {
        val avgLat = vertices.map { it.latitude }.average()
        val avgLng = vertices.map { it.longitude }.average()
        return LatLng(avgLat, avgLng)
    }
    
    override fun undo(): Boolean {
        if (vertices.isEmpty()) return false
        
        try {
            // 移除最后一个顶点
            vertices.removeLastOrNull()
            
            // 移除对应的顶点圆圈
            if (vertexCircles.isNotEmpty()) {
                val lastCircle = vertexCircles.removeLastOrNull()
                lastCircle?.let { circleManager?.delete(it) }
            }
            
            // 更新多边形显示
            updatePolygonDisplay()
            
            Timber.d("撤销操作完成，剩余顶点数: ${vertices.size}")
            return true
        } catch (e: Exception) {
            Timber.e(e, "撤销操作失败")
            return false
        }
    }
    
    override fun clear() {
        try {
            vertices.clear()
            vertexCircles.forEach { circleManager?.delete(it) }
            vertexCircles.clear()
            currentPolygon?.let { fillManager?.delete(it) }
            currentPolygon = null
            
            Timber.d("清除绘制状态完成")
        } catch (e: Exception) {
            Timber.e(e, "清除绘制状态失败")
        }
    }
    
    override fun canUndo(): Boolean = vertices.isNotEmpty()
    
    override fun cleanup() {
        try {
            // 移除地图点击监听器
            mapClickListener?.let { mapLibreMap.removeOnMapClickListener(it) }
            mapClickListener = null
            
            // 清除绘制状态
            clear()
            
            // 销毁注释管理器
            fillManager?.onDestroy()
            circleManager?.onDestroy()
            fillManager = null
            circleManager = null
            
            Timber.d("ManualDrawingHandler 清理完成")
        } catch (e: Exception) {
            Timber.e(e, "清理失败")
        }
    }
} 