package cn.agrolinking.wmst.map.validation

import android.util.Log
import cn.agrolinking.wmst.domain.drawing.LandPlotInProgress
import cn.agrolinking.wmst.domain.drawing.PolygonInProgress
import cn.agrolinking.wmst.map.util.GeometryUtils
import org.maplibre.android.geometry.LatLng
import kotlin.math.*

/**
 * 几何体自动修复引擎
 * 提供智能的几何体错误修复功能
 */
class GeometryRepairer {
    
    companion object {
        private const val TAG = "GeometryRepairer"
        
        // 修复配置常量
        const val MERGE_DISTANCE_THRESHOLD_M = 0.5          // 顶点合并距离阈值（米）
        const val SIMPLIFY_DISTANCE_THRESHOLD_M = 0.2       // 简化距离阈值（米）
        const val MIN_ANGLE_THRESHOLD_DEGREES = 15.0        // 最小角度阈值（度）
        const val INTERSECTION_TOLERANCE_M = 0.1            // 相交容差（米）
        const val HOLE_BUFFER_DISTANCE_M = 1.0              // 孔洞缓冲距离（米）
        const val MAX_REPAIR_ITERATIONS = 10                // 最大修复迭代次数
    }
    
    /**
     * 修复结果
     */
    data class RepairResult(
        val repairedLandPlot: LandPlotInProgress?,
        val appliedFixes: List<AppliedFix>,
        val remainingIssues: List<GeometryValidator.ValidationError>,
        val isFullyRepaired: Boolean,
        val repairMetadata: RepairMetadata
    ) {
        val hasChanges: Boolean get() = appliedFixes.isNotEmpty()
        val successRate: Double get() = if (repairMetadata.totalIssues > 0) {
            (repairMetadata.totalIssues - remainingIssues.size).toDouble() / repairMetadata.totalIssues
        } else 1.0
        
        val summaryText: String
            get() = buildString {
                appendLine("修复完成:")
                appendLine("• 应用修复: ${appliedFixes.size}项")
                appendLine("• 剩余问题: ${remainingIssues.size}项")
                appendLine("• 成功率: ${String.format("%.1f", successRate * 100)}%")
                if (appliedFixes.isNotEmpty()) {
                    appendLine("\n应用的修复:")
                    appliedFixes.forEach { fix ->
                        appendLine("  • ${fix.description}")
                    }
                }
                if (remainingIssues.isNotEmpty()) {
                    appendLine("\n剩余问题:")
                    remainingIssues.forEach { issue ->
                        appendLine("  • ${issue.message}")
                    }
                }
            }
    }
    
    /**
     * 应用的修复
     */
    data class AppliedFix(
        val type: RepairType,
        val description: String,
        val location: LatLng? = null,
        val polygonIndex: Int? = null,
        val affectedVertices: List<Int> = emptyList(),
        val beforeCount: Int? = null,
        val afterCount: Int? = null
    )
    
    /**
     * 修复元数据
     */
    data class RepairMetadata(
        val totalIssues: Int,
        val repairableIssues: Int,
        val processingTimeMs: Long,
        val iterationsUsed: Int,
        val verticesRemoved: Int,
        val verticesAdded: Int,
        val ringsModified: Int
    )
    
    /**
     * 修复类型
     */
    enum class RepairType {
        MERGE_DUPLICATE_VERTICES,     // 合并重复顶点
        REMOVE_COLLINEAR_VERTICES,    // 移除共线顶点
        FIX_SELF_INTERSECTION,        // 修复自相交
        SMOOTH_ACUTE_ANGLES,          // 平滑锐角
        SIMPLIFY_GEOMETRY,            // 简化几何体
        FIX_RING_ORIENTATION,         // 修复环方向
        MOVE_HOLE_INSIDE_BOUNDARY,    // 移动孔洞到边界内
        SEPARATE_INTERSECTING_HOLES,  // 分离相交孔洞
        VALIDATE_COORDINATES,         // 验证坐标
        OPTIMIZE_VERTEX_DENSITY       // 优化顶点密度
    }
    
    /**
     * 修复策略
     */
    data class RepairStrategy(
        val enabledRepairTypes: Set<RepairType>,
        val aggressiveMode: Boolean = false,
        val preserveOriginalShape: Boolean = true,
        val maxVertexReduction: Double = 0.3, // 最大顶点减少比例
        val qualityThreshold: Double = 0.8     // 质量阈值
    ) {
        companion object {
            val DEFAULT = RepairStrategy(
                enabledRepairTypes = setOf(
                    RepairType.MERGE_DUPLICATE_VERTICES,
                    RepairType.REMOVE_COLLINEAR_VERTICES,
                    RepairType.FIX_RING_ORIENTATION,
                    RepairType.VALIDATE_COORDINATES
                )
            )
            
            val AGGRESSIVE = RepairStrategy(
                enabledRepairTypes = RepairType.values().toSet(),
                aggressiveMode = true,
                preserveOriginalShape = false,
                maxVertexReduction = 0.5
            )
            
            val CONSERVATIVE = RepairStrategy(
                enabledRepairTypes = setOf(
                    RepairType.MERGE_DUPLICATE_VERTICES,
                    RepairType.VALIDATE_COORDINATES
                ),
                preserveOriginalShape = true,
                maxVertexReduction = 0.1
            )
        }
    }
    
    /**
     * 几何验证器实例
     */
    private val validator = GeometryValidator()
    
    /**
     * 自动修复地块
     */
    fun repairLandPlot(
        landPlot: LandPlotInProgress,
        strategy: RepairStrategy = RepairStrategy.DEFAULT
    ): RepairResult {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "开始修复地块，使用策略: ${strategy.enabledRepairTypes.size}种修复类型")
        
        // 初始验证
        val initialValidation = validator.validateLandPlot(landPlot)
        val totalIssues = initialValidation.errors.size + initialValidation.warnings.size
        val repairableIssues = countRepairableIssuesFromValidation(initialValidation, strategy)
        
        if (totalIssues == 0) {
            Log.d(TAG, "地块无需修复")
            return RepairResult(
                repairedLandPlot = landPlot,
                appliedFixes = emptyList(),
                remainingIssues = emptyList(),
                isFullyRepaired = true,
                repairMetadata = RepairMetadata(
                    totalIssues = 0,
                    repairableIssues = 0,
                    processingTimeMs = System.currentTimeMillis() - startTime,
                    iterationsUsed = 0,
                    verticesRemoved = 0,
                    verticesAdded = 0,
                    ringsModified = 0
                )
            )
        }
        
        // 执行修复
        var currentLandPlot = landPlot
        val appliedFixes = mutableListOf<AppliedFix>()
        var iteration = 0
        var totalVerticesRemoved = 0
        var totalVerticesAdded = 0
        var totalRingsModified = 0
        
        while (iteration < MAX_REPAIR_ITERATIONS) {
            iteration++
            val iterationFixes = mutableListOf<AppliedFix>()
            var hasChanges = false
            
            // 逐个多边形修复
            val repairedPolygons = currentLandPlot.polygons.mapIndexed { polygonIndex, polygon ->
                val polygonRepairResult = repairPolygon(polygon, polygonIndex, strategy)
                if (polygonRepairResult.hasChanges) {
                    hasChanges = true
                    iterationFixes.addAll(polygonRepairResult.appliedFixes)
                    totalVerticesRemoved += polygonRepairResult.verticesRemoved
                    totalVerticesAdded += polygonRepairResult.verticesAdded
                    if (polygonRepairResult.hasChanges) totalRingsModified++
                }
                polygonRepairResult.repairedPolygon ?: polygon
            }
            
            if (!hasChanges) {
                Log.d(TAG, "第${iteration}轮修复无变化，停止迭代")
                break
            }
            
            appliedFixes.addAll(iterationFixes)
            currentLandPlot = currentLandPlot.copy(polygons = repairedPolygons)
            
            Log.d(TAG, "第${iteration}轮修复完成，应用${iterationFixes.size}项修复")
        }
        
        // 最终验证
        val finalValidation = validator.validateLandPlot(currentLandPlot)
        val allFinalIssues = mutableListOf<GeometryValidator.ValidationError>()
        // 将警告转换为错误类型来统一处理
        allFinalIssues.addAll(finalValidation.errors)
        finalValidation.warnings.forEach { warning ->
            allFinalIssues.add(GeometryValidator.ValidationError(
                type = convertWarningToErrorType(warning.type),
                message = warning.message,
                location = warning.location,
                polygonIndex = warning.polygonIndex,
                ringIndex = warning.ringIndex,
                vertexIndex = warning.vertexIndex
            ))
        }
        val isFullyRepaired = allFinalIssues.isEmpty()
        
        val processingTime = System.currentTimeMillis() - startTime
        
        Log.i(TAG, "修复完成: ${appliedFixes.size}项修复，${iteration}次迭代，耗时${processingTime}ms")
        
        return RepairResult(
            repairedLandPlot = currentLandPlot,
            appliedFixes = appliedFixes,
            remainingIssues = allFinalIssues,
            isFullyRepaired = isFullyRepaired,
            repairMetadata = RepairMetadata(
                totalIssues = totalIssues,
                repairableIssues = repairableIssues,
                processingTimeMs = processingTime,
                iterationsUsed = iteration,
                verticesRemoved = totalVerticesRemoved,
                verticesAdded = totalVerticesAdded,
                ringsModified = totalRingsModified
            )
        )
    }
    
    /**
     * 修复单个多边形
     */
    private fun repairPolygon(
        polygon: PolygonInProgress,
        polygonIndex: Int,
        strategy: RepairStrategy
    ): PolygonRepairResult {
        var currentPolygon = polygon
        val appliedFixes = mutableListOf<AppliedFix>()
        var verticesRemoved = 0
        var verticesAdded = 0
        
        // 按优先级执行修复
        val repairOrder = getRepairOrder(strategy)
        
        for (repairType in repairOrder) {
            if (!strategy.enabledRepairTypes.contains(repairType)) continue
            
            val repairResult = when (repairType) {
                RepairType.VALIDATE_COORDINATES -> {
                    repairInvalidCoordinates(currentPolygon, polygonIndex)
                }
                RepairType.MERGE_DUPLICATE_VERTICES -> {
                    mergeDuplicateVertices(currentPolygon, polygonIndex)
                }
                RepairType.REMOVE_COLLINEAR_VERTICES -> {
                    removeCollinearVertices(currentPolygon, polygonIndex)
                }
                RepairType.FIX_SELF_INTERSECTION -> {
                    fixSelfIntersection(currentPolygon, polygonIndex)
                }
                RepairType.SMOOTH_ACUTE_ANGLES -> {
                    smoothAcuteAngles(currentPolygon, polygonIndex, strategy)
                }
                RepairType.FIX_RING_ORIENTATION -> {
                    fixRingOrientation(currentPolygon, polygonIndex)
                }
                RepairType.MOVE_HOLE_INSIDE_BOUNDARY -> {
                    moveHolesInsideBoundary(currentPolygon, polygonIndex)
                }
                RepairType.SEPARATE_INTERSECTING_HOLES -> {
                    separateIntersectingHoles(currentPolygon, polygonIndex)
                }
                RepairType.SIMPLIFY_GEOMETRY -> {
                    simplifyGeometry(currentPolygon, polygonIndex, strategy)
                }
                RepairType.OPTIMIZE_VERTEX_DENSITY -> {
                    optimizeVertexDensity(currentPolygon, polygonIndex, strategy)
                }
            }
            
            if (repairResult.hasChanges) {
                currentPolygon = repairResult.repairedPolygon!!
                appliedFixes.addAll(repairResult.appliedFixes)
                verticesRemoved += repairResult.verticesRemoved
                verticesAdded += repairResult.verticesAdded
                
                Log.d(TAG, "多边形$polygonIndex 应用修复: ${repairType.name}")
            }
        }
        
        return PolygonRepairResult(
            repairedPolygon = if (appliedFixes.isNotEmpty()) currentPolygon else null,
            appliedFixes = appliedFixes,
            hasChanges = appliedFixes.isNotEmpty(),
            verticesRemoved = verticesRemoved,
            verticesAdded = verticesAdded
        )
    }
    
    /**
     * 多边形修复结果
     */
    private data class PolygonRepairResult(
        val repairedPolygon: PolygonInProgress?,
        val appliedFixes: List<AppliedFix>,
        val hasChanges: Boolean,
        val verticesRemoved: Int,
        val verticesAdded: Int
    )
    
    /**
     * 单个修复操作结果
     */
    private data class SingleRepairResult(
        val repairedPolygon: PolygonInProgress?,
        val appliedFixes: List<AppliedFix>,
        val hasChanges: Boolean,
        val verticesRemoved: Int = 0,
        val verticesAdded: Int = 0
    )
    
    // ========== 工具方法 ==========
    
    /**
     * 统计可修复的问题数量
     */
    private fun countRepairableIssues(
        errors: List<GeometryValidator.ValidationError>,
        strategy: RepairStrategy
    ): Int {
        return errors.count { error ->
            val repairType = getRepairTypeForError(error.type)
            repairType != null && strategy.enabledRepairTypes.contains(repairType)
        }
    }
    
    /**
     * 从验证结果统计可修复的问题数量
     */
    private fun countRepairableIssuesFromValidation(
        validationResult: GeometryValidator.ValidationResult,
        strategy: RepairStrategy
    ): Int {
        val errorCount = validationResult.errors.count { error ->
            val repairType = getRepairTypeForError(error.type)
            repairType != null && strategy.enabledRepairTypes.contains(repairType)
        }
        
        val warningCount = validationResult.warnings.count { warning ->
            val repairType = getRepairTypeForWarning(warning.type)
            repairType != null && strategy.enabledRepairTypes.contains(repairType)
        }
        
        return errorCount + warningCount
    }
    
    /**
     * 将警告类型转换为错误类型
     */
    private fun convertWarningToErrorType(warningType: GeometryValidator.WarningType): GeometryValidator.ErrorType {
        return when (warningType) {
            GeometryValidator.WarningType.HIGH_VERTEX_DENSITY -> GeometryValidator.ErrorType.EXCESSIVE_VERTICES
            GeometryValidator.WarningType.LOW_VERTEX_DENSITY -> GeometryValidator.ErrorType.INSUFFICIENT_VERTICES
            GeometryValidator.WarningType.NEAR_DUPLICATE_VERTICES -> GeometryValidator.ErrorType.DUPLICATE_VERTICES
            GeometryValidator.WarningType.SMALL_ANGLE -> GeometryValidator.ErrorType.ACUTE_ANGLE
            GeometryValidator.WarningType.IRREGULAR_SHAPE -> GeometryValidator.ErrorType.INVALID_RING_ORIENTATION
            else -> GeometryValidator.ErrorType.INVALID_TOPOLOGY
        }
    }
    
    /**
     * 获取修复优先级顺序
     */
    private fun getRepairOrder(strategy: RepairStrategy): List<RepairType> {
        // 基础修复优先级（从高到低）
        val baseOrder = listOf(
            RepairType.VALIDATE_COORDINATES,      // 最高优先级：确保坐标有效
            RepairType.MERGE_DUPLICATE_VERTICES,  // 合并重复顶点
            RepairType.REMOVE_COLLINEAR_VERTICES, // 移除共线顶点
            RepairType.FIX_RING_ORIENTATION,     // 修复环方向
            RepairType.MOVE_HOLE_INSIDE_BOUNDARY, // 移动孔洞到边界内
            RepairType.SEPARATE_INTERSECTING_HOLES, // 分离相交孔洞
            RepairType.FIX_SELF_INTERSECTION,    // 修复自相交
            RepairType.SMOOTH_ACUTE_ANGLES,      // 平滑锐角
            RepairType.OPTIMIZE_VERTEX_DENSITY,  // 优化顶点密度
            RepairType.SIMPLIFY_GEOMETRY         // 最低优先级：几何简化
        )
        
        return if (strategy.aggressiveMode) {
            // 激进模式：包含所有启用的修复类型
            baseOrder.filter { strategy.enabledRepairTypes.contains(it) }
        } else {
            // 保守模式：优先执行安全的修复
            val safeRepairs = listOf(
                RepairType.VALIDATE_COORDINATES,
                RepairType.MERGE_DUPLICATE_VERTICES,
                RepairType.REMOVE_COLLINEAR_VERTICES,
                RepairType.FIX_RING_ORIENTATION
            )
            val enabledSafeRepairs = safeRepairs.filter { strategy.enabledRepairTypes.contains(it) }
            val enabledOtherRepairs = baseOrder.filter { 
                strategy.enabledRepairTypes.contains(it) && !safeRepairs.contains(it) 
            }
            enabledSafeRepairs + enabledOtherRepairs
        }
    }
    
    /**
     * 根据错误类型获取对应的修复类型
     */
    private fun getRepairTypeForError(errorType: GeometryValidator.ErrorType): RepairType? {
        return when (errorType) {
            GeometryValidator.ErrorType.INVALID_COORDINATES -> RepairType.VALIDATE_COORDINATES
            GeometryValidator.ErrorType.DUPLICATE_VERTICES -> RepairType.MERGE_DUPLICATE_VERTICES
            GeometryValidator.ErrorType.COLLINEAR_VERTICES -> RepairType.REMOVE_COLLINEAR_VERTICES
            GeometryValidator.ErrorType.SELF_INTERSECTION -> RepairType.FIX_SELF_INTERSECTION
            GeometryValidator.ErrorType.ACUTE_ANGLE -> RepairType.SMOOTH_ACUTE_ANGLES
            GeometryValidator.ErrorType.INVALID_RING_ORIENTATION -> RepairType.FIX_RING_ORIENTATION
            GeometryValidator.ErrorType.HOLE_OUTSIDE_BOUNDARY -> RepairType.MOVE_HOLE_INSIDE_BOUNDARY
            GeometryValidator.ErrorType.HOLE_INTERSECTION -> RepairType.SEPARATE_INTERSECTING_HOLES
            GeometryValidator.ErrorType.EDGE_TOO_SHORT,
            GeometryValidator.ErrorType.EDGE_TOO_LONG -> RepairType.OPTIMIZE_VERTEX_DENSITY
            GeometryValidator.ErrorType.EXCESSIVE_VERTICES -> RepairType.SIMPLIFY_GEOMETRY
            else -> null // 其他错误类型暂不支持自动修复
        }
    }
    
    /**
     * 根据警告类型获取对应的修复类型
     */
    private fun getRepairTypeForWarning(warningType: GeometryValidator.WarningType): RepairType? {
        return when (warningType) {
            GeometryValidator.WarningType.HIGH_VERTEX_DENSITY -> RepairType.SIMPLIFY_GEOMETRY
            GeometryValidator.WarningType.LOW_VERTEX_DENSITY -> RepairType.OPTIMIZE_VERTEX_DENSITY
            GeometryValidator.WarningType.NEAR_DUPLICATE_VERTICES -> RepairType.MERGE_DUPLICATE_VERTICES
            GeometryValidator.WarningType.SMALL_ANGLE -> RepairType.SMOOTH_ACUTE_ANGLES
            GeometryValidator.WarningType.IRREGULAR_SHAPE -> RepairType.FIX_RING_ORIENTATION
            else -> null
        }
    }
    
    // ========== 具体修复算法 ==========
    
    /**
     * 修复无效坐标
     */
    private fun repairInvalidCoordinates(
        polygon: PolygonInProgress,
        polygonIndex: Int
    ): SingleRepairResult {
        val appliedFixes = mutableListOf<AppliedFix>()
        var hasChanges = false
        
        // 修复外边界
        val repairedExterior = repairRingCoordinates(polygon.exterior).also { repairedRing ->
            if (repairedRing.size != polygon.exterior.size) {
                hasChanges = true
                appliedFixes.add(AppliedFix(
                    type = RepairType.VALIDATE_COORDINATES,
                    description = "修复外边界无效坐标",
                    polygonIndex = polygonIndex,
                    beforeCount = polygon.exterior.size,
                    afterCount = repairedRing.size
                ))
            }
        }
        
        // 修复孔洞
        val repairedHoles = polygon.holes.mapIndexed { holeIndex, hole ->
            repairRingCoordinates(hole).also { repairedHole ->
                if (repairedHole.size != hole.size) {
                    hasChanges = true
                    appliedFixes.add(AppliedFix(
                        type = RepairType.VALIDATE_COORDINATES,
                        description = "修复孔洞$holeIndex 无效坐标",
                        polygonIndex = polygonIndex,
                        beforeCount = hole.size,
                        afterCount = repairedHole.size
                    ))
                }
            }
        }
        
        val repairedPolygon = if (hasChanges) {
            polygon.copy(exterior = repairedExterior, holes = repairedHoles)
        } else null
        
        return SingleRepairResult(
            repairedPolygon = repairedPolygon,
            appliedFixes = appliedFixes,
            hasChanges = hasChanges,
            verticesRemoved = (polygon.exterior.size + polygon.holes.sumOf { it.size }) - 
                            (repairedExterior.size + repairedHoles.sumOf { it.size })
        )
    }
    
    /**
     * 修复环的坐标
     */
    private fun repairRingCoordinates(ring: List<LatLng>): List<LatLng> {
        return ring.filter { coord ->
            coord.latitude.isFinite() && 
            coord.longitude.isFinite() &&
            coord.latitude in -90.0..90.0 &&
            coord.longitude in -180.0..180.0
        }
    }
    
    /**
     * 合并重复顶点
     */
    private fun mergeDuplicateVertices(
        polygon: PolygonInProgress,
        polygonIndex: Int
    ): SingleRepairResult {
        val appliedFixes = mutableListOf<AppliedFix>()
        var verticesRemoved = 0
        
        // 处理外边界
        val repairedExterior = mergeRingDuplicateVertices(polygon.exterior)
        if (repairedExterior.size != polygon.exterior.size) {
            val removedCount = polygon.exterior.size - repairedExterior.size
            verticesRemoved += removedCount
            appliedFixes.add(AppliedFix(
                type = RepairType.MERGE_DUPLICATE_VERTICES,
                description = "外边界合并${removedCount}个重复顶点",
                polygonIndex = polygonIndex,
                beforeCount = polygon.exterior.size,
                afterCount = repairedExterior.size
            ))
        }
        
        // 处理孔洞
        val repairedHoles = polygon.holes.mapIndexed { holeIndex, hole ->
            val repairedHole = mergeRingDuplicateVertices(hole)
            if (repairedHole.size != hole.size) {
                val removedCount = hole.size - repairedHole.size
                verticesRemoved += removedCount
                appliedFixes.add(AppliedFix(
                    type = RepairType.MERGE_DUPLICATE_VERTICES,
                    description = "孔洞$holeIndex 合并${removedCount}个重复顶点",
                    polygonIndex = polygonIndex,
                    beforeCount = hole.size,
                    afterCount = repairedHole.size
                ))
            }
            repairedHole
        }
        
        val hasChanges = appliedFixes.isNotEmpty()
        val repairedPolygon = if (hasChanges) {
            polygon.copy(exterior = repairedExterior, holes = repairedHoles)
        } else null
        
        return SingleRepairResult(
            repairedPolygon = repairedPolygon,
            appliedFixes = appliedFixes,
            hasChanges = hasChanges,
            verticesRemoved = verticesRemoved
        )
    }
    
    /**
     * 合并环中的重复顶点
     */
    private fun mergeRingDuplicateVertices(ring: List<LatLng>): List<LatLng> {
        if (ring.size <= 1) return ring
        
        val merged = mutableListOf<LatLng>()
        merged.add(ring[0])
        
        for (i in 1 until ring.size) {
            val current = ring[i]
            val last = merged.last()
            
            val distance = GeometryUtils.distanceBetween(current, last)
            if (distance >= MERGE_DISTANCE_THRESHOLD_M) {
                merged.add(current)
            }
        }
        
        // 确保至少有3个顶点（对于多边形）
        return if (merged.size < 3 && ring.size >= 3) {
            ring.take(3)
        } else merged
    }
    
    /**
     * 移除共线顶点
     */
    private fun removeCollinearVertices(
        polygon: PolygonInProgress,
        polygonIndex: Int
    ): SingleRepairResult {
        val appliedFixes = mutableListOf<AppliedFix>()
        var verticesRemoved = 0
        
        // 处理外边界
        val repairedExterior = removeRingCollinearVertices(polygon.exterior)
        if (repairedExterior.size != polygon.exterior.size) {
            val removedCount = polygon.exterior.size - repairedExterior.size
            verticesRemoved += removedCount
            appliedFixes.add(AppliedFix(
                type = RepairType.REMOVE_COLLINEAR_VERTICES,
                description = "外边界移除${removedCount}个共线顶点",
                polygonIndex = polygonIndex,
                beforeCount = polygon.exterior.size,
                afterCount = repairedExterior.size
            ))
        }
        
        // 处理孔洞
        val repairedHoles = polygon.holes.mapIndexed { holeIndex, hole ->
            val repairedHole = removeRingCollinearVertices(hole)
            if (repairedHole.size != hole.size) {
                val removedCount = hole.size - repairedHole.size
                verticesRemoved += removedCount
                appliedFixes.add(AppliedFix(
                    type = RepairType.REMOVE_COLLINEAR_VERTICES,
                    description = "孔洞$holeIndex 移除${removedCount}个共线顶点",
                    polygonIndex = polygonIndex,
                    beforeCount = hole.size,
                    afterCount = repairedHole.size
                ))
            }
            repairedHole
        }
        
        val hasChanges = appliedFixes.isNotEmpty()
        val repairedPolygon = if (hasChanges) {
            polygon.copy(exterior = repairedExterior, holes = repairedHoles)
        } else null
        
        return SingleRepairResult(
            repairedPolygon = repairedPolygon,
            appliedFixes = appliedFixes,
            hasChanges = hasChanges,
            verticesRemoved = verticesRemoved
        )
    }
    
    /**
     * 移除环中的共线顶点
     */
    private fun removeRingCollinearVertices(ring: List<LatLng>): List<LatLng> {
        if (ring.size <= 3) return ring
        
        val filtered = mutableListOf<LatLng>()
        
        for (i in ring.indices) {
            val prev = ring[if (i == 0) ring.size - 1 else i - 1]
            val current = ring[i]
            val next = ring[(i + 1) % ring.size]
            
            val angle = calculateAngle(prev, current, next)
            val angleDegrees = Math.toDegrees(angle)
            
            // 保留非共线顶点（角度不接近180度）
            if (abs(angleDegrees - 180.0) > 1.0) {
                filtered.add(current)
            }
        }
        
        // 确保至少有3个顶点
        return if (filtered.size < 3) ring else filtered
    }
    
    /**
     * 计算三点间的角度
     */
    private fun calculateAngle(p1: LatLng, vertex: LatLng, p2: LatLng): Double {
        val dx1 = p1.longitude - vertex.longitude
        val dy1 = p1.latitude - vertex.latitude
        val dx2 = p2.longitude - vertex.longitude
        val dy2 = p2.latitude - vertex.latitude
        
        val angle1 = atan2(dy1, dx1)
        val angle2 = atan2(dy2, dx2)
        
        var angle = abs(angle2 - angle1)
        if (angle > PI) {
            angle = 2 * PI - angle
        }
        
        return angle
    }
    
    /**
     * 修复自相交
     */
    private fun fixSelfIntersection(
        polygon: PolygonInProgress,
        polygonIndex: Int
    ): SingleRepairResult {
        val appliedFixes = mutableListOf<AppliedFix>()
        var verticesRemoved = 0
        
        // 尝试基础的自相交修复
        val repairedExterior = fixRingSelfIntersection(polygon.exterior)
        if (repairedExterior != null && repairedExterior.size != polygon.exterior.size) {
            val removedCount = polygon.exterior.size - repairedExterior.size
            verticesRemoved += removedCount
            appliedFixes.add(AppliedFix(
                type = RepairType.FIX_SELF_INTERSECTION,
                description = "外边界修复自相交，移除${removedCount}个顶点",
                polygonIndex = polygonIndex,
                beforeCount = polygon.exterior.size,
                afterCount = repairedExterior.size
            ))
        }
        
        // 处理孔洞的自相交
        val repairedHoles = polygon.holes.mapIndexed { holeIndex, hole ->
            val repairedHole = fixRingSelfIntersection(hole)
            if (repairedHole != null && repairedHole.size != hole.size) {
                val removedCount = hole.size - repairedHole.size
                verticesRemoved += removedCount
                appliedFixes.add(AppliedFix(
                    type = RepairType.FIX_SELF_INTERSECTION,
                    description = "孔洞$holeIndex 修复自相交，移除${removedCount}个顶点",
                    polygonIndex = polygonIndex,
                    beforeCount = hole.size,
                    afterCount = repairedHole.size
                ))
            }
            repairedHole ?: hole
        }
        
        val hasChanges = appliedFixes.isNotEmpty()
        val repairedPolygon = if (hasChanges) {
            polygon.copy(
                exterior = repairedExterior ?: polygon.exterior, 
                holes = repairedHoles
            )
        } else null
        
        return SingleRepairResult(
            repairedPolygon = repairedPolygon,
            appliedFixes = appliedFixes,
            hasChanges = hasChanges,
            verticesRemoved = verticesRemoved
        )
    }
    
    /**
     * 修复环的自相交（简化版本）
     */
    private fun fixRingSelfIntersection(ring: List<LatLng>): List<LatLng>? {
        if (ring.size < 4) return null
        
        // 检测并移除相交的线段
        val cleanedRing = mutableListOf<LatLng>()
        val processed = BooleanArray(ring.size) { false }
        
        var i = 0
        while (i < ring.size) {
            if (processed[i]) {
                i++
                continue
            }
            
            cleanedRing.add(ring[i])
            processed[i] = true
            
            // 检查当前点到后续点是否形成相交
            for (j in i + 2 until ring.size - 1) {
                if (processed[j]) continue
                
                val current = ring[i]
                val next = ring[(i + 1) % ring.size]
                val checkStart = ring[j]
                val checkEnd = ring[(j + 1) % ring.size]
                
                // 简单的线段相交检测
                if (doLineSegmentsIntersect(current, next, checkStart, checkEnd)) {
                    // 发现相交，跳过中间的点
                    for (k in i + 1..j) {
                        processed[k] = true
                    }
                    i = j
                    break
                }
            }
            i++
        }
        
        // 如果移除了顶点且仍有足够的顶点形成多边形
        return if (cleanedRing.size >= 3 && cleanedRing.size < ring.size) {
            cleanedRing
        } else null
    }
    
    /**
     * 检测两条线段是否相交
     */
    private fun doLineSegmentsIntersect(
        p1: LatLng, q1: LatLng,
        p2: LatLng, q2: LatLng
    ): Boolean {
        // CCW 方向检测
        fun ccw(A: LatLng, B: LatLng, C: LatLng): Boolean {
            return (C.latitude - A.latitude) * (B.longitude - A.longitude) > 
                   (B.latitude - A.latitude) * (C.longitude - A.longitude)
        }
        
        return ccw(p1, p2, q2) != ccw(q1, p2, q2) && ccw(p1, q1, p2) != ccw(p1, q1, q2)
    }
    
    /**
     * 平滑锐角
     */
    private fun smoothAcuteAngles(
        polygon: PolygonInProgress,
        polygonIndex: Int,
        strategy: RepairStrategy
    ): SingleRepairResult {
        if (!strategy.preserveOriginalShape) {
            // 可以实现角度平滑算法
            // 目前保守处理，不修改形状
        }
        
        return SingleRepairResult(
            repairedPolygon = null,
            appliedFixes = emptyList(),
            hasChanges = false
        )
    }
    
    /**
     * 修复环方向
     */
    private fun fixRingOrientation(
        polygon: PolygonInProgress,
        polygonIndex: Int
    ): SingleRepairResult {
        val appliedFixes = mutableListOf<AppliedFix>()
        var hasChanges = false
        
        // 检查外边界方向（应该是逆时针）
        val exteriorIsCounterClockwise = isCounterClockwise(polygon.exterior)
        val repairedExterior = if (!exteriorIsCounterClockwise) {
            hasChanges = true
            appliedFixes.add(AppliedFix(
                type = RepairType.FIX_RING_ORIENTATION,
                description = "修复外边界方向为逆时针",
                polygonIndex = polygonIndex
            ))
            polygon.exterior.reversed()
        } else {
            polygon.exterior
        }
        
        // 检查孔洞方向（应该是顺时针）
        val repairedHoles = polygon.holes.mapIndexed { holeIndex, hole ->
            val holeIsCounterClockwise = isCounterClockwise(hole)
            if (holeIsCounterClockwise) {
                hasChanges = true
                appliedFixes.add(AppliedFix(
                    type = RepairType.FIX_RING_ORIENTATION,
                    description = "修复孔洞$holeIndex 方向为顺时针",
                    polygonIndex = polygonIndex
                ))
                hole.reversed()
            } else {
                hole
            }
        }
        
        val repairedPolygon = if (hasChanges) {
            polygon.copy(exterior = repairedExterior, holes = repairedHoles)
        } else null
        
        return SingleRepairResult(
            repairedPolygon = repairedPolygon,
            appliedFixes = appliedFixes,
            hasChanges = hasChanges
        )
    }
    
    /**
     * 判断环是否为逆时针方向
     */
    private fun isCounterClockwise(ring: List<LatLng>): Boolean {
        if (ring.size < 3) return true
        
        // 使用Shoelace公式计算有符号面积
        var signedArea = 0.0
        for (i in ring.indices) {
            val j = (i + 1) % ring.size
            signedArea += (ring[j].longitude - ring[i].longitude) * (ring[j].latitude + ring[i].latitude)
        }
        
        return signedArea < 0 // 负值表示逆时针
    }
    
    /**
     * 移动孔洞到边界内
     */
    private fun moveHolesInsideBoundary(
        polygon: PolygonInProgress,
        polygonIndex: Int
    ): SingleRepairResult {
        val appliedFixes = mutableListOf<AppliedFix>()
        
        // 简化实现：移除完全在边界外的孔洞
        val validHoles = polygon.holes.filterIndexed { holeIndex, hole ->
            val isInside = hole.all { vertex ->
                GeometryUtils.isPointInPolygon(vertex, polygon.exterior)
            }
            
            if (!isInside) {
                appliedFixes.add(AppliedFix(
                    type = RepairType.MOVE_HOLE_INSIDE_BOUNDARY,
                    description = "移除边界外的孔洞$holeIndex",
                    polygonIndex = polygonIndex
                ))
            }
            
            isInside
        }
        
        val hasChanges = validHoles.size != polygon.holes.size
        val repairedPolygon = if (hasChanges) {
            polygon.copy(holes = validHoles)
        } else null
        
        return SingleRepairResult(
            repairedPolygon = repairedPolygon,
            appliedFixes = appliedFixes,
            hasChanges = hasChanges
        )
    }
    
    /**
     * 分离相交孔洞
     */
    private fun separateIntersectingHoles(
        polygon: PolygonInProgress,
        polygonIndex: Int
    ): SingleRepairResult {
        // 简化实现：目前仅检测不修复
        // 实际应用中可能需要复杂的几何计算
        return SingleRepairResult(
            repairedPolygon = null,
            appliedFixes = emptyList(),
            hasChanges = false
        )
    }
    
    /**
     * 简化几何体
     */
    private fun simplifyGeometry(
        polygon: PolygonInProgress,
        polygonIndex: Int,
        strategy: RepairStrategy
    ): SingleRepairResult {
        if (strategy.preserveOriginalShape) {
            return SingleRepairResult(
                repairedPolygon = null,
                appliedFixes = emptyList(),
                hasChanges = false
            )
        }
        
        val appliedFixes = mutableListOf<AppliedFix>()
        var verticesRemoved = 0
        
        // 使用道格拉斯-普克算法简化
        val simplifiedExterior = simplifyRing(polygon.exterior, SIMPLIFY_DISTANCE_THRESHOLD_M)
        if (simplifiedExterior.size != polygon.exterior.size) {
            val removedCount = polygon.exterior.size - simplifiedExterior.size
            verticesRemoved += removedCount
            appliedFixes.add(AppliedFix(
                type = RepairType.SIMPLIFY_GEOMETRY,
                description = "外边界简化，移除${removedCount}个顶点",
                polygonIndex = polygonIndex,
                beforeCount = polygon.exterior.size,
                afterCount = simplifiedExterior.size
            ))
        }
        
        val simplifiedHoles = polygon.holes.mapIndexed { holeIndex, hole ->
            val simplifiedHole = simplifyRing(hole, SIMPLIFY_DISTANCE_THRESHOLD_M)
            if (simplifiedHole.size != hole.size) {
                val removedCount = hole.size - simplifiedHole.size
                verticesRemoved += removedCount
                appliedFixes.add(AppliedFix(
                    type = RepairType.SIMPLIFY_GEOMETRY,
                    description = "孔洞$holeIndex 简化，移除${removedCount}个顶点",
                    polygonIndex = polygonIndex,
                    beforeCount = hole.size,
                    afterCount = simplifiedHole.size
                ))
            }
            simplifiedHole
        }
        
        val hasChanges = appliedFixes.isNotEmpty()
        val repairedPolygon = if (hasChanges) {
            polygon.copy(exterior = simplifiedExterior, holes = simplifiedHoles)
        } else null
        
        return SingleRepairResult(
            repairedPolygon = repairedPolygon,
            appliedFixes = appliedFixes,
            hasChanges = hasChanges,
            verticesRemoved = verticesRemoved
        )
    }
    
    /**
     * 简化环（道格拉斯-普克算法的简化版本）
     */
    private fun simplifyRing(ring: List<LatLng>, tolerance: Double): List<LatLng> {
        if (ring.size <= 3) return ring
        
        // 简化实现：移除距离过近的连续顶点
        val simplified = mutableListOf<LatLng>()
        simplified.add(ring[0])
        
        for (i in 1 until ring.size) {
            val current = ring[i]
            val last = simplified.last()
            
            val distance = GeometryUtils.distanceBetween(current, last)
            if (distance >= tolerance) {
                simplified.add(current)
            }
        }
        
        // 确保至少有3个顶点
        return if (simplified.size < 3) ring.take(3) else simplified
    }
    
    /**
     * 优化顶点密度
     */
    private fun optimizeVertexDensity(
        polygon: PolygonInProgress,
        polygonIndex: Int,
        strategy: RepairStrategy
    ): SingleRepairResult {
        // 检查顶点数量是否过多
        val totalVertices = polygon.exterior.size + polygon.holes.sumOf { it.size }
        val maxAllowedVertices = (totalVertices * (1.0 - strategy.maxVertexReduction)).toInt()
        
        if (totalVertices <= maxAllowedVertices) {
            return SingleRepairResult(
                repairedPolygon = null,
                appliedFixes = emptyList(),
                hasChanges = false
            )
        }
        
        // 需要减少顶点数量，使用几何简化
        return simplifyGeometry(polygon, polygonIndex, strategy)
    }
    
    // ========== 公共接口方法 ==========
    
    /**
     * 快速修复（仅基础修复）
     */
    fun quickRepair(landPlot: LandPlotInProgress): RepairResult {
        return repairLandPlot(landPlot, RepairStrategy.CONSERVATIVE)
    }
    
    /**
     * 激进修复（所有修复类型）
     */
    fun aggressiveRepair(landPlot: LandPlotInProgress): RepairResult {
        return repairLandPlot(landPlot, RepairStrategy.AGGRESSIVE)
    }
    
    /**
     * 获取修复建议
     */
    fun getRepairSuggestions(
        validationResult: GeometryValidator.ValidationResult
    ): List<RepairSuggestion> {
        val suggestions = mutableListOf<RepairSuggestion>()
        
        // 处理错误
        validationResult.errors.forEach { error ->
            val repairType = getRepairTypeForError(error.type)
            if (repairType != null) {
                suggestions.add(RepairSuggestion(
                    repairType = repairType,
                    errorType = error.type,
                    description = getRepairDescription(repairType),
                    priority = getRepairPriority(repairType),
                    isAutoRepairable = isAutoRepairable(repairType),
                    estimatedTimeMs = getEstimatedRepairTime(repairType)
                ))
            }
        }
        
        // 处理警告
        validationResult.warnings.forEach { warning ->
            val repairType = getRepairTypeForWarning(warning.type)
            if (repairType != null) {
                // 将警告类型转换为错误类型以统一处理
                val errorType = convertWarningToErrorType(warning.type)
                suggestions.add(RepairSuggestion(
                    repairType = repairType,
                    errorType = errorType,
                    description = getRepairDescription(repairType),
                    priority = getRepairPriority(repairType),
                    isAutoRepairable = isAutoRepairable(repairType),
                    estimatedTimeMs = getEstimatedRepairTime(repairType)
                ))
            }
        }
        
        return suggestions.sortedBy { it.priority }
    }
    
    /**
     * 修复建议
     */
    data class RepairSuggestion(
        val repairType: RepairType,
        val errorType: GeometryValidator.ErrorType,
        val description: String,
        val priority: Int, // 1-10，1为最高优先级
        val isAutoRepairable: Boolean,
        val estimatedTimeMs: Long
    )
    
    /**
     * 获取修复描述
     */
    private fun getRepairDescription(repairType: RepairType): String {
        return when (repairType) {
            RepairType.VALIDATE_COORDINATES -> "验证并修复无效坐标"
            RepairType.MERGE_DUPLICATE_VERTICES -> "合并重复或过近的顶点"
            RepairType.REMOVE_COLLINEAR_VERTICES -> "移除共线的多余顶点"
            RepairType.FIX_SELF_INTERSECTION -> "修复自相交问题"
            RepairType.SMOOTH_ACUTE_ANGLES -> "平滑过小的锐角"
            RepairType.FIX_RING_ORIENTATION -> "修复环的方向"
            RepairType.MOVE_HOLE_INSIDE_BOUNDARY -> "调整孔洞位置到边界内"
            RepairType.SEPARATE_INTERSECTING_HOLES -> "分离相交的孔洞"
            RepairType.SIMPLIFY_GEOMETRY -> "简化几何体，减少顶点数量"
            RepairType.OPTIMIZE_VERTEX_DENSITY -> "优化顶点密度"
        }
    }
    
    /**
     * 获取修复优先级
     */
    private fun getRepairPriority(repairType: RepairType): Int {
        return when (repairType) {
            RepairType.VALIDATE_COORDINATES -> 1
            RepairType.MERGE_DUPLICATE_VERTICES -> 2
            RepairType.REMOVE_COLLINEAR_VERTICES -> 3
            RepairType.FIX_RING_ORIENTATION -> 4
            RepairType.MOVE_HOLE_INSIDE_BOUNDARY -> 5
            RepairType.SEPARATE_INTERSECTING_HOLES -> 6
            RepairType.FIX_SELF_INTERSECTION -> 7
            RepairType.SMOOTH_ACUTE_ANGLES -> 8
            RepairType.OPTIMIZE_VERTEX_DENSITY -> 9
            RepairType.SIMPLIFY_GEOMETRY -> 10
        }
    }
    
    /**
     * 检查是否可自动修复
     */
    private fun isAutoRepairable(repairType: RepairType): Boolean {
        return when (repairType) {
            RepairType.VALIDATE_COORDINATES,
            RepairType.MERGE_DUPLICATE_VERTICES,
            RepairType.REMOVE_COLLINEAR_VERTICES,
            RepairType.FIX_RING_ORIENTATION,
            RepairType.MOVE_HOLE_INSIDE_BOUNDARY -> true
            else -> false
        }
    }
    
    /**
     * 估算修复时间
     */
    private fun getEstimatedRepairTime(repairType: RepairType): Long {
        return when (repairType) {
            RepairType.VALIDATE_COORDINATES,
            RepairType.FIX_RING_ORIENTATION -> 10L
            RepairType.MERGE_DUPLICATE_VERTICES,
            RepairType.REMOVE_COLLINEAR_VERTICES -> 50L
            RepairType.MOVE_HOLE_INSIDE_BOUNDARY,
            RepairType.SEPARATE_INTERSECTING_HOLES -> 100L
            RepairType.OPTIMIZE_VERTEX_DENSITY,
            RepairType.SIMPLIFY_GEOMETRY -> 200L
            RepairType.SMOOTH_ACUTE_ANGLES,
            RepairType.FIX_SELF_INTERSECTION -> 500L
        }
    }
} 