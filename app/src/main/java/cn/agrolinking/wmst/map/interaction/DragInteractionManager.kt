package cn.agrolinking.wmst.map.interaction

import android.graphics.PointF
import android.util.Log
import cn.agrolinking.wmst.domain.drawing.LandPlotInProgress
import cn.agrolinking.wmst.map.util.GeometryUtils
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.maps.MapLibreMap
import kotlin.math.abs
import kotlin.math.cos
import kotlin.math.sin

/**
 * 拖拽交互管理器
 * 处理复杂几何体（多多边形+孔洞）的顶点和边线中点拖拽逻辑
 * 支持拖拽预览、智能吸附和几何体约束
 */
class DragInteractionManager(
    private val map: MapLibreMap
) {
    companion object {
        private const val TAG = "DragInteractionManager"
        
        // 吸附阈值（像素）
        const val SNAP_THRESHOLD_PX = 40f
        
        // 顶点半径（像素）- 大幅增大以便更容易点击
        const val VERTEX_RADIUS_PX = 60f
        
        // 边线触摸阈值（像素）
        const val EDGE_TOUCH_THRESHOLD_PX = 30f
        
        // 网格吸附间隔（米）
        const val GRID_SNAP_INTERVAL_METERS = 10.0
        
        // 角度吸附阈值（度）
        const val ANGLE_SNAP_THRESHOLD_DEGREES = 15.0
        
        // 最小多边形面积（平方米）
        const val MIN_POLYGON_AREA_SQM = 1.0
    }
    
    /**
     * 拖拽目标类型 - 支持复杂几何体定位
     */
    sealed class DragTarget {
        /**
         * 顶点拖拽
         * @param polygonIndex 多边形索引
         * @param ringIndex 环索引 (0=外边界, >0=孔洞)
         * @param vertexIndex 顶点索引
         */
        data class Vertex(
            val polygonIndex: Int,
            val ringIndex: Int,
            val vertexIndex: Int
        ) : DragTarget()
        
        /**
         * 边线中点拖拽
         * @param polygonIndex 多边形索引
         * @param ringIndex 环索引 (0=外边界, >0=孔洞)
         * @param edgeStartIndex 边线起点索引
         * @param edgeEndIndex 边线终点索引
         */
        data class EdgeMidpoint(
            val polygonIndex: Int,
            val ringIndex: Int,
            val edgeStartIndex: Int,
            val edgeEndIndex: Int
        ) : DragTarget()
    }
    
    /**
     * 吸附类型
     */
    sealed class SnapType {
        object None : SnapType()
        data class Vertex(val target: LatLng) : SnapType()
        data class Grid(val target: LatLng) : SnapType()
        data class Angle(val target: LatLng, val angle: Double) : SnapType()
        
        val snapTarget: LatLng?
            get() = when (this) {
                is None -> null
                is Vertex -> target
                is Grid -> target
                is Angle -> target
            }
    }
    
    /**
     * 拖拽预览信息
     */
    data class DragPreview(
        val originalPoint: LatLng,
        val currentPoint: LatLng,
        val previewPoint: LatLng, // 吸附后的预览点
        val snapType: SnapType,
        val isValidGeometry: Boolean,
        val validationMessage: String? = null
    )
    
    /**
     * 拖拽状态
     */
    data class DragState(
        val isDragging: Boolean = false,
        val target: DragTarget? = null,
        val startPoint: LatLng? = null,
        val currentPoint: LatLng? = null,
        val preview: DragPreview? = null,
        val snapCandidates: List<LatLng> = emptyList()
    )
    
    private var dragState = DragState()
    
    // 拖拽预览回调
    var onDragPreviewUpdate: ((DragPreview?) -> Unit)? = null
    
    // 吸附设置
    var enableVertexSnap: Boolean = true
    var enableGridSnap: Boolean = true
    var enableAngleSnap: Boolean = true
    var enableGeometryValidation: Boolean = true
    
    /**
     * 检测触摸点是否命中复杂几何体中的可拖拽元素
     * @param touchPoint 触摸点的屏幕坐标
     * @param landPlot 复杂地块数据
     * @return 如果命中返回DragTarget，否则返回null
     */
    fun detectDragTarget(touchPoint: PointF, landPlot: LandPlotInProgress): DragTarget? {
        // 优先检测顶点
        landPlot.polygons.forEachIndexed { polygonIndex, polygon ->
            // 检测外边界顶点
            polygon.exterior.forEachIndexed { vertexIndex, vertex ->
                val screenPoint = map.projection.toScreenLocation(vertex)
                val distance = GeometryUtils.distanceBetweenScreenPoints(
                    touchPoint.x, touchPoint.y,
                    screenPoint.x, screenPoint.y
                )
                
                if (distance <= VERTEX_RADIUS_PX) {
                    Log.d(TAG, "检测到外边界顶点拖拽目标，多边形: $polygonIndex, 顶点: $vertexIndex")
                    return DragTarget.Vertex(polygonIndex, 0, vertexIndex)
                }
            }
            
            // 检测孔洞顶点
            polygon.holes.forEachIndexed { holeIndex, hole ->
                hole.forEachIndexed { vertexIndex, vertex ->
                    val screenPoint = map.projection.toScreenLocation(vertex)
                    val distance = GeometryUtils.distanceBetweenScreenPoints(
                        touchPoint.x, touchPoint.y,
                        screenPoint.x, screenPoint.y
                    )
                    
                    if (distance <= VERTEX_RADIUS_PX) {
                        Log.d(TAG, "检测到孔洞顶点拖拽目标，多边形: $polygonIndex, 孔洞: $holeIndex, 顶点: $vertexIndex")
                        return DragTarget.Vertex(polygonIndex, holeIndex + 1, vertexIndex)
                    }
                }
            }
        }
        
        // 检测边线中点
        landPlot.polygons.forEachIndexed { polygonIndex, polygon ->
            // 检测外边界边线
            if (polygon.exterior.size >= 3) {
                val midpointTarget = detectRingEdgeMidpoint(
                    touchPoint, polygon.exterior, polygonIndex, 0
                )
                if (midpointTarget != null) return midpointTarget
            }
            
            // 检测孔洞边线
            polygon.holes.forEachIndexed { holeIndex, hole ->
                if (hole.size >= 3) {
                    val midpointTarget = detectRingEdgeMidpoint(
                        touchPoint, hole, polygonIndex, holeIndex + 1
                    )
                    if (midpointTarget != null) return midpointTarget
                }
            }
        }
        
        return null
    }
    
    /**
     * 检测单个环的边线中点
     */
    private fun detectRingEdgeMidpoint(
        touchPoint: PointF,
        ring: List<LatLng>,
        polygonIndex: Int,
        ringIndex: Int
    ): DragTarget.EdgeMidpoint? {
        for (i in ring.indices) {
            val startIndex = i
            val endIndex = (i + 1) % ring.size
            val startPoint = ring[startIndex]
            val endPoint = ring[endIndex]
            
            val startScreen = map.projection.toScreenLocation(startPoint)
            val endScreen = map.projection.toScreenLocation(endPoint)
            
            val distanceToEdge = GeometryUtils.distancePointToLineSegment(
                touchPoint.x, touchPoint.y,
                startScreen.x, startScreen.y,
                endScreen.x, endScreen.y
            )
            
            if (distanceToEdge <= EDGE_TOUCH_THRESHOLD_PX) {
                Log.d(TAG, "检测到边线拖拽目标，多边形: $polygonIndex, 环: $ringIndex, 边: $startIndex -> $endIndex")
                return DragTarget.EdgeMidpoint(polygonIndex, ringIndex, startIndex, endIndex)
            }
        }
        return null
    }
    
    /**
     * 兼容性方法：检测简单多边形的拖拽目标（保持向后兼容）
     */
    fun detectDragTarget(touchPoint: PointF, polygonPoints: List<LatLng>): DragTarget? {
        Log.d(TAG, "开始检测拖拽目标，触摸点: (${touchPoint.x}, ${touchPoint.y}), 多边形顶点数: ${polygonPoints.size}")

        if (polygonPoints.size < 3) {
            Log.d(TAG, "多边形顶点数不足，无法检测拖拽目标")
            return null
        }

        // 优先检测顶点
        polygonPoints.forEachIndexed { index, vertex ->
            val screenPoint = map.projection.toScreenLocation(vertex)
            val distance = GeometryUtils.distanceBetweenScreenPoints(
                touchPoint.x, touchPoint.y,
                screenPoint.x, screenPoint.y
            )

            Log.d(TAG, "顶点$index: 地理坐标(${"%.6f".format(vertex.latitude)}, ${"%.6f".format(vertex.longitude)}) -> 屏幕坐标(${screenPoint.x}, ${screenPoint.y}), 距离: ${"%.1f".format(distance)}px")

            if (distance <= VERTEX_RADIUS_PX) {
                Log.d(TAG, "✅ 检测到顶点拖拽目标，索引: $index, 距离: ${"%.1f".format(distance)}px <= ${VERTEX_RADIUS_PX}px")
                return DragTarget.Vertex(0, 0, index) // 转换为复杂几何体格式
            }
        }
        
        // 检测边线中点
        for (i in polygonPoints.indices) {
            val startIndex = i
            val endIndex = (i + 1) % polygonPoints.size
            val startPoint = polygonPoints[startIndex]
            val endPoint = polygonPoints[endIndex]
            
            val startScreen = map.projection.toScreenLocation(startPoint)
            val endScreen = map.projection.toScreenLocation(endPoint)
            
            val distanceToEdge = GeometryUtils.distancePointToLineSegment(
                touchPoint.x, touchPoint.y,
                startScreen.x, startScreen.y,
                endScreen.x, endScreen.y
            )
            
            if (distanceToEdge <= EDGE_TOUCH_THRESHOLD_PX) {
                Log.d(TAG, "检测到边线拖拽目标，边: $startIndex -> $endIndex")
                return DragTarget.EdgeMidpoint(0, 0, startIndex, endIndex) // 转换为复杂几何体格式
            }
        }
        
        return null
    }
    
    /**
     * 开始拖拽
     */
    fun startDrag(target: DragTarget, startPoint: LatLng, landPlot: LandPlotInProgress? = null) {
        val snapCandidates = generateSnapCandidates(startPoint, landPlot)
        
        dragState = dragState.copy(
            isDragging = true,
            target = target,
            startPoint = startPoint,
            currentPoint = startPoint,
            snapCandidates = snapCandidates
        )
        
        Log.d(TAG, "开始拖拽: $target, 吸附候选点: ${snapCandidates.size}个")
    }
    
    /**
     * 更新拖拽位置
     */
    fun updateDrag(newPoint: LatLng) {
        if (dragState.isDragging) {
            dragState = dragState.copy(currentPoint = newPoint)
        }
    }
    
    /**
     * 结束拖拽
     */
    fun endDrag(): LatLng? {
        val finalPoint = dragState.preview?.previewPoint ?: dragState.currentPoint
        
        dragState = DragState() // 重置状态
        onDragPreviewUpdate?.invoke(null) // 清除预览
        
        Log.d(TAG, "拖拽结束，最终位置: $finalPoint")
        return finalPoint
    }
    
    /**
     * 取消拖拽
     */
    fun cancelDrag() {
        dragState = DragState()
        onDragPreviewUpdate?.invoke(null)
        Log.d(TAG, "拖拽已取消")
    }
    
    /**
     * 获取当前拖拽状态
     */
    fun getDragState(): DragState = dragState
    
    /**
     * 应用拖拽结果到复杂地块数据
     * @param originalLandPlot 原始地块数据
     * @param finalPoint 拖拽的最终位置
     * @return 更新后的地块数据
     */
    fun applyDragResult(originalLandPlot: LandPlotInProgress, finalPoint: LatLng): LandPlotInProgress {
        val target = dragState.target ?: return originalLandPlot
        
        return when (target) {
            is DragTarget.Vertex -> {
                // 移动顶点
                originalLandPlot.updateVertex(target, finalPoint)
            }
            is DragTarget.EdgeMidpoint -> {
                // 在边线中点插入新顶点
                originalLandPlot.insertVertex(target, finalPoint)
            }
        }
    }
    
    /**
     * 兼容性方法：应用拖拽结果到简单多边形
     */
    fun applyDragResult(originalPoints: List<LatLng>, finalPoint: LatLng): List<LatLng> {
        val target = dragState.target ?: return originalPoints
        
        return when (target) {
            is DragTarget.Vertex -> {
                // 移动顶点
                originalPoints.toMutableList().apply {
                    if (target.vertexIndex < size) {
                        this[target.vertexIndex] = finalPoint
                    }
                }
            }
            is DragTarget.EdgeMidpoint -> {
                // 在边线中点插入新顶点
                originalPoints.toMutableList().apply {
                    val insertIndex = target.edgeEndIndex
                    add(insertIndex, finalPoint)
                }
            }
        }
    }
    
    /**
     * 计算边线中点
     */
    fun getEdgeMidpoint(points: List<LatLng>, edgeStartIndex: Int, edgeEndIndex: Int): LatLng? {
        if (edgeStartIndex >= points.size || edgeEndIndex >= points.size) return null
        
        val startPoint = points[edgeStartIndex]
        val endPoint = points[edgeEndIndex]
        return GeometryUtils.midPoint(startPoint, endPoint)
    }
    
    /**
     * 获取所有边线中点（用于渲染）
     */
    fun getAllEdgeMidpoints(points: List<LatLng>): List<Pair<Int, LatLng>> {
        if (points.size < 3) return emptyList()
        
        return points.indices.map { i ->
            val startIndex = i
            val endIndex = (i + 1) % points.size
            val midpoint = GeometryUtils.midPoint(points[startIndex], points[endIndex])
            Pair(startIndex, midpoint)
        }
    }
    
    /**
     * 获取复杂地块的所有边线中点（用于渲染）
     */
    fun getAllEdgeMidpoints(landPlot: LandPlotInProgress): List<EdgeMidpointInfo> {
        val midpoints = mutableListOf<EdgeMidpointInfo>()
        
        landPlot.polygons.forEachIndexed { polygonIndex, polygon ->
            // 外边界中点
            if (polygon.exterior.size >= 3) {
                polygon.exterior.indices.forEach { i ->
                    val startIndex = i
                    val endIndex = (i + 1) % polygon.exterior.size
                    val midpoint = GeometryUtils.midPoint(
                        polygon.exterior[startIndex],
                        polygon.exterior[endIndex]
                    )
                    midpoints.add(
                        EdgeMidpointInfo(
                            polygonIndex = polygonIndex,
                            ringIndex = 0,
                            edgeStartIndex = startIndex,
                            midpoint = midpoint
                        )
                    )
                }
            }
            
            // 孔洞中点
            polygon.holes.forEachIndexed { holeIndex, hole ->
                if (hole.size >= 3) {
                    hole.indices.forEach { i ->
                        val startIndex = i
                        val endIndex = (i + 1) % hole.size
                        val midpoint = GeometryUtils.midPoint(hole[startIndex], hole[endIndex])
                        midpoints.add(
                            EdgeMidpointInfo(
                                polygonIndex = polygonIndex,
                                ringIndex = holeIndex + 1,
                                edgeStartIndex = startIndex,
                                midpoint = midpoint
                            )
                        )
                    }
                }
            }
        }
        
        return midpoints
    }
    
    /**
     * 边线中点信息
     */
    data class EdgeMidpointInfo(
        val polygonIndex: Int,
        val ringIndex: Int,
        val edgeStartIndex: Int,
        val midpoint: LatLng
    )
    
    /**
     * 检测是否正在拖拽
     */
    fun isDragging(): Boolean = dragState.isDragging
    
    /**
     * 获取当前拖拽的目标类型
     */
    fun getCurrentDragTarget(): DragTarget? = dragState.target
    
    // ========== 拖拽预览和吸附功能 ==========
    
    /**
     * 更新拖拽位置（带预览和吸附）
     * @param newPoint 新的拖拽位置
     * @param landPlot 当前地块数据（用于几何体验证）
     */
    fun updateDragPosition(newPoint: LatLng, landPlot: LandPlotInProgress? = null): DragPreview? {
        if (!dragState.isDragging || dragState.target == null || dragState.startPoint == null) {
            return null
        }
        
        // 1. 计算吸附结果
        val snapResult = calculateSnap(newPoint, dragState.snapCandidates)
        val previewPoint = snapResult.snapTarget ?: newPoint
        
        // 2. 几何体有效性验证
        val validationResult = if (enableGeometryValidation && landPlot != null) {
            validateDragGeometry(landPlot, dragState.target!!, previewPoint)
        } else {
            Pair(true, null)
        }
        
        // 3. 创建预览对象
        val preview = DragPreview(
            originalPoint = dragState.startPoint!!,
            currentPoint = newPoint,
            previewPoint = previewPoint,
            snapType = snapResult,
            isValidGeometry = validationResult.first,
            validationMessage = validationResult.second
        )
        
        // 4. 更新状态
        dragState = dragState.copy(
            currentPoint = newPoint,
            preview = preview
        )
        
        // 5. 触发预览回调
        onDragPreviewUpdate?.invoke(preview)
        
        return preview
    }
    
    /**
     * 生成吸附候选点
     */
    private fun generateSnapCandidates(currentPoint: LatLng, landPlot: LandPlotInProgress?): List<LatLng> {
        val candidates = mutableListOf<LatLng>()
        
        // 1. 添加地块中的其他顶点作为吸附候选
        if (enableVertexSnap && landPlot != null) {
            landPlot.polygons.forEach { polygon ->
                candidates.addAll(polygon.exterior)
                polygon.holes.forEach { hole ->
                    candidates.addAll(hole)
                }
            }
        }
        
        // 2. 添加网格吸附点
        if (enableGridSnap) {
            candidates.addAll(generateGridSnapPoints(currentPoint))
        }
        
        // 移除当前点附近的候选点（避免自吸附）
        return candidates.filter { candidate ->
            GeometryUtils.distanceBetween(currentPoint, candidate) > 5.0 // 5米最小距离
        }
    }
    
    /**
     * 生成网格吸附点
     */
    private fun generateGridSnapPoints(centerPoint: LatLng): List<LatLng> {
        val gridPoints = mutableListOf<LatLng>()
        val gridSize = GRID_SNAP_INTERVAL_METERS
        
        // 在中心点周围生成3x3网格
        for (i in -1..1) {
            for (j in -1..1) {
                val offsetLat = i * gridSize / 111000.0 // 约1度 = 111km
                val offsetLng = j * gridSize / (111000.0 * cos(Math.toRadians(centerPoint.latitude)))
                
                gridPoints.add(
                    LatLng(
                        centerPoint.latitude + offsetLat,
                        centerPoint.longitude + offsetLng
                    )
                )
            }
        }
        
        return gridPoints
    }
    
    /**
     * 计算吸附结果
     */
    private fun calculateSnap(currentPoint: LatLng, candidates: List<LatLng>): SnapType {
        // 1. 顶点吸附检查
        if (enableVertexSnap) {
            val nearestVertex = candidates.minByOrNull { candidate ->
                GeometryUtils.distanceBetween(currentPoint, candidate)
            }
            
            nearestVertex?.let { vertex ->
                val distance = GeometryUtils.distanceBetween(currentPoint, vertex)
                val screenDistance = map.projection.toScreenLocation(vertex).let { vertexScreen ->
                    val currentScreen = map.projection.toScreenLocation(currentPoint)
                    GeometryUtils.distanceBetweenScreenPoints(
                        currentScreen.x, currentScreen.y,
                        vertexScreen.x, vertexScreen.y
                    )
                }
                
                if (screenDistance <= SNAP_THRESHOLD_PX) {
                    return SnapType.Vertex(vertex)
                }
            }
        }
        
        // 2. 网格吸附检查
        if (enableGridSnap) {
            val gridPoints = generateGridSnapPoints(currentPoint)
            val nearestGrid = gridPoints.minByOrNull { grid ->
                GeometryUtils.distanceBetween(currentPoint, grid)
            }
            
            nearestGrid?.let { grid ->
                val screenDistance = map.projection.toScreenLocation(grid).let { gridScreen ->
                    val currentScreen = map.projection.toScreenLocation(currentPoint)
                    GeometryUtils.distanceBetweenScreenPoints(
                        currentScreen.x, currentScreen.y,
                        gridScreen.x, gridScreen.y
                    )
                }
                
                if (screenDistance <= SNAP_THRESHOLD_PX / 2) { // 网格吸附阈值更小
                    return SnapType.Grid(grid)
                }
            }
        }
        
        // 3. 角度吸附检查
        if (enableAngleSnap && dragState.target is DragTarget.Vertex) {
            // TODO: 实现角度吸附（45度、90度等标准角度）
        }
        
        return SnapType.None
    }
    
    /**
     * 验证拖拽后的几何体有效性
     */
    private fun validateDragGeometry(
        landPlot: LandPlotInProgress,
        target: DragTarget,
        newPoint: LatLng
    ): Pair<Boolean, String?> {
        
        // 创建临时的更新后地块用于验证
        val tempLandPlot = when (target) {
            is DragTarget.Vertex -> landPlot.updateVertex(target, newPoint)
            is DragTarget.EdgeMidpoint -> landPlot.insertVertex(target, newPoint)
        }
        
        // 1. 检查多边形面积
        tempLandPlot.polygons.forEach { polygon ->
            if (polygon.exterior.size >= 3) {
                val area = GeometryUtils.calculatePolygonArea(polygon.exterior)
                if (area < MIN_POLYGON_AREA_SQM) {
                    return Pair(false, "多边形面积过小 (${String.format("%.1f", area)}m²)")
                }
            }
        }
        
        // 2. 检查自相交
        tempLandPlot.polygons.forEach { polygon ->
            if (GeometryUtils.hasLinesIntersect(polygon.exterior)) {
                return Pair(false, "检测到线段相交")
            }
            
            polygon.holes.forEach { hole ->
                if (GeometryUtils.hasLinesIntersect(hole)) {
                    return Pair(false, "孔洞中检测到线段相交")
                }
            }
        }
        
        // 3. 检查孔洞是否在外边界内
        tempLandPlot.polygons.forEach { polygon ->
            polygon.holes.forEach { hole ->
                if (hole.size >= 3) {
                    hole.forEach { holePoint ->
                        if (!GeometryUtils.isPointInPolygon(holePoint, polygon.exterior)) {
                            return Pair(false, "孔洞超出外边界范围")
                        }
                    }
                }
            }
        }
        
        return Pair(true, null)
    }
    
    /**
     * 获取当前拖拽预览
     */
    fun getCurrentPreview(): DragPreview? = dragState.preview
}

/**
 * LandPlotInProgress扩展方法 - 支持复杂几何体顶点操作
 */
private fun LandPlotInProgress.updateVertex(target: DragInteractionManager.DragTarget.Vertex, newPoint: LatLng): LandPlotInProgress {
    val updatedPolygons = polygons.toMutableList()
    
    if (target.polygonIndex in updatedPolygons.indices) {
        val polygon = updatedPolygons[target.polygonIndex]
        
        if (target.ringIndex == 0) {
            // 更新外边界顶点
            if (target.vertexIndex in polygon.exterior.indices) {
                val updatedExterior = polygon.exterior.toMutableList()
                updatedExterior[target.vertexIndex] = newPoint
                updatedPolygons[target.polygonIndex] = polygon.copy(exterior = updatedExterior)
            }
        } else {
            // 更新孔洞顶点
            val holeIndex = target.ringIndex - 1
            if (holeIndex in polygon.holes.indices && target.vertexIndex in polygon.holes[holeIndex].indices) {
                val updatedHoles = polygon.holes.toMutableList()
                val updatedHole = updatedHoles[holeIndex].toMutableList()
                updatedHole[target.vertexIndex] = newPoint
                updatedHoles[holeIndex] = updatedHole
                updatedPolygons[target.polygonIndex] = polygon.copy(holes = updatedHoles)
            }
        }
    }
    
    return copy(polygons = updatedPolygons)
}

private fun LandPlotInProgress.insertVertex(target: DragInteractionManager.DragTarget.EdgeMidpoint, newPoint: LatLng): LandPlotInProgress {
    val updatedPolygons = polygons.toMutableList()
    
    if (target.polygonIndex in updatedPolygons.indices) {
        val polygon = updatedPolygons[target.polygonIndex]
        
        if (target.ringIndex == 0) {
            // 在外边界插入顶点
            val updatedExterior = polygon.exterior.toMutableList()
            val insertIndex = target.edgeEndIndex
            updatedExterior.add(insertIndex, newPoint)
            updatedPolygons[target.polygonIndex] = polygon.copy(exterior = updatedExterior)
        } else {
            // 在孔洞插入顶点
            val holeIndex = target.ringIndex - 1
            if (holeIndex in polygon.holes.indices) {
                val updatedHoles = polygon.holes.toMutableList()
                val updatedHole = updatedHoles[holeIndex].toMutableList()
                val insertIndex = target.edgeEndIndex
                updatedHole.add(insertIndex, newPoint)
                updatedHoles[holeIndex] = updatedHole
                updatedPolygons[target.polygonIndex] = polygon.copy(holes = updatedHoles)
            }
        }
    }
    
    return copy(polygons = updatedPolygons)
} 