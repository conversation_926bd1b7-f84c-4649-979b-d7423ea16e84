package cn.agrolinking.wmst.map.coordinate

import cn.agrolinking.wmst.map.model.CoordinateSystem
import kotlin.math.*

/**
 * 坐标转换工具类
 * 用于处理不同坐标系之间的转换，解决地图图层偏移问题
 */
object CoordinateConverter {
    
    // 地球半径 (米)
    private const val EARTH_RADIUS = 6378245.0
    
    // 第一偏心率平方
    private const val EE = 0.00669342162296594323
    
    // 圆周率
    private const val PI = 3.1415926535897932384626
    
    /**
     * 根据源坐标系和目标坐标系执行坐标转换
     */
    fun convert(
        longitude: Double,
        latitude: Double,
        fromSystem: CoordinateSystem,
        toSystem: CoordinateSystem
    ): Pair<Double, Double> {
        
        // 如果坐标系相同，无需转换
        if (fromSystem == toSystem) {
            return Pair(longitude, latitude)
        }
        
        return when {
            // EPSG:3857 → GCJ02 (吉林一号 → 天地图/高德)
            fromSystem == CoordinateSystem.EPSG_3857 && toSystem == CoordinateSystem.GCJ02 -> {
                val wgs84 = epsg3857ToWgs84(longitude, latitude)
                wgs84ToGcj02(wgs84.first, wgs84.second)
            }
            
            // WGS84 → GCJ02
            fromSystem == CoordinateSystem.WGS84 && toSystem == CoordinateSystem.GCJ02 -> {
                wgs84ToGcj02(longitude, latitude)
            }
            
            // GCJ02 → WGS84 
            fromSystem == CoordinateSystem.GCJ02 && toSystem == CoordinateSystem.WGS84 -> {
                gcj02ToWgs84(longitude, latitude)
            }
            
            // GCJ02 → BD09 (百度坐标系)
            fromSystem == CoordinateSystem.GCJ02 && toSystem == CoordinateSystem.BD09 -> {
                gcj02ToBd09(longitude, latitude)
            }
            
            // BD09 → GCJ02
            fromSystem == CoordinateSystem.BD09 && toSystem == CoordinateSystem.GCJ02 -> {
                bd09ToGcj02(longitude, latitude)
            }
            
            else -> {
                // 暂不支持的转换，返回原坐标
                Pair(longitude, latitude)
            }
        }
    }
    
    /**
     * EPSG:3857 (Web Mercator) 转 WGS84
     */
    private fun epsg3857ToWgs84(x: Double, y: Double): Pair<Double, Double> {
        val lon = x / 20037508.34 * 180
        var lat = y / 20037508.34 * 180
        lat = 180 / PI * (2 * atan(exp(lat * PI / 180)) - PI / 2)
        return Pair(lon, lat)
    }
    
    /**
     * WGS84 转 EPSG:3857 (Web Mercator)
     */
    private fun wgs84ToEpsg3857(longitude: Double, latitude: Double): Pair<Double, Double> {
        val x = longitude * 20037508.34 / 180
        var y = ln(tan((90 + latitude) * PI / 360)) / (PI / 180)
        y = y * 20037508.34 / 180
        return Pair(x, y)
    }
    
    /**
     * WGS84 转 GCJ02 (火星坐标系)
     * 国家测绘局标准加密算法
     */
    private fun wgs84ToGcj02(longitude: Double, latitude: Double): Pair<Double, Double> {
        if (isOutOfChina(longitude, latitude)) {
            return Pair(longitude, latitude)
        }
        
        var dLat = transformLat(longitude - 105.0, latitude - 35.0)
        var dLon = transformLon(longitude - 105.0, latitude - 35.0)
        
        val radLat = latitude / 180.0 * PI
        var magic = sin(radLat)
        magic = 1 - EE * magic * magic
        val sqrtMagic = sqrt(magic)
        
        dLat = (dLat * 180.0) / ((EARTH_RADIUS * (1 - EE)) / (magic * sqrtMagic) * PI)
        dLon = (dLon * 180.0) / (EARTH_RADIUS / sqrtMagic * cos(radLat) * PI)
        
        val mgLat = latitude + dLat
        val mgLon = longitude + dLon
        
        return Pair(mgLon, mgLat)
    }
    
    /**
     * GCJ02 转 WGS84
     */
    private fun gcj02ToWgs84(longitude: Double, latitude: Double): Pair<Double, Double> {
        if (isOutOfChina(longitude, latitude)) {
            return Pair(longitude, latitude)
        }
        
        var dLat = transformLat(longitude - 105.0, latitude - 35.0)
        var dLon = transformLon(longitude - 105.0, latitude - 35.0)
        
        val radLat = latitude / 180.0 * PI
        var magic = sin(radLat)
        magic = 1 - EE * magic * magic
        val sqrtMagic = sqrt(magic)
        
        dLat = (dLat * 180.0) / ((EARTH_RADIUS * (1 - EE)) / (magic * sqrtMagic) * PI)
        dLon = (dLon * 180.0) / (EARTH_RADIUS / sqrtMagic * cos(radLat) * PI)
        
        val mgLat = latitude - dLat
        val mgLon = longitude - dLon
        
        return Pair(mgLon, mgLat)
    }
    
    /**
     * GCJ02 转 BD09 (百度坐标系)
     */
    private fun gcj02ToBd09(longitude: Double, latitude: Double): Pair<Double, Double> {
        val z = sqrt(longitude * longitude + latitude * latitude) + 0.00002 * sin(latitude * PI)
        val theta = atan2(latitude, longitude) + 0.000003 * cos(longitude * PI)
        
        val bdLon = z * cos(theta) + 0.0065
        val bdLat = z * sin(theta) + 0.006
        
        return Pair(bdLon, bdLat)
    }
    
    /**
     * BD09 转 GCJ02
     */
    private fun bd09ToGcj02(longitude: Double, latitude: Double): Pair<Double, Double> {
        val x = longitude - 0.0065
        val y = latitude - 0.006
        val z = sqrt(x * x + y * y) - 0.00002 * sin(y * PI)
        val theta = atan2(y, x) - 0.000003 * cos(x * PI)
        
        val gcjLon = z * cos(theta)
        val gcjLat = z * sin(theta)
        
        return Pair(gcjLon, gcjLat)
    }
    
    /**
     * 判断是否在中国境外
     */
    private fun isOutOfChina(longitude: Double, latitude: Double): Boolean {
        return longitude < 72.004 || longitude > 137.8347 || 
               latitude < 0.8293 || latitude > 55.8271
    }
    
    /**
     * 纬度转换
     */
    private fun transformLat(x: Double, y: Double): Double {
        var ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * sqrt(abs(x))
        ret += (20.0 * sin(6.0 * x * PI) + 20.0 * sin(2.0 * x * PI)) * 2.0 / 3.0
        ret += (20.0 * sin(y * PI) + 40.0 * sin(y / 3.0 * PI)) * 2.0 / 3.0
        ret += (160.0 * sin(y / 12.0 * PI) + 320 * sin(y * PI / 30.0)) * 2.0 / 3.0
        return ret
    }
    
    /**
     * 经度转换
     */
    private fun transformLon(x: Double, y: Double): Double {
        var ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * sqrt(abs(x))
        ret += (20.0 * sin(6.0 * x * PI) + 20.0 * sin(2.0 * x * PI)) * 2.0 / 3.0
        ret += (20.0 * sin(x * PI) + 40.0 * sin(x / 3.0 * PI)) * 2.0 / 3.0
        ret += (150.0 * sin(x / 12.0 * PI) + 300.0 * sin(x / 30.0 * PI)) * 2.0 / 3.0
        return ret
    }
} 