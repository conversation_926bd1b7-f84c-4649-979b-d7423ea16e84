package cn.agrolinking.wmst.map.manager

import android.location.Location
import android.util.Log
import org.maplibre.android.MapLibre
import org.maplibre.android.camera.CameraPosition
import org.maplibre.android.camera.CameraUpdateFactory
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.location.LocationComponent
import org.maplibre.android.location.LocationComponentActivationOptions
import org.maplibre.android.location.modes.CameraMode
import org.maplibre.android.location.modes.RenderMode
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.Style

/**
 * MapLibre 地图核心管理器
 * 负责地图的初始化、样式管理和生命周期管理
 */
class MapLibreMapManager private constructor() {
    
    companion object {
        private const val TAG = "MapLibreMapManager"
        
        @Volatile
        private var INSTANCE: MapLibreMapManager? = null
        
        fun getInstance(): MapLibreMapManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MapLibreMapManager().also { INSTANCE = it }
            }
        }
    }
    
    private var mapLibreMap: MapLibreMap? = null
    private var currentStyle: Style? = null
    private var locationComponent: LocationComponent? = null
    private val mapReadyCallbacks = mutableListOf<(MapLibreMap) -> Unit>()
    private val styleLoadedCallbacks = mutableListOf<(Style) -> Unit>()
    
    /**
     * 初始化地图
     */
    fun initializeMap(mapView: MapView, callback: (MapLibreMap) -> Unit) {
        Log.d(TAG, "开始初始化地图")
        
        mapView.getMapAsync { map ->
            try {
                mapLibreMap = map
                setupMapDefaults(map)
                
                Log.d(TAG, "地图初始化成功")
                
                // 执行所有等待的回调
                mapReadyCallbacks.forEach { it(map) }
                mapReadyCallbacks.clear()
                
                callback(map)
            } catch (e: Exception) {
                Log.e(TAG, "地图初始化失败", e)
            }
        }
    }
    
    /**
     * 获取当前地图实例
     */
    fun getMap(): MapLibreMap? = mapLibreMap
    
    /**
     * 获取当前样式
     */
    fun getCurrentStyle(): Style? = currentStyle
    
    /**
     * 地图准备就绪时的回调
     */
    fun onMapReady(callback: (MapLibreMap) -> Unit) {
        mapLibreMap?.let { 
            callback(it) 
        } ?: mapReadyCallbacks.add(callback)
    }
    
    /**
     * 样式加载完成时的回调
     */
    fun onStyleLoaded(callback: (Style) -> Unit) {
        currentStyle?.let { 
            callback(it) 
        } ?: styleLoadedCallbacks.add(callback)
    }
    
    /**
     * 设置地图样式
     */
    fun setMapStyle(styleJson: String, callback: ((Style) -> Unit)? = null) {
        Log.d(TAG, "设置地图样式: $styleJson")
        
        mapLibreMap?.setStyle(styleJson) { style ->
            try {
                currentStyle = style
                Log.d(TAG, "地图样式加载成功")
                
                // 执行所有等待的样式回调
                styleLoadedCallbacks.forEach { it(style) }
                styleLoadedCallbacks.clear()
                
                callback?.invoke(style)
            } catch (e: Exception) {
                Log.e(TAG, "地图样式加载失败", e)
            }
        }
    }
    
    /**
     * 设置地图默认配置
     */
    private fun setupMapDefaults(map: MapLibreMap) {
        try {
            map.uiSettings.apply {
                isZoomGesturesEnabled = true
                isScrollGesturesEnabled = true
                isRotateGesturesEnabled = true
                isTiltGesturesEnabled = true
                isCompassEnabled = true
                isAttributionEnabled = true
            }
            
            // 设置缩放范围
            map.setMinZoomPreference(1.0)
            map.setMaxZoomPreference(20.0)
            
            Log.d(TAG, "地图默认配置设置完成")
        } catch (e: Exception) {
            Log.e(TAG, "设置地图默认配置失败", e)
        }
    }
    
    /**
     * 检查地图是否已准备就绪
     */
    fun isMapReady(): Boolean = mapLibreMap != null
    
    /**
     * 检查样式是否已加载
     */
    fun isStyleLoaded(): Boolean = currentStyle != null
    
    /**
     * 清理资源
     */
    fun cleanup() {
        Log.d(TAG, "清理地图管理器资源")
        
        locationComponent = null
        mapLibreMap = null
        currentStyle = null
        mapReadyCallbacks.clear()
        styleLoadedCallbacks.clear()
    }
    
    /**
     * 启用位置组件
     */
    @SuppressWarnings("MissingPermission")
    fun enableLocationComponent(context: android.content.Context, style: Style) {
        try {
            mapLibreMap?.let { map ->
                locationComponent = map.locationComponent.apply {
                    activateLocationComponent(
                        LocationComponentActivationOptions.builder(context, style)
                            .useDefaultLocationEngine(false)
                            .build()
                    )
                    isLocationComponentEnabled = true
                    cameraMode = CameraMode.NONE
                    renderMode = RenderMode.COMPASS
                }
                Log.d(TAG, "位置组件启用成功")
            }
        } catch (e: Exception) {
            Log.e(TAG, "启用位置组件失败", e)
        }
    }

    /**
     * 更新用户位置
     */
    fun updateUserLocation(location: Location) {
        try {
            locationComponent?.forceLocationUpdate(location)
            Log.d(TAG, "用户位置更新: ${location.latitude}, ${location.longitude}")
        } catch (e: Exception) {
            Log.e(TAG, "更新用户位置失败", e)
        }
    }

    /**
     * 移动相机到指定位置
     */
    fun moveCamera(latLng: LatLng, zoom: Double = 15.0, animate: Boolean = true) {
        try {
            mapLibreMap?.let { map ->
                val cameraPosition = CameraPosition.Builder()
                    .target(latLng)
                    .zoom(zoom)
                    .build()

                if (animate) {
                    map.animateCamera(CameraUpdateFactory.newCameraPosition(cameraPosition))
                } else {
                    map.moveCamera(CameraUpdateFactory.newCameraPosition(cameraPosition))
                }
                
                Log.d(TAG, "相机移动到: ${latLng.latitude}, ${latLng.longitude}, 缩放: $zoom")
            }
        } catch (e: Exception) {
            Log.e(TAG, "移动相机失败", e)
        }
    }

    /**
     * 移动相机到用户位置
     */
    fun moveCameraToUserLocation(location: Location, zoom: Double = 15.0) {
        val latLng = LatLng(location.latitude, location.longitude)
        moveCamera(latLng, zoom, animate = true)
    }

    /**
     * 设置位置跟随模式
     */
    fun setLocationTrackingMode(enabled: Boolean) {
        try {
            locationComponent?.cameraMode = if (enabled) {
                CameraMode.TRACKING
            } else {
                CameraMode.NONE
            }
            Log.d(TAG, "位置跟随模式: $enabled")
        } catch (e: Exception) {
            Log.e(TAG, "设置位置跟随模式失败", e)
        }
    }

    /**
     * 获取地图状态信息
     */
    fun getMapInfo(): String {
        return buildString {
            append("MapLibreMapManager状态:\n")
            append("- 地图已准备: ${isMapReady()}\n")
            append("- 样式已加载: ${isStyleLoaded()}\n")
            append("- 位置组件已启用: ${locationComponent?.isLocationComponentEnabled == true}\n")
            append("- 等待地图回调数: ${mapReadyCallbacks.size}\n")
            append("- 等待样式回调数: ${styleLoadedCallbacks.size}")
        }
    }
} 