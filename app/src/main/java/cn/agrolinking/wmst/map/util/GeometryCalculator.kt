package cn.agrolinking.wmst.map.util

import org.maplibre.android.geometry.LatLng
import kotlin.math.*

class GeometryCalculator {
    
    companion object {
        private const val EARTH_RADIUS = 6378137.0 // 地球半径(米)
    }
    
    /**
     * 计算多边形面积 (平方米)
     * 使用球面三角形面积计算公式
     */
    fun calculatePolygonArea(vertices: List<LatLng>): Double {
        if (vertices.size < 3) return 0.0
        
        val coordinates = vertices.map { 
            doubleArrayOf(Math.toRadians(it.longitude), Math.toRadians(it.latitude))
        }
        
        var area = 0.0
        val n = coordinates.size
        
        for (i in 0 until n) {
            val j = (i + 1) % n
            area += coordinates[i][0] * coordinates[j][1]
            area -= coordinates[j][0] * coordinates[i][1]
        }
        
        area = abs(area) / 2.0
        return area * EARTH_RADIUS * EARTH_RADIUS
    }
    
    /**
     * 计算多边形周长 (米)
     */
    fun calculatePolygonPerimeter(vertices: List<LatLng>): Double {
        if (vertices.size < 2) return 0.0
        
        var perimeter = 0.0
        
        for (i in 0 until vertices.size) {
            val current = vertices[i]
            val next = vertices[(i + 1) % vertices.size]
            perimeter += calculateDistance(current, next)
        }
        
        return perimeter
    }
    
    /**
     * 计算两点间距离 (米)
     * 使用Haversine公式
     */
    fun calculateDistance(point1: LatLng, point2: LatLng): Double {
        val lat1Rad = Math.toRadians(point1.latitude)
        val lat2Rad = Math.toRadians(point2.latitude)
        val deltaLatRad = Math.toRadians(point2.latitude - point1.latitude)
        val deltaLngRad = Math.toRadians(point2.longitude - point1.longitude)
        
        val a = sin(deltaLatRad / 2).pow(2) + 
                cos(lat1Rad) * cos(lat2Rad) * sin(deltaLngRad / 2).pow(2)
        val c = 2 * atan2(sqrt(a), sqrt(1 - a))
        
        return EARTH_RADIUS * c
    }
    
    /**
     * 计算圆形面积 (平方米)
     */
    fun calculateCircleArea(radius: Double): Double {
        return PI * radius * radius
    }
    
    /**
     * 计算矩形面积 (平方米)
     */
    fun calculateRectangleArea(width: Double, height: Double): Double {
        return width * height
    }
    
    /**
     * 格式化面积显示
     */
    fun formatArea(areaInSquareMeters: Double): String {
        return when {
            areaInSquareMeters < 10000 -> {
                String.format("%.1f 平方米", areaInSquareMeters)
            }
            areaInSquareMeters < 1000000 -> {
                String.format("%.2f 亩", areaInSquareMeters / 666.67)
            }
            else -> {
                String.format("%.2f 平方公里", areaInSquareMeters / 1000000)
            }
        }
    }
    
    /**
     * 格式化距离显示
     */
    fun formatDistance(distanceInMeters: Double): String {
        return when {
            distanceInMeters < 1000 -> {
                String.format("%.1f 米", distanceInMeters)
            }
            else -> {
                String.format("%.2f 公里", distanceInMeters / 1000)
            }
        }
    }
} 