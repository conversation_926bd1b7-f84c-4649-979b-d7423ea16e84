<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.2.2" type="baseline" client="gradle" dependencies="false" name="AGP (8.2.2)" variant="all" version="8.2.2">

    <issue
        id="ObsoleteLintCustomCheck"
        message="Library lint checks reference invalid APIs; these checks **will be skipped**!&#xA;&#xA;Lint found an issue registry (`androidx.compose.runtime.lint.RuntimeIssueRegistry`)&#xA;which contains some references to invalid API:&#xA;org.jetbrains.kotlin.analysis.api.session.KtAnalysisSessionProvider: org.jetbrains.kotlin.analysis.api.lifetime.KtLifetimeTokenFactory getTokenFactory()&#xA;(Referenced from androidx/compose/runtime/lint/AutoboxingStateCreationDetector.class)&#xA;&#xA;Therefore, this lint check library is **not** included&#xA;in analysis. This affects the following lint checks:&#xA;`AutoboxingStateValueProperty`&#xA;`AutoboxingStateCreation`&#xA;`CoroutineCreationDuringComposition`&#xA;`FlowOperatorInvokedInComposition`&#xA;`ComposableLambdaParameterNaming`&#xA;`ComposableLambdaParameterPosition`&#xA;`ComposableNaming`&#xA;`StateFlowValueCalledInComposition`&#xA;`CompositionLocalNaming`&#xA;`MutableCollectionMutableState`&#xA;`ProduceStateDoesNotAssignValue`&#xA;`RememberReturnType`&#xA;`OpaqueUnitKey`&#xA;`UnrememberedMutableState`&#xA;&#xA;To use this lint check, upgrade to a more recent version&#xA;of the library.">
        <location
            file="$GRADLE_USER_HOME/caches/transforms-3/6040484a2b87d0f52f34856cdb84a292/transformed/runtime-release/jars/lint.jar"/>
    </issue>

    <issue
        id="OldTargetApi"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details."
        errorLine1="        targetSdk 34"
        errorLine2="        ~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.compose:compose-bom than 2024.01.00 is available: 2025.05.00"
        errorLine1="    def composeBom = platform(&apos;androidx.compose:compose-bom:2024.01.00&apos;)"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="57"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0"
        errorLine1="    implementation &apos;androidx.core:core-ktx:1.12.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="66"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.0"
        errorLine1="    implementation &apos;androidx.lifecycle:lifecycle-runtime-ktx:2.7.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="67"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1"
        errorLine1="    implementation &apos;androidx.activity:activity-compose:1.8.2&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="68"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        errorLine1="    androidTestImplementation &apos;androidx.test.ext:junit:1.1.5&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="70"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        errorLine1="    androidTestImplementation &apos;androidx.test.espresso:espresso-core:3.5.1&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="71"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.hilt:hilt-navigation-compose than 1.1.0 is available: 1.2.0"
        errorLine1="    implementation &apos;androidx.hilt:hilt-navigation-compose:1.1.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="77"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.1"
        errorLine1="    implementation &quot;androidx.room:room-runtime:$room_version&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="89"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.1"
        errorLine1="    implementation &quot;androidx.room:room-ktx:$room_version&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="91"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.navigation:navigation-compose than 2.7.6 is available: 2.9.0"
        errorLine1="    implementation &quot;androidx.navigation:navigation-compose:2.7.6&quot;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="107"
            column="20"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        message="The application adaptive icon is missing a monochrome tag"
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        message="The application adaptive roundIcon is missing a monochrome tag"
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"/>
    </issue>

</issues>
