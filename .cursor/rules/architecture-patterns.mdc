---
description: 
globs: 
alwaysApply: true
---
# 万亩数田(WMST) 架构模式和代码组织

## Clean Architecture 分层

### 1. UI层 (Presentation Layer)
负责用户界面和用户交互逻辑
- **MainActivity**: [MainActivity.kt](mdc:app/src/main/java/cn/agrolinking/wmst/ui/MainActivity.kt) - 应用入口Activity
- **导航控制**: [ComposeApp.kt](mdc:app/src/main/java/cn/agrolinking/wmst/ui/ComposeApp.kt) - 页面路由管理
- **用户列表**: [ui/users/](mdc:app/src/main/java/cn/agrolinking/wmst/ui/users) - 用户管理相关UI
- **详情页面**: [ui/details/](mdc:app/src/main/java/cn/agrolinking/wmst/ui/details) - 详情展示UI

### 2. 数据层 (Data Layer)
管理数据来源和数据访问
- **Repository模式**: [repository/](mdc:app/src/main/java/cn/agrolinking/wmst/repository) - 统一数据访问接口
- **本地数据库**: [database/](mdc:app/src/main/java/cn/agrolinking/wmst/database) - Room数据库实体和DAO
- **网络服务**: [network/](mdc:app/src/main/java/cn/agrolinking/wmst/network) - Retrofit API接口

### 3. 领域层 (Domain Layer)
包含业务逻辑和领域模型
- **领域模型**: [domain/](mdc:app/src/main/java/cn/agrolinking/wmst/domain) - 纯Kotlin业务实体

## 依赖注入 (Hilt)
- **DI配置**: [di/](mdc:app/src/main/java/cn/agrolinking/wmst/di) - Hilt模块配置
- **应用类**: [ComposeApplication.kt](mdc:app/src/main/java/cn/agrolinking/wmst/ComposeApplication.kt) - @HiltAndroidApp注解

## 数据流转模式
```
Network API → Repository → ViewModel → UI State → Compose UI
     ↓              ↓
  API Models → Database → Domain Models
```

## 关键设计原则
1. **单一职责**: 每个类只负责一个功能
2. **依赖倒置**: 高层模块不依赖低层模块
3. **接口隔离**: Repository接口抽象数据访问
4. **数据单向流动**: 使用Flow进行响应式编程

## 命名约定
- **实体类**: `UserEntity`, `DetailsEntity` (数据库实体)
- **领域模型**: `User`, `Details` (业务模型) 
- **API模型**: `UserApiModel` (网络模型)
- **Repository**: `UsersRepository`, `DetailsRepository`
- **ViewModel**: `UsersViewModel`, `DetailsViewModel`

## 包结构规范
```
cn.agrolinking.wmst/
├── ui/           # UI层
├── repository/   # 数据仓库层
├── database/     # 本地数据库
├── network/      # 网络层
├── domain/       # 领域模型
├── di/           # 依赖注入
└── util/         # 工具类
```
