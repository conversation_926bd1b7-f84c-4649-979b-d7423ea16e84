---
description: 
globs: 
alwaysApply: true
---
# 万亩数田(WMST) 项目结构指南

## 项目概览
这是一个基于现代Android技术栈的农业种植管理系统，采用Clean Architecture + MVVM架构模式。

## 核心配置文件
- [build.gradle](mdc:build.gradle) - 主构建配置，包含依赖和Android配置
- [gradle.properties](mdc:gradle.properties) - Gradle属性配置
- [settings.gradle](mdc:settings.gradle) - 项目设置
- [README.md](mdc:README.md) - 项目完整文档和技术规范

## 应用入口点
- [ComposeApplication.kt](mdc:app/src/main/java/cn/agrolinking/wmst/ComposeApplication.kt) - 应用程序类，Hilt DI和Timber日志配置
- [MainActivity.kt](mdc:app/src/main/java/cn/agrolinking/wmst/ui/MainActivity.kt) - 主Activity，应用UI入口
- [ComposeApp.kt](mdc:app/src/main/java/cn/agrolinking/wmst/ui/ComposeApp.kt) - 导航控制器，管理页面路由

## 架构分层

### UI层 (Presentation Layer)
- [ui/users/](mdc:app/src/main/java/cn/agrolinking/wmst/ui/users) - 用户列表模块UI
- [ui/details/](mdc:app/src/main/java/cn/agrolinking/wmst/ui/details) - 用户详情模块UI
- [ui/components/](mdc:app/src/main/java/cn/agrolinking/wmst/ui/components) - 通用UI组件
- [ui/theme/](mdc:app/src/main/java/cn/agrolinking/wmst/ui/theme) - Material Design主题配置

### 数据层 (Data Layer)
- [repository/](mdc:app/src/main/java/cn/agrolinking/wmst/repository) - 数据仓库层，统一数据访问
- [database/](mdc:app/src/main/java/cn/agrolinking/wmst/database) - Room数据库配置和实体
- [network/](mdc:app/src/main/java/cn/agrolinking/wmst/network) - Retrofit网络请求API

### 领域层 (Domain Layer)
- [domain/](mdc:app/src/main/java/cn/agrolinking/wmst/domain) - 业务领域模型，纯Kotlin类

### 依赖注入
- [di/](mdc:app/src/main/java/cn/agrolinking/wmst/di) - Hilt依赖注入模块配置

## 开发文档
- [docs/android-development-debug-guide.md](mdc:docs/android-development-debug-guide.md) - 完整的Android开发调试指南

## 数据流转
```
API Response → Network Models → Repository → Domain Models → ViewModel → UI State
```

## 包名规范
项目包名：`cn.agrolinking.wmst`
所有Kotlin文件都在此包名空间下组织
