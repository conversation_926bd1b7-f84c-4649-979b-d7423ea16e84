---
description: 
globs: 
alwaysApply: true
---
# 万亩数田(WMST) 开发工作流程

## 环境配置
项目需要Java 17+版本，使用jenv进行版本管理。查看 [.java-version](mdc:.java-version) 了解配置的Java版本。

## 构建命令
```bash
# 清理构建
./gradlew clean

# 构建Debug版本
./gradlew assembleDebug

# 安装到设备
./gradlew installDebug

# 运行测试
./gradlew test
```

## 日志调试
项目使用Timber日志库，配置在 [ComposeApplication.kt](mdc:app/src/main/java/cn/agrolinking/wmst/ComposeApplication.kt)。

### 查看应用日志
```bash
# 过滤应用日志
adb logcat | grep "cn.agrolinking.wmst"

# 查看特定仓库日志
adb logcat | grep "UsersRepository"
```

## 关键依赖
参考 [build.gradle](mdc:build.gradle) 中的依赖配置：
- Jetpack Compose - 声明式UI
- Hilt - 依赖注入
- Room - 本地数据库
- Retrofit - 网络请求
- Timber - 日志系统

## 代码风格
- 使用Kotlin代码风格指南
- 遵循Clean Architecture原则
- 数据单向流动 (Flow)
- Repository模式进行数据访问

## 文档参考
- [README.md](mdc:README.md) - 项目完整技术文档
- [docs/android-development-debug-guide.md](mdc:docs/android-development-debug-guide.md) - 详细调试指南

## Git工作流
项目使用GitLab进行版本控制，remote URL: `http://gitlab.agrolinking.cn/wmst/nlt-planting-cms-app.git`
