---
description: 
globs: 
alwaysApply: true
---
---
description: "万亩数田(WMST)的核心技术架构规则。AI在任何时候都必须遵守这些规则。"
globs:
  - "**"
alwaysApply: true
---
# 核心架构规则: 万亩数田(WMST)

## 1. UI框架: Jetpack Compose 优先

- **描述**: 所有新界面和UI组件**必须**使用 Jetpack Compose 实现。
- **禁止**: 创建新的 Activity, Fragment, 或 XML 布局文件。
- **互操作性**: 与传统 View 的互操作**必须**使用官方推荐的 `AndroidView` (在Compose中嵌入View) 或 `ComposeView` (在View中嵌入Compose)。

## 2. 应用架构: 单Activity模式 (Single-Activity Architecture)

- **描述**: 整个应用**只有一个**主 `MainActivity`。
- **导航**: 页面导航**必须**使用 `NavHost` (Navigation-Compose) 进行管理。

## 3. 设计系统: Material Design 3 (Compose版)

- **描述**: 所有UI组件的样式和主题**必须**遵循 Material 3 规范。
- **资源**: 颜色、排版和形状定义在 `app/src/main/java/cn/agrolinking/wmst/ui/theme/` 包下，**禁止**在XML中定义新的样式。

## 4. 状态管理: Unidirectional Data Flow (UDF)

- **描述**: UI状态**必须**是单一数据源，通常由 ViewModel 提供。
- **实现**: 数据向下流动 (ViewModel -> UI)，事件向上传递 (UI -> ViewModel)。使用 `StateFlow` 或 `State` 来持有和观察UI状态。

## 5. 依赖注入: Hilt

- **描述**: 所有依赖项的创建和提供**必须**使用 Hilt 进行管理。
- **禁止**: 手动创建 ViewModel, Repository, 或其他可注入的实例。

---
**自我提醒**: 在执行任何文件创建或修改任务前，我必须回顾并严格遵守此文件中的所有规则，以确保架构一致性。
