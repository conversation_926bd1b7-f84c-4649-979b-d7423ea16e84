---
description:
globs:
alwaysApply: false
---
# 万亩数田(WMST) 调试和故障排除

## 日志系统
项目使用Timber日志库，在 [ComposeApplication.kt](mdc:app/src/main/java/cn/agrolinking/wmst/ComposeApplication.kt) 中配置。

### 日志级别
- `Timber.d()` - 调试信息
- `Timber.i()` - 一般信息  
- `Timber.w()` - 警告信息
- `Timber.e()` - 错误信息

### 查看日志
```bash
# 应用日志
adb logcat | grep "cn.agrolinking.wmst"

# 网络请求日志
adb logcat | grep -E "(OkHttp|Retrofit)"

# 数据库日志  
adb logcat | grep "Room"
```

## 常见问题排除

### 1. Java版本不兼容
- **问题**: Android Gradle Plugin需要Java 11+
- **解决**: 检查 [.java-version](mdc:.java-version) 和 `$JAVA_HOME` 配置
- **命令**: `java -version` 确认版本

### 2. 构建失败
- **清理缓存**: `./gradlew clean`
- **重新构建**: `./gradlew build`
- **检查依赖**: 查看 [build.gradle](mdc:build.gradle)

### 3. 设备连接问题
```bash
# 检查设备
adb devices

# 重启ADB
adb kill-server && adb start-server
```

### 4. 应用崩溃调试
```bash
# 查看崩溃日志
adb logcat | grep -E "(FATAL|AndroidRuntime)"

# 查看应用特定错误
adb logcat | grep "cn.agrolinking.wmst"
```

## 性能调试

### 内存使用
```bash
# 应用内存信息
adb shell dumpsys meminfo cn.agrolinking.wmst

# GC日志
adb logcat | grep "GC"
```

### 网络调试
主要网络请求在 [repository/UsersRepository.kt](mdc:app/src/main/java/cn/agrolinking/wmst/repository/UsersRepository.kt) 中实现。

## 数据库调试
Room数据库配置在 [database/](mdc:app/src/main/java/cn/agrolinking/wmst/database/) 目录。

### 查看数据库操作
```bash
adb logcat | grep "SQLite"
```

## 调试工作流
1. **添加日志** - 在关键代码点添加Timber日志
2. **构建安装** - `./gradlew installDebug`  
3. **查看日志** - `adb logcat | grep "万亩数田"`
4. **分析问题** - 根据日志输出定位问题
5. **修复验证** - 修改代码后重新测试

## 详细调试指南
参考 [docs/android-development-debug-guide.md](mdc:docs/android-development-debug-guide.md) 获取完整的调试指南，包含615行详细的调试说明和实战案例。
