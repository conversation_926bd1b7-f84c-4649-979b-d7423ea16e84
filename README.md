# 🌾 万亩数田 (WMST)

这是一个基于现代Android技术栈开发的农业种植管理系统，隶属于agrolinking农业科技平台，采用Clean Architecture + MVVM架构模式，为农业生产提供数字化解决方案。

## 📱 产品功能

### 核心功能模块
- **用户管理**：完整的用户信息管理和展示系统
- **详情查看**：支持用户详细信息的查看和管理
- **离线缓存**：智能本地数据缓存，无网络环境下仍可使用
- **导航系统**：流畅的页面导航和跳转体验

### 特色功能
- 响应式UI设计，适配不同屏幕尺寸
- 实时数据同步，保证信息准确性
- 优雅的错误处理和用户反馈
- 现代化的Material Design 3设计语言

## 🏗️ 技术架构

### 整体架构设计
```
┌─────────────────┐
│   UI Layer      │  Jetpack Compose + ViewModel
├─────────────────┤
│ Repository Layer│  数据仓库抽象层
├─────────────────┤
│   Data Layer    │  Network + Database
└─────────────────┘
```

### 核心技术栈

| 技术领域 | 技术选型 | 版本 | 说明 |
|---------|----------|------|------|
| **UI框架** | Jetpack Compose | 2024.01.00 | 完全声明式UI开发 |
| **架构模式** | MVVM + Clean Architecture | - | 清晰的分层架构 |
| **依赖注入** | Dagger Hilt | 2.50 | 完整的DI解决方案 |
| **网络请求** | Retrofit + OkHttp | 2.9.0 / 4.12.0 | RESTful API调用 |
| **本地存储** | Room | 2.6.1 | SQLite ORM数据库 |
| **JSON解析** | Moshi | 1.14.0 | 高性能JSON处理 |
| **异步处理** | Kotlin Coroutines + Flow | - | 响应式编程 |
| **图片加载** | Coil | 2.5.0 | 异步图片加载 |
| **导航管理** | Navigation Compose | 2.7.6 | 声明式导航 |
| **日志系统** | Timber | 5.0.1 | 结构化日志 |
| **内存检测** | LeakCanary | 2.9.1 | 内存泄漏检测 |

## 🎯 代码架构详解

### 分层架构实现

#### 1. 数据层 (Data Layer)
```kotlin
// Room数据库配置
@Database(entities = [UserEntity::class, DetailsEntity::class], version = 1)
abstract class AppDatabase : RoomDatabase() {
    abstract val usersDao: UsersDao
}
```

#### 2. 网络层 (Network Layer)
```kotlin
// Retrofit API接口
interface UsersApi {
    @GET("/repos/square/retrofit/stargazers")
    suspend fun getUsers(): List<UserApiModel>
}
```

#### 3. 仓库层 (Repository Layer)
```kotlin
// 数据仓库实现
class UsersRepository @Inject constructor(
    private val usersApi: UsersApi,
    private val appDatabase: AppDatabase
) {
    val users: Flow<List<User>?> = 
        appDatabase.usersDao.getUsers().map { it?.asDomainModel() }
}
```

#### 4. UI层 (Presentation Layer)
```kotlin
// ViewModel实现
@HiltViewModel
class UsersViewModel @Inject constructor(
    private val usersRepository: UsersRepository
) : ViewModel()
```

### 数据流转图
```
API Response → UserApiModel → UserEntity → User (Domain) → UI State
```

## 🌐 API接口设计

### 接口配置
- **基础URL**: `https://api.github.com/`
- **认证方式**: 暂无（使用公共API）
- **数据格式**: JSON
- **请求方式**: RESTful

### 主要接口
| 接口 | 方法 | 说明 |
|------|------|------|
| `/repos/square/retrofit/stargazers` | GET | 获取用户列表 |
| `/users/{user}` | GET | 获取用户详情 |

### 网络配置特性
- ✅ HTTP请求日志记录（调试模式）
- ✅ 自动JSON序列化/反序列化
- ✅ 网络异常处理
- ✅ 请求响应拦截器

## 📦 项目结构

```
app/src/main/java/cn/agrolinking/wmst/
├── 📁 database/          # Room数据库
│   ├── UserEntity.kt     # 用户实体
│   ├── DetailsEntity.kt  # 详情实体
│   └── Room.kt          # 数据库配置
├── 📁 di/               # 依赖注入
│   ├── DatabaseModule.kt # 数据库模块
│   └── NetworkModule.kt  # 网络模块
├── 📁 domain/           # 领域模型
│   ├── User.kt          # 用户领域模型
│   └── Details.kt       # 详情领域模型
├── 📁 network/          # 网络层
│   ├── UsersApi.kt      # 用户API接口
│   ├── DetailsApi.kt    # 详情API接口
│   └── model/           # API数据模型
├── 📁 repository/       # 仓库层
│   ├── UsersRepository.kt    # 用户仓库
│   └── DetailsRepository.kt  # 详情仓库
├── 📁 ui/               # UI层
│   ├── MainActivity.kt  # 主Activity
│   ├── ComposeApp.kt    # 应用导航
│   ├── users/           # 用户模块UI
│   ├── details/         # 详情模块UI
│   ├── components/      # 通用组件
│   └── theme/           # 主题配置
├── 📁 util/             # 工具类
└── ComposeApplication.kt # 应用入口
```

## ⚙️ 构建配置

### 应用配置
```gradle
android {
    compileSdk 34
    defaultConfig {
        applicationId "cn.agrolinking.wmst"
        minSdk 23
        targetSdk 34
        versionCode 1
        versionName "1.0"
    }
}
```

### 构建要求
- **编译SDK**: Android 14 (API 34)
- **最低支持**: Android 6.0 (API 23)
- **Java版本**: JDK 17
- **Kotlin版本**: 1.9.22
- **Compose编译器**: 1.5.8

## 👨‍💻 开发规范

### 代码组织规范
✅ **优秀实践**
- 清晰的包结构分层
- 统一的命名约定
- 完整的依赖注入配置
- 响应式编程（Flow）
- 完善的错误处理机制

### 架构规范
- 严格遵循单一职责原则  
- 数据单向流动
- 依赖倒置（Repository模式）
- UI状态管理规范
- Clean Architecture分层

### 编码规范
- Kotlin代码风格指南
- Compose最佳实践
- 协程处理异步操作
- Room数据库ORM规范

## 🚀 快速开始

### 环境要求
- **Android Studio**: Iguana (2023.2.1) 或更高版本
- **JDK**: 17 或更高版本
- **Kotlin**: 1.9.22 或更高版本
- **Android Gradle Plugin**: 8.2.2 或更高版本

### 🍎 Apple Silicon Mac 特别支持

**问题**：Apple Silicon Mac (M1/M2/M3/M4) 上Android模拟器可能出现GUI崩溃问题。

**解决方案**：项目提供了专门的无头模式启动脚本，完美解决崩溃问题：

```bash
# 使用项目提供的启动脚本
./scripts/start-emulator-apple-silicon.sh

# 或指定特定的AVD
./scripts/start-emulator-apple-silicon.sh Pixel_6_API_34
```

**优势**：
- ✅ 避免GUI崩溃问题
- ✅ 更稳定的运行环境  
- ✅ 完整的ADB调试功能
- ✅ 支持所有开发和测试功能

详细说明请参考：[Apple Silicon Mac 调试指南](docs/android-development-debug-guide.md#apple-silicon-mac-特殊配置)

### 构建步骤
```bash
# 1. 克隆项目
git clone [项目地址]

# 2. 进入项目目录
cd nlt-planting-cms-app

# 3. 同步依赖
./gradlew build

# 4. 运行应用
./gradlew installDebug
```

### 开发调试
```bash
# 查看日志
adb logcat | grep WMST

# 清理构建
./gradlew clean

# 运行测试
./gradlew test
```

## 📊 项目亮点

### 🌟 技术亮点
1. **现代化技术栈**: 全面采用Jetpack Compose + 最新库版本
2. **清洁架构**: 严格的分层设计，易于测试和维护
3. **响应式设计**: 使用Flow进行数据流管理
4. **离线支持**: Room数据库提供本地缓存
5. **依赖注入**: Hilt提供完整的DI方案

### 📈 性能优化
- Compose重组优化
- 内存泄漏检测
- 网络请求缓存
- 图片异步加载
- 数据库查询优化

## 🔧 改进建议

### 待完善功能
- [ ] 单元测试覆盖
- [ ] UI自动化测试
- [ ] 更完善的错误状态UI
- [ ] 分页加载机制
- [ ] 生产环境代码混淆
- [ ] CI/CD自动化流程

### 扩展方向
- [ ] 农作物管理模块
- [ ] 天气预报集成
- [ ] 病虫害识别功能
- [ ] 农技知识库
- [ ] 社区交流平台

## 👥 开发团队

**农联网技术团队 (AgroLinking Tech Team)**
- 架构设计：Clean Architecture + MVVM
- 技术选型：现代Android技术栈
- 开发理念：代码是写给人看的，只是机器恰好可以运行

## 📄 许可证

本项目采用 [Apache License 2.0](LICENSE) 开源协议

---

> 💡 **注意**: 此项目当前处于开发阶段，部分功能正在完善中。如有问题请提交Issue或联系开发团队。

**最后更新**: 2025/06/15