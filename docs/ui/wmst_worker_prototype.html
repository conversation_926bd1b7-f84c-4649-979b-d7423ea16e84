<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作业人员 - 万亩数田 App 重新设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.5;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #FF9800, #F57700);
            color: white;
            padding: 20px;
            border-radius: 12px;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .phone-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
        }

        .phone {
            background: #000;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 320px;
            height: 680px;
            margin: 0 auto;
        }

        .screen {
            background: #fff;
            border-radius: 18px;
            height: 100%;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            background: #FF9800;
            height: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            color: white;
            font-size: 12px;
            font-weight: 500;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .online-dot {
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        .page {
            height: calc(100% - 30px);
            display: flex;
            flex-direction: column;
        }

        .top-bar {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #E65100;
        }

        .role-tag {
            font-size: 10px;
            padding: 4px 8px;
            border-radius: 10px;
            font-weight: 500;
            margin-left: 8px;
            background: #FFF3E0;
            color: #E65100;
        }

        .avatar {
            width: 36px;
            height: 36px;
            background: linear-gradient(45deg, #FF9800, #FFB74D);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
        }

        .content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }

        .summary-card {
            background: linear-gradient(135deg, #FF9800, #F57700);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            position: relative;
            overflow: hidden;
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        }

        .summary-content {
            position: relative;
            z-index: 1;
        }

        .summary-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-top: 12px;
        }

        .summary-stat {
            text-align: center;
        }

        .stat-value {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .action-btn {
            background: white;
            border: 2px solid #FF9800;
            border-radius: 12px;
            padding: 16px 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(255,152,0,0.1);
        }

        .action-btn:hover {
            background: #FF9800;
            color: white;
            transform: translateY(-2px);
        }

        .action-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: #FF9800;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .action-label {
            font-size: 11px;
            color: #FF9800;
            text-align: center;
            font-weight: 600;
            line-height: 1.2;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card h3 {
            font-size: 16px;
            color: #E65100;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .notice-banner {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .notice-icon {
            font-size: 24px;
        }

        .notice-content {
            flex: 1;
        }

        .notice-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .notice-text {
            font-size: 12px;
            opacity: 0.9;
            line-height: 1.4;
        }

        .task-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .task-item {
            padding: 14px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-item:hover {
            background: #fafafa;
        }

        .task-icon {
            width: 40px;
            height: 40px;
            background: #FFF3E0;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .task-info {
            flex: 1;
        }

        .task-title {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
        }

        .task-meta {
            font-size: 12px;
            color: #888;
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .task-status {
            padding: 6px 10px;
            border-radius: 16px;
            font-size: 11px;
            font-weight: 600;
            white-space: nowrap;
        }

        .status-pending {
            background: #FFF3E0;
            color: #F57700;
        }

        /* Tab样式 */
        .tab-btn {
            flex: 1;
            padding: 12px 8px;
            background: transparent;
            border: none;
            font-size: 12px;
            color: #666;
            cursor: pointer;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all 0.2s;
        }

        .tab-btn.active {
            color: #FF9800;
            font-weight: 600;
        }

        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: #FF9800;
        }

        .tab-badge {
            background: #FF9800;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }

        .tab-btn.active .tab-badge {
            background: #F57700;
        }

        .tab-content {
            padding: 0;
        }

        .tab-content .task-item:first-child {
            border-top: none;
        }

        .status-urgent {
            background: #FFEBEE;
            color: #C62828;
        }

        .status-completed {
            background: #E8F5E8;
            color: #2E7D32;
        }

        .bottom-nav {
            height: 60px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            color: #999;
            font-size: 10px;
            cursor: pointer;
            transition: color 0.2s;
        }

        .nav-item.active {
            color: #FF9800;
        }

        .nav-icon {
            font-size: 20px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #E65100;
        }

        .section-action {
            font-size: 12px;
            color: #FF9800;
            text-decoration: none;
            padding: 6px 12px;
            border: 1px solid #FF9800;
            border-radius: 16px;
            transition: all 0.2s;
        }

        .section-action:hover {
            background: #FF9800;
            color: white;
        }

        .phone-label {
            text-align: center;
            margin-bottom: 12px;
            font-weight: 600;
            color: #E65100;
            font-size: 16px;
        }

        /* 工单详情页面样式 */
        .task-detail-header {
            background: linear-gradient(135deg, #FF9800, #F57700);
            color: white;
            padding: 20px;
            margin: -16px -16px 16px -16px;
        }

        .task-priority {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            margin-bottom: 8px;
            background: rgba(244, 67, 54, 0.2);
            color: #C62828;
        }

        .task-detail-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .task-detail-meta {
            font-size: 12px;
            opacity: 0.9;
        }

        .completion-form {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #FF9800;
        }

        .form-textarea {
            resize: none;
            height: 80px;
        }

        .complete-btn {
            width: 100%;
            padding: 16px;
            background: #FF9800;
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .complete-btn:hover {
            background: #F57700;
            transform: translateY(-2px);
        }

        /* 地图页面样式 */
        .map-container {
            background: linear-gradient(135deg, #81C784, #4CAF50);
            height: 200px;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            margin-bottom: 16px;
            position: relative;
            overflow: hidden;
        }

        .map-overlay {
            position: absolute;
            top: 12px;
            left: 12px;
            right: 12px;
            display: flex;
            gap: 8px;
        }

        .map-search {
            flex: 1;
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 20px;
            padding: 8px 12px;
            font-size: 12px;
        }

        .map-btn {
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 20px;
            padding: 8px 12px;
            font-size: 11px;
            cursor: pointer;
            color: #4CAF50;
            font-weight: 500;
            transition: all 0.2s;
        }

        .map-btn:hover {
            background: white;
        }

        .map-content {
            text-align: center;
            margin-top: 40px;
        }

        .map-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .map-subtitle {
            font-size: 11px;
            opacity: 0.8;
        }

        .map-tools {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .tool-btn {
            flex: 1;
            padding: 12px;
            background: white;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            color: #4CAF50;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .tool-btn.active {
            background: #4CAF50;
            color: white;
        }

        .tool-btn:hover {
            background: #4CAF50;
            color: white;
        }

        /* 记录页面样式 */
        .record-types {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .record-type-btn {
            background: white;
            border: 2px solid #FF9800;
            border-radius: 12px;
            padding: 20px 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .record-type-btn:hover {
            background: #FF9800;
            color: white;
        }

        .record-type-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .record-type-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .record-type-desc {
            font-size: 11px;
            color: #666;
            text-align: center;
            line-height: 1.3;
        }

        .recent-records {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .record-item {
            padding: 14px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            gap: 12px;
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-icon {
            width: 36px;
            height: 36px;
            background: #FFF3E0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .record-info {
            flex: 1;
        }

        .record-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
        }

        .record-meta {
            font-size: 11px;
            color: #888;
            margin-bottom: 6px;
        }

        .record-content {
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 6px;
            line-height: 1.4;
        }

        .record-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
            white-space: nowrap;
        }

        .status-synced {
            background: #E8F5E8;
            color: #2E7D32;
        }

        .status-pending {
            background: #FFF3E0;
            color: #F57700;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👷 作业人员 - 万亩数田</h1>
            <p>基于真实业务流程重新设计 · 简单实用 · 符合农业作业场景</p>
        </div>

        <div class="phone-grid">
            <!-- 首页 - 工作台概念 -->
            <div>
                <div class="phone-label">🏠 首页 - 我的工作台</div>
                <div class="phone">
                    <div class="screen">
                        <div class="status-bar">
                            <div class="status-left">
                                <div class="online-dot"></div>
                                <span>GPS定位中</span>
                            </div>
                            <div>09:24</div>
                        </div>
                        
                        <div class="page">
                            <div class="top-bar">
                                <div>
                                    <span class="page-title">我的工作台</span>
                                    <span class="role-tag">作业人员</span>
                                </div>
                                <div class="avatar">李</div>
                            </div>
                            
                            <div class="content">
                                <!-- 工作概况 -->
                                <div class="summary-card">
                                    <div class="summary-content">
                                        <div class="summary-title">今日工作概况</div>
                                        <div style="font-size: 12px; opacity: 0.9; margin-bottom: 8px;">
                                            📍 当前位置：华北基地 A-01地块
                                        </div>
                                        <div class="summary-stats">
                                            <div class="summary-stat">
                                                <div class="stat-value">3</div>
                                                <div class="stat-label">待办工单</div>
                                            </div>
                                            <div class="summary-stat">
                                                <div class="stat-value">2</div>
                                                <div class="stat-label">已完成</div>
                                            </div>
                                            <div class="summary-stat">
                                                <div class="stat-value">85亩</div>
                                                <div class="stat-label">今日作业</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 重要通知 -->
                                <div class="notice-banner">
                                    <div class="notice-icon">📢</div>
                                    <div class="notice-content">
                                        <div class="notice-title">今日天气提醒</div>
                                        <div class="notice-text">明日气温骤降至15°C，注意大棚保温作业</div>
                                    </div>
                                </div>

                                <!-- 快速操作入口 -->
                                <div class="section-header">
                                    <div class="section-title">快速操作</div>
                                </div>
                                <div class="quick-actions">
                                    <button class="action-btn">
                                        <div class="action-icon">📝</div>
                                        <div class="action-label">快速<br>记录</div>
                                    </button>
                                    <button class="action-btn">
                                        <div class="action-icon">🗺️</div>
                                        <div class="action-label">农田<br>圈划</div>
                                    </button>
                                    <button class="action-btn">
                                        <div class="action-icon">📍</div>
                                        <div class="action-label">位置<br>导航</div>
                                    </button>
                                    <button class="action-btn">
                                        <div class="action-icon">📞</div>
                                        <div class="action-label">紧急<br>联系</div>
                                    </button>
                                </div>

                                <!-- 近期活动 -->
                                <div class="section-header">
                                    <div class="section-title">近期活动</div>
                                    <a href="#" class="section-action">查看更多</a>
                                </div>
                                
                                <div class="task-list">
                                    <div class="task-item">
                                        <div class="task-icon">✅</div>
                                        <div class="task-info">
                                            <div class="task-title">A区施肥工单已完成</div>
                                            <div class="task-meta">
                                                <span>📍 A-01地块</span>
                                                <span>⏰ 2小时前</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="task-item">
                                        <div class="task-icon">📝</div>
                                        <div class="task-info">
                                            <div class="task-title">发现B区虫害情况</div>
                                            <div class="task-meta">
                                                <span>📍 B-03地块</span>
                                                <span>⏰ 1小时前</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bottom-nav">
                                <div class="nav-item active">
                                    <div class="nav-icon">📋</div>
                                    <div>工单</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">🗺️</div>
                                    <div>地图</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📝</div>
                                    <div>记录</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👤</div>
                                    <div>我的</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 工单详情页面 - 完成工单 -->
            <div>
                <div class="phone-label">📋 工单详情 - 完成确认</div>
                <div class="phone">
                    <div class="screen">
                        <div class="status-bar">
                            <div class="status-left">
                                <div class="online-dot"></div>
                                <span>GPS已定位</span>
                            </div>
                            <div>11:30</div>
                        </div>
                        
                        <div class="page">
                            <div class="top-bar">
                                <div class="page-title">工单详情</div>
                                <div class="avatar">李</div>
                            </div>
                            
                            <div class="content">
                                <!-- 工单信息 -->
                                <div class="task-detail-header">
                                    <div class="task-priority">高优先级</div>
                                    <div class="task-detail-title">A区玉米施肥作业</div>
                                    <div class="task-detail-meta">📍 A-01地块 · 👤 王管理员分配 · ⏰ 09:00-12:00</div>
                                </div>

                                <!-- 作业要求 -->
                                <div class="card">
                                    <h3>📋 作业要求</h3>
                                    <div style="font-size: 14px; color: #666; line-height: 1.6;">
                                        <p><strong>施肥类型：</strong>有机复合肥</p>
                                        <p><strong>用量标准：</strong>每亩50公斤</p>
                                        <p><strong>作业面积：</strong>预计85亩</p>
                                        <p><strong>注意事项：</strong>避开灌溉设备，注意用量均匀</p>
                                    </div>
                                </div>

                                <!-- 完成填报表单 -->
                                <div class="completion-form">
                                    <h3 style="color: #E65100; margin-bottom: 16px;">📝 完成填报</h3>
                                    
                                    <div class="form-group">
                                        <label class="form-label">实际完成面积（亩）</label>
                                        <input type="number" class="form-input" placeholder="请输入实际完成面积" value="85">
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">实际用肥量（公斤）</label>
                                        <input type="number" class="form-input" placeholder="请输入实际用肥量" value="4250">
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">作业质量</label>
                                        <select class="form-select">
                                            <option value="excellent">优秀</option>
                                            <option value="good" selected>良好</option>
                                            <option value="normal">一般</option>
                                            <option value="poor">较差</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">备注说明（可选）</label>
                                        <textarea class="form-textarea" placeholder="记录作业过程中的特殊情况...">作业过程顺利，玉米长势良好，未发现病虫害</textarea>
                                    </div>

                                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; margin-bottom: 16px;">
                                        <button style="padding: 8px; background: #E3F2FD; border: 1px solid #2196F3; border-radius: 6px; color: #1565C0; font-size: 11px;">📷 拍照</button>
                                        <button style="padding: 8px; background: #F3E5F5; border: 1px solid #9C27B0; border-radius: 6px; color: #7B1FA2; font-size: 11px;">🎤 语音</button>
                                        <button style="padding: 8px; background: #E8F5E8; border: 1px solid #4CAF50; border-radius: 6px; color: #2E7D32; font-size: 11px;">📍 定位</button>
                                    </div>

                                    <button class="complete-btn">确认完成工单</button>
                                </div>
                            </div>

                            <div class="bottom-nav">
                                <div class="nav-item">
                                    <div class="nav-icon">🏠</div>
                                    <div>首页</div>
                                </div>
                                <div class="nav-item active">
                                    <div class="nav-icon">📋</div>
                                    <div>工单</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">🗺️</div>
                                    <div>地图</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📝</div>
                                    <div>记录</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👤</div>
                                    <div>我的</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 地图页面 - 农田圈划 -->
            <div>
                <div class="phone-label">🗺️ 地图 - 农田圈划功能</div>
                <div class="phone">
                    <div class="screen">
                        <div class="status-bar">
                            <div class="status-left">
                                <div class="online-dot"></div>
                                <span>GPS高精度</span>
                            </div>
                            <div>09:24</div>
                        </div>
                        
                        <div class="page">
                            <div class="top-bar">
                                <div class="page-title">地图</div>
                                <div class="avatar">李</div>
                            </div>
                            
                            <div class="content">
                                <!-- 地图容器 -->
                                <div class="map-container">
                                    <div class="map-overlay">
                                        <input type="text" class="map-search" placeholder="搜索地块或位置...">
                                        <button class="map-btn" onclick="startDrawing()">开始圈划</button>
                                    </div>
                                    
                                    <div class="map-content">
                                        <div class="map-title">📍 华北基地地图</div>
                                        <div class="map-subtitle">当前位置：A-01地块中心</div>
                                    </div>
                                </div>

                                <!-- 地图工具 -->
                                <div class="map-tools">
                                    <button class="tool-btn active">🗺️ 查看</button>
                                    <button class="tool-btn">✏️ 圈划</button>
                                    <button class="tool-btn">📍 导航</button>
                                </div>

                                <!-- 圈划历史 -->
                                <div class="section-header">
                                    <div class="section-title">我的圈划记录</div>
                                    <a href="#" class="section-action">查看全部</a>
                                </div>
                                
                                <div class="task-list">
                                    <div class="task-item">
                                        <div class="task-icon">🗺️</div>
                                        <div class="task-info">
                                            <div class="task-title">A-01东侧补种区域</div>
                                            <div class="task-meta">
                                                <span>📏 约15亩</span>
                                                <span>📍 A-01地块</span>
                                                <span>⏰ 今日10:30</span>
                                            </div>
                                        </div>
                                        <div class="task-status status-completed">已确认</div>
                                    </div>
                                    <div class="task-item">
                                        <div class="task-icon">⚠️</div>
                                        <div class="task-info">
                                            <div class="task-title">B-03灌溉死角区域</div>
                                            <div class="task-meta">
                                                <span>📏 约8亩</span>
                                                <span>📍 B-03地块</span>
                                                <span>⏰ 今日11:15</span>
                                            </div>
                                        </div>
                                        <div class="task-status status-pending">待审核</div>
                                    </div>
                                </div>

                                <!-- 圈划指南 -->
                                <div class="card">
                                    <h3>💡 圈划操作指南</h3>
                                    <div style="font-size: 12px; color: #666; line-height: 1.6;">
                                        <p><strong>1. 确保定位：</strong>GPS信号良好（精度±3米内）</p>
                                        <p><strong>2. 开始圈划：</strong>点击"开始圈划"进入圈划模式</p>
                                        <p><strong>3. 绘制边界：</strong>沿地块边界拖拽手指绘制</p>
                                        <p><strong>4. 确认提交：</strong>系统自动计算面积后确认</p>
                                        <p style="color: #FF9800; margin-top: 8px;"><strong>💡 提示：</strong>支持离线圈划，联网后自动同步</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bottom-nav">
                                <div class="nav-item">
                                    <div class="nav-icon">🏠</div>
                                    <div>首页</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📋</div>
                                    <div>工单</div>
                                </div>
                                <div class="nav-item active">
                                    <div class="nav-icon">🗺️</div>
                                    <div>地图</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📝</div>
                                    <div>记录</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👤</div>
                                    <div>我的</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 记录页面 - 主动上报 -->
            <div>
                <div class="phone-label">📝 记录 - 主动上报</div>
                <div class="phone">
                    <div class="screen">
                        <div class="status-bar">
                            <div class="status-left">
                                <div class="online-dot"></div>
                                <span>记录已同步</span>
                            </div>
                            <div>09:24</div>
                        </div>
                        
                        <div class="page">
                            <div class="top-bar">
                                <div class="page-title">我的记录</div>
                                <div class="avatar">李</div>
                            </div>
                            
                            <div class="content">
                                <!-- 记录类型选择 -->
                                <div class="section-header">
                                    <div class="section-title">创建记录</div>
                                </div>
                                <div class="record-types">
                                    <button class="record-type-btn">
                                        <div class="record-type-icon">👁️</div>
                                        <div class="record-type-title">巡田记录</div>
                                        <div class="record-type-desc">日常巡田发现的情况</div>
                                    </button>
                                    <button class="record-type-btn">
                                        <div class="record-type-icon">🔧</div>
                                        <div class="record-type-title">作业记录</div>
                                        <div class="record-type-desc">自主作业完成情况</div>
                                    </button>
                                    <button class="record-type-btn">
                                        <div class="record-type-icon">⚠️</div>
                                        <div class="record-type-title">问题记录</div>
                                        <div class="record-type-desc">发现问题及时上报</div>
                                    </button>
                                    <button class="record-type-btn">
                                        <div class="record-type-icon">💡</div>
                                        <div class="record-type-title">建议记录</div>
                                        <div class="record-type-desc">改进建议和想法</div>
                                    </button>
                                </div>

                                <!-- 最近记录 -->
                                <div class="section-header">
                                    <div class="section-title">最近记录</div>
                                    <a href="#" class="section-action">查看全部</a>
                                </div>
                                
                                <div class="recent-records">
                                    <div class="record-item">
                                        <div class="record-icon">⚠️</div>
                                        <div class="record-info">
                                            <div class="record-title">B-03地块发现虫害</div>
                                            <div class="record-meta">📍 B-03地块 · ⏰ 2小时前 · 🎤 语音记录</div>
                                            <div class="record-content">
                                                发现玉米叶片有虫眼，疑似玉米螟，影响面积约20平米，建议尽快处理。
                                            </div>
                                        </div>
                                        <div class="record-status status-pending">待处理</div>
                                    </div>
                                    <div class="record-item">
                                        <div class="record-icon">👁️</div>
                                        <div class="record-info">
                                            <div class="record-title">A区灌溉效果巡查</div>
                                            <div class="record-meta">📍 A-01地块 · ⏰ 4小时前 · 📷 照片记录</div>
                                            <div class="record-content">
                                                巡查A区灌溉情况，水量充足，覆盖均匀，玉米长势良好。
                                            </div>
                                        </div>
                                        <div class="record-status status-synced">已同步</div>
                                    </div>
                                    <div class="record-item">
                                        <div class="record-icon">🔧</div>
                                        <div class="record-info">
                                            <div class="record-title">C区除草作业完成</div>
                                            <div class="record-meta">📍 C-02地块 · ⏰ 昨日16:30 · 📍 位置记录</div>
                                            <div class="record-content">
                                                完成C区20亩除草作业，清除杂草彻底，预计一周内不会重新生长。
                                            </div>
                                        </div>
                                        <div class="record-status status-synced">已同步</div>
                                    </div>
                                </div>
                            </div>

                            <div class="bottom-nav">
                                <div class="nav-item">
                                    <div class="nav-icon">🏠</div>
                                    <div>首页</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📋</div>
                                    <div>工单</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">🗺️</div>
                                    <div>地图</div>
                                </div>
                                <div class="nav-item active">
                                    <div class="nav-icon">📝</div>
                                    <div>记录</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👤</div>
                                    <div>我的</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的页面 - 个人中心 -->
            <div>
                <div class="phone-label">👤 我的 - 个人中心</div>
                <div class="phone">
                    <div class="screen">
                        <div class="status-bar">
                            <div class="status-left">
                                <div class="online-dot"></div>
                                <span>数据已同步</span>
                            </div>
                            <div>09:24</div>
                        </div>
                        
                        <div class="page">
                            <div class="top-bar">
                                <div class="page-title">个人中心</div>
                                <div class="avatar">李</div>
                            </div>
                            
                            <div class="content">
                                <!-- 个人信息卡片 -->
                                <div class="summary-card">
                                    <div class="summary-content">
                                        <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 12px;">
                                            <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #FF9800, #FFB74D); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold;">李</div>
                                            <div>
                                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 4px;">李明</div>
                                                <div style="font-size: 12px; opacity: 0.9;">作业人员 · 华北基地</div>
                                                <div style="font-size: 12px; opacity: 0.8;">工号：WK001 · 入职：2年</div>
                                            </div>
                                        </div>
                                        <div class="summary-stats">
                                            <div class="summary-stat">
                                                <div class="stat-value">156</div>
                                                <div class="stat-label">本月工单</div>
                                            </div>
                                            <div class="summary-stat">
                                                <div class="stat-value">98.5%</div>
                                                <div class="stat-label">完成率</div>
                                            </div>
                                            <div class="summary-stat">
                                                <div class="stat-value">4.8</div>
                                                <div class="stat-label">评分</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 数据统计 -->
                                <div class="card">
                                    <h3>📊 我的数据</h3>
                                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                            <div style="font-size: 20px; font-weight: bold; color: #FF9800; margin-bottom: 4px;">2,340</div>
                                            <div style="font-size: 12px; color: #666;">累计作业面积(亩)</div>
                                        </div>
                                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                            <div style="font-size: 20px; font-weight: bold; color: #4CAF50; margin-bottom: 4px;">67</div>
                                            <div style="font-size: 12px; color: #666;">圈划区域(个)</div>
                                        </div>
                                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                            <div style="font-size: 20px; font-weight: bold; color: #2196F3; margin-bottom: 4px;">245</div>
                                            <div style="font-size: 12px; color: #666;">记录条数</div>
                                        </div>
                                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                            <div style="font-size: 20px; font-weight: bold; color: #9C27B0; margin-bottom: 4px;">89</div>
                                            <div style="font-size: 12px; color: #666;">问题发现(个)</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 功能菜单 -->
                                <div class="card">
                                    <h3>⚙️ 功能设置</h3>
                                    <div style="space-y: 12px;">
                                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <div style="width: 32px; height: 32px; background: #E3F2FD; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 16px;">📱</div>
                                                <div>
                                                    <div style="font-size: 14px; font-weight: 500;">消息通知</div>
                                                    <div style="font-size: 11px; color: #888;">工单提醒、系统通知</div>
                                                </div>
                                            </div>
                                            <div style="color: #999; font-size: 18px;">›</div>
                                        </div>
                                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <div style="width: 32px; height: 32px; background: #E8F5E8; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 16px;">📡</div>
                                                <div>
                                                    <div style="font-size: 14px; font-weight: 500;">离线设置</div>
                                                    <div style="font-size: 11px; color: #888;">离线数据、同步设置</div>
                                                </div>
                                            </div>
                                            <div style="color: #999; font-size: 18px;">›</div>
                                        </div>
                                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <div style="width: 32px; height: 32px; background: #FFF3E0; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 16px;">🔧</div>
                                                <div>
                                                    <div style="font-size: 14px; font-weight: 500;">应用设置</div>
                                                    <div style="font-size: 11px; color: #888;">主题、语言、权限</div>
                                                </div>
                                            </div>
                                            <div style="color: #999; font-size: 18px;">›</div>
                                        </div>
                                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0;">
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <div style="width: 32px; height: 32px; background: #F3E5F5; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 16px;">❓</div>
                                                <div>
                                                    <div style="font-size: 14px; font-weight: 500;">帮助反馈</div>
                                                    <div style="font-size: 11px; color: #888;">使用帮助、意见反馈</div>
                                                </div>
                                            </div>
                                            <div style="color: #999; font-size: 18px;">›</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 同步状态 -->
                                <div class="card">
                                    <h3>☁️ 数据同步</h3>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <span style="font-size: 14px;">上次同步时间</span>
                                        <span style="font-size: 12px; color: #4CAF50;">刚刚</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <span style="font-size: 14px;">待同步数据</span>
                                        <span style="font-size: 12px; color: #666;">0条</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                        <span style="font-size: 14px;">本地缓存</span>
                                        <span style="font-size: 12px; color: #666;">156MB</span>
                                    </div>
                                    <div style="display: flex; gap: 8px;">
                                        <button style="flex: 1; padding: 8px; background: #4CAF50; color: white; border: none; border-radius: 6px; font-size: 12px;">立即同步</button>
                                        <button style="flex: 1; padding: 8px; background: transparent; color: #666; border: 1px solid #ddd; border-radius: 6px; font-size: 12px;">清理缓存</button>
                                    </div>
                                </div>

                                <!-- 退出登录 -->
                                <div style="margin-top: 20px;">
                                    <button style="width: 100%; padding: 14px; background: #FFEBEE; color: #C62828; border: 1px solid #FFCDD2; border-radius: 8px; font-size: 14px; font-weight: 500;">
                                        退出登录
                                    </button>
                                </div>

                                <!-- 版本信息 -->
                                <div style="text-align: center; margin-top: 16px; padding: 12px; color: #999; font-size: 11px;">
                                    万亩数田 v1.2.0<br>
                                    © 2024 农联网科技
                                </div>
                            </div>

                            <div class="bottom-nav">
                                <div class="nav-item">
                                    <div class="nav-icon">🏠</div>
                                    <div>首页</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📋</div>
                                    <div>工单</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">🗺️</div>
                                    <div>地图</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📝</div>
                                    <div>记录</div>
                                </div>
                                <div class="nav-item active">
                                    <div class="nav-icon">👤</div>
                                    <div>我的</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设计说明 -->
        <div style="background: white; border-radius: 12px; padding: 20px; margin-top: 30px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <h2 style="color: #4A6B37; margin-bottom: 16px;">🎯 重新设计核心要点</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h3 style="color: #5D7C47; margin-bottom: 12px;">📋 业务逻辑重构</h3>
                    <ul style="font-size: 14px; color: #666; line-height: 1.8; list-style: none; padding: 0;">
                        <li><strong>✅ 工单</strong> = 被动执行管理员分配的任务</li>
                        <li><strong>📝 记录</strong> = 主动上报发现的情况</li>
                        <li><strong>🗺️ 农田圈划</strong> = 专项测绘功能</li>
                        <li><strong>🏠 首页</strong> = 工作台概念，状态概览</li>
                    </ul>
                </div>
                
                <div>
                    <h3 style="color: #5D7C47; margin-bottom: 12px;">🔧 功能不是手段</h3>
                    <ul style="font-size: 14px; color: #666; line-height: 1.8; list-style: none; padding: 0;">
                        <li><strong>❌ 拍照上传</strong> → ✅ 作业记录的手段</li>
                        <li><strong>❌ 语音记录</strong> → ✅ 记录方式之一</li>
                        <li><strong>❌ 位置打卡</strong> → ✅ 工单执行验证</li>
                        <li><strong>❌ 异常上报</strong> → ✅ 问题记录类型</li>
                    </ul>
                </div>
                
                <div>
                    <h3 style="color: #5D7C47; margin-bottom: 12px;">🌾 符合农业场景</h3>
                    <ul style="font-size: 14px; color: #666; line-height: 1.8; list-style: none; padding: 0;">
                        <li><strong>干完再填</strong>：作业完成后一次性填报</li>
                        <li><strong>简单快速</strong>：关键信息快速录入</li>
                        <li><strong>结果导向</strong>：关注完成情况，不是过程</li>
                        <li><strong>离线支持</strong>：田间网络不稳定时可用</li>
                    </ul>
                </div>
                
                <div>
                    <h3 style="color: #5D7C47; margin-bottom: 12px;">🎨 配色优化调整</h3>
                    <ul style="font-size: 14px; color: #666; line-height: 1.8; list-style: none; padding: 0;">
                        <li><strong>大地绿主题</strong>：沉稳自然，贴近农业</li>
                        <li><strong>降低饱和度</strong>：减少视觉疲劳</li>
                        <li><strong>高对比度</strong>：保证户外可见性</li>
                        <li><strong>护眼配色</strong>：长时间使用不刺眼</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 20px; padding: 16px; background: #F0F4ED; border-radius: 8px; border-left: 4px solid #5D7C47;">
                <h4 style="color: #4A6B37; margin-bottom: 8px;">🎨 配色方案说明</h4>
                <p style="font-size: 14px; color: #4A6B37; margin: 0; line-height: 1.6;">
                    从<strong>艳丽橙色</strong>调整为<strong>沉稳大地绿</strong>，更符合农业工作环境。
                    新配色方案采用<strong>#5D7C47（主绿色）</strong>和<strong>#4A6B37（深绿色）</strong>，
                    既保持了足够的对比度确保户外可见性，又营造出<strong>专业、稳重、贴近自然</strong>的视觉感受。
                    同时降低了整体饱和度，减少长时间使用的视觉疲劳。
                </p>
            </div>
        </div>
    </div>

    <script>
        // Tab切换功能
        function switchTab(clickedTab, tabId) {
            // 移除所有tab的active状态
            document.querySelectorAll('.tab-btn').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 隐藏所有tab内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            
            // 激活点击的tab
            clickedTab.classList.add('active');
            
            // 显示对应的内容
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.style.display = 'block';
            }
        }

        // 打开工单详情
        function openTaskDetail(taskId) {
            alert(`📋 打开工单详情\n\n工单ID: ${taskId}\n\n将跳转到工单详情页面，显示：\n• 详细作业要求\n• 完成填报表单\n• 提交确认功能`);
        }

        // 导航切换效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                const parent = this.closest('.bottom-nav');
                parent.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 按钮交互效果
        document.querySelectorAll('.action-btn, .record-type-btn, .tool-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // 工单完成按钮
        document.querySelectorAll('.complete-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('🎉 工单完成！\n\n已记录：\n• 完成面积：85亩\n• 用肥量：4250公斤\n• 质量评价：良好\n\n数据已同步到管理系统');
            });
        });

        // 圈划功能演示
        function startDrawing() {
            alert('🗺️ 开始农田圈划\n\n请在地图上拖拽手指\n沿着地块边界绘制区域\n\n系统将自动计算面积\n并生成圈划记录');
        }

        // 模拟实时数据更新
        setInterval(() => {
            const onlineDots = document.querySelectorAll('.online-dot');
            onlineDots.forEach(dot => {
                dot.style.transform = `scale(${0.8 + Math.random() * 0.4})`;
            });
        }, 2000);
    </script>
</body>
</html>
                                    <div class="nav-icon">🏠</div>
                                    <div>首页</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📋</div>
                                    <div>工单</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">🗺️</div>
                                    <div>地图</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📝</div>
                                    <div>记录</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👤</div>
                                    <div>我的</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 工单页面 - 被动执行任务 -->
            <div>
                <div class="phone-label">📋 工单 - 执行分配任务</div>
                <div class="phone">
                    <div class="screen">
                        <div class="status-bar">
                            <div class="status-left">
                                <div class="online-dot"></div>
                                <span>工单已同步</span>
                            </div>
                            <div>09:24</div>
                        </div>
                        
                        <div class="page">
                            <div class="top-bar">
                                <div class="page-title">我的工单</div>
                                <div class="avatar">李</div>
                            </div>
                            
                            <div class="content">
                                <!-- 工单统计 -->
                                <div class="summary-card">
                                    <div class="summary-content">
                                        <div class="summary-title">工单概况</div>
                                        <div class="summary-stats">
                                            <div class="summary-stat">
                                                <div class="stat-value">3</div>
                                                <div class="stat-label">待处理</div>
                                            </div>
                                            <div class="summary-stat">
                                                <div class="stat-value">1</div>
                                                <div class="stat-label">进行中</div>
                                            </div>
                                            <div class="summary-stat">
                                                <div class="stat-value">15</div>
                                                <div class="stat-label">本周完成</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 工单列表 -->
                                <div class="section-header">
                                    <div class="section-title">今日工单</div>
                                    <a href="#" class="section-action">全部工单</a>
                                </div>
                                
                                <div class="task-list">
                                    <div class="task-item">
                                        <div class="task-icon">🌱</div>
                                        <div class="task-info">
                                            <div class="task-title">A区玉米施肥作业</div>
                                            <div class="task-meta">
                                                <span>📍 A-01地块</span>
                                                <span>⏰ 09:00-12:00</span>
                                                <span>👤 王管理员分配</span>
                                            </div>
                                        </div>
                                        <div class="task-status status-pending">进行中</div>
                                    </div>
                                    <div class="task-item">
                                        <div class="task-icon">🚿</div>
                                        <div class="task-info">
                                            <div class="task-title">B区灌溉系统检查</div>
                                            <div class="task-meta">
                                                <span>📍 B-03地块</span>
                                                <span>⏰ 14:00-16:00</span>
                                                <span>👤 王管理员分配</span>
                                            </div>
                                        </div>
                                        <div class="task-status status-urgent">待开始</div>
                                    </div>
                                    <div class="task-item">
                                        <div class="task-icon">🗺️</div>
                                        <div class="task-info">
                                            <div class="task-title">新地块边界测绘</div>
                                            <div class="task-meta">
                                                <span>📍 C区东侧</span>
                                                <span>⏰ 16:30-18:00</span>
                                                <span>👤 王管理员分配</span>
                                            </div>
                                        </div>
                                        <div class="task-status status-pending">待开始</div>
                                    </div>
                                </div>
                            </div>

                            <div class="bottom-nav">
                                <div class="nav-item">
                                    <div class="nav-icon">🏠</div>
                                    <div>首页</div>
                                </div>
                                <div class="nav-item active">