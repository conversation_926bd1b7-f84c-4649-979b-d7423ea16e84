<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基地管理员 - 万亩数田 App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.5;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #5D7C47, #4A6B37);
            color: white;
            padding: 20px;
            border-radius: 12px;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .phone-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
        }

        .phone {
            background: #000;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 320px;
            height: 680px;
            margin: 0 auto;
        }

        .screen {
            background: #fff;
            border-radius: 18px;
            height: 100%;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            background: #5D7C47;
            height: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            color: white;
            font-size: 12px;
            font-weight: 500;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .online-dot {
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        .page {
            height: calc(100% - 30px);
            display: flex;
            flex-direction: column;
        }

        .top-bar {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #4A6B37;
        }

        .role-tag {
            font-size: 10px;
            padding: 4px 8px;
            border-radius: 10px;
            font-weight: 500;
            margin-left: 8px;
            background: #F0F4ED;
            color: #4A6B37;
        }

        .avatar {
            width: 36px;
            height: 36px;
            background: linear-gradient(45deg, #5D7C47, #7A9B63);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
        }

        .content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }

        .base-overview {
            background: linear-gradient(135deg, #5D7C47, #4A6B37);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            position: relative;
            overflow: hidden;
        }

        .base-overview::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        }

        .base-content {
            position: relative;
            z-index: 1;
        }

        .base-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .base-subtitle {
            font-size: 12px;
            opacity: 0.9;
            margin-bottom: 12px;
        }

        .base-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .base-metric {
            text-align: center;
        }

        .base-metric-value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .base-metric-label {
            font-size: 10px;
            opacity: 0.8;
        }

        .weather-card {
            background: linear-gradient(135deg, #6B8E5A, #5D7C47);
            color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .weather-left {
            flex: 1;
        }

        .weather-temp {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .weather-desc {
            font-size: 12px;
            opacity: 0.9;
        }

        .weather-icon {
            font-size: 28px;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-bottom: 16px;
        }

        .action-btn {
            background: white;
            border: 2px solid #5D7C47;
            border-radius: 12px;
            padding: 16px 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(93,124,71,0.1);
        }

        .action-btn:hover {
            background: #5D7C47;
            color: white;
            transform: translateY(-2px);
        }

        .action-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: #5D7C47;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .action-label {
            font-size: 11px;
            color: #5D7C47;
            text-align: center;
            font-weight: 600;
            line-height: 1.2;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card h3 {
            font-size: 16px;
            color: #4A6B37;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .alert-banner {
            background: #FEF7E6;
            border: 1px solid #F5D76E;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .alert-icon {
            width: 32px;
            height: 32px;
            background: #C4841D;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            flex-shrink: 0;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-size: 14px;
            font-weight: 600;
            color: #C4841D;
            margin-bottom: 4px;
        }

        .alert-desc {
            font-size: 12px;
            color: #C4841D;
            opacity: 0.8;
        }

        .staff-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .staff-item {
            padding: 14px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .staff-item:last-child {
            border-bottom: none;
        }

        .staff-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #5D7C47, #7A9B63);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: bold;
        }

        .staff-info {
            flex: 1;
        }

        .staff-name {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
        }

        .staff-meta {
            font-size: 12px;
            color: #888;
            display: flex;
            gap: 12px;
        }

        .staff-status {
            padding: 6px 10px;
            border-radius: 16px;
            font-size: 11px;
            font-weight: 600;
            white-space: nowrap;
        }

        .status-working {
            background: #F0F4ED;
            color: #4A6B37;
        }

        .status-idle {
            background: #FEF7E6;
            color: #C4841D;
        }

        .status-offline {
            background: #F5F5F5;
            color: #666;
        }

        .task-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .task-item {
            padding: 14px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-item:hover {
            background: #fafafa;
        }

        .task-icon {
            width: 40px;
            height: 40px;
            background: #F0F4ED;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .task-info {
            flex: 1;
        }

        .task-title {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #333;
        }

        .task-meta {
            font-size: 12px;
            color: #888;
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .task-status {
            padding: 6px 10px;
            border-radius: 16px;
            font-size: 11px;
            font-weight: 600;
            white-space: nowrap;
        }

        .status-pending {
            background: #FEF7E6;
            color: #C4841D;
        }

        .status-urgent {
            background: #FFEBEE;
            color: #C62828;
        }

        .status-completed {
            background: #F0F4ED;
            color: #4A6B37;
        }

        .bottom-nav {
            height: 60px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            color: #999;
            font-size: 10px;
            cursor: pointer;
            transition: color 0.2s;
        }

        .nav-item.active {
            color: #5D7C47;
        }

        .nav-icon {
            font-size: 20px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #4A6B37;
        }

        .section-action {
            font-size: 12px;
            color: #5D7C47;
            text-decoration: none;
            padding: 6px 12px;
            border: 1px solid #5D7C47;
            border-radius: 16px;
            transition: all 0.2s;
        }

        .section-action:hover {
            background: #5D7C47;
            color: white;
        }

        .phone-label {
            text-align: center;
            margin-bottom: 12px;
            font-weight: 600;
            color: #4A6B37;
            font-size: 16px;
        }

        .fab {
            position: absolute;
            bottom: 70px;
            right: 16px;
            width: 56px;
            height: 56px;
            background: #5D7C47;
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 6px 16px rgba(93,124,71,0.4);
            transition: all 0.3s;
        }

        .fab:hover {
            transform: scale(1.1);
        }

        /* 地块管理样式 */
        .map-container {
            background: linear-gradient(135deg, #7A9B63, #5D7C47);
            height: 180px;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            margin-bottom: 16px;
            position: relative;
            overflow: hidden;
        }

        .map-overlay {
            position: absolute;
            top: 12px;
            left: 12px;
            right: 12px;
            display: flex;
            gap: 8px;
        }

        .map-search {
            flex: 1;
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 20px;
            padding: 8px 12px;
            font-size: 12px;
        }

        .map-btn {
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 20px;
            padding: 8px 12px;
            font-size: 11px;
            cursor: pointer;
            color: #4A6B37;
            font-weight: 500;
            transition: all 0.2s;
        }

        .map-btn:hover {
            background: white;
        }

        .plot-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .plot-card {
            background: white;
            border-radius: 12px;
            padding: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .plot-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .plot-name {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .plot-area {
            font-size: 11px;
            color: #888;
        }

        .plot-crop {
            font-size: 12px;
            color: #5D7C47;
            margin-bottom: 8px;
        }

        .plot-status {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
        }

        .plot-health {
            color: #4A6B37;
            font-weight: 500;
        }

        .plot-tasks {
            color: #666;
        }

        /* 任务创建表单 */
        .task-form {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #5D7C47;
        }

        .form-textarea {
            resize: none;
            height: 80px;
        }

        .submit-btn {
            width: 100%;
            padding: 16px;
            background: #5D7C47;
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .submit-btn:hover {
            background: #4A6B37;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌾 基地管理员 - 万亩数田</h1>
            <p>现场指挥 · 任务调度 · 团队协作 · 质量监督</p>
        </div>

        <div class="phone-grid">
            <!-- 工作台首页 -->
            <div>
                <div class="phone-label">🏠 工作台 - 基地管理中心</div>
                <div class="phone">
                    <div class="screen">
                        <div class="status-bar">
                            <div class="status-left">
                                <div class="online-dot"></div>
                                <span>华北基地在线</span>
                            </div>
                            <div>09:24</div>
                        </div>
                        
                        <div class="page">
                            <div class="top-bar">
                                <div>
                                    <span class="page-title">华北生产基地</span>
                                    <span class="role-tag">基地管理员</span>
                                </div>
                                <div class="avatar">王</div>
                            </div>
                            
                            <div class="content">
                                <!-- 基地概况 -->
                                <div class="base-overview">
                                    <div class="base-content">
                                        <div class="base-title">今日基地概况</div>
                                        <div class="base-subtitle">河北省承德市 · 总面积1200亩 · 15名员工</div>
                                        <div class="base-metrics">
                                            <div class="base-metric">
                                                <div class="base-metric-value">8</div>
                                                <div class="base-metric-label">在岗人员</div>
                                            </div>
                                            <div class="base-metric">
                                                <div class="base-metric-value">12</div>
                                                <div class="base-metric-label">进行中任务</div>
                                            </div>
                                            <div class="base-metric">
                                                <div class="base-metric-value">3</div>
                                                <div class="base-metric-label">待处理异常</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 天气信息 -->
                                <div class="weather-card">
                                    <div class="weather-left">
                                        <div class="weather-temp">23°C</div>
                                        <div class="weather-desc">多云转晴 · 湿度65% · 微风</div>
                                        <div style="font-size: 11px; opacity: 0.8; margin-top: 4px;">
                                            明日：大风降温，注意保温措施
                                        </div>
                                    </div>
                                    <div class="weather-icon">🌤️</div>
                                </div>

                                <!-- 紧急告警 -->
                                <div class="alert-banner">
                                    <div class="alert-icon">⚠️</div>
                                    <div class="alert-content">
                                        <div class="alert-title">设备异常告警</div>
                                        <div class="alert-desc">3号灌溉系统离线，影响B区块200亩作物灌溉</div>
                                    </div>
                                </div>

                                <!-- 快速操作 -->
                                <div class="section-header">
                                    <div class="section-title">快速操作</div>
                                </div>
                                <div class="quick-actions">
                                    <button class="action-btn">
                                        <div class="action-icon">👥</div>
                                        <div class="action-label">人员<br>查看</div>
                                    </button>
                                    <button class="action-btn">
                                        <div class="action-icon">📋</div>
                                        <div class="action-label">派发<br>任务</div>
                                    </button>
                                    <button class="action-btn">
                                        <div class="action-icon">🗺️</div>
                                        <div class="action-label">地块<br>圈划</div>
                                    </button>
                                    <button class="action-btn">
                                        <div class="action-icon">📞</div>
                                        <div class="action-label">紧急<br>联系</div>
                                    </button>
                                </div>

                                <!-- 最新动态 -->
                                <div class="section-header">
                                    <div class="section-title">最新动态</div>
                                    <a href="#" class="section-action">查看更多</a>
                                </div>
                                
                                <div class="task-list">
                                    <div class="task-item">
                                        <div class="task-icon">📝</div>
                                        <div class="task-info">
                                            <div class="task-title">李明完成A区施肥工单</div>
                                            <div class="task-meta">
                                                <span>📍 A-01地块</span>
                                                <span>⏰ 2分钟前</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="task-item">
                                        <div class="task-icon">⚠️</div>
                                        <div class="task-info">
                                            <div class="task-title">张强上报B区虫害问题</div>
                                            <div class="task-meta">
                                                <span>📍 B-03地块</span>
                                                <span>⏰ 15分钟前</span>
                                            </div>
                                        </div>
                                        <div class="task-status status-urgent">待处理</div>
                                    </div>
                                </div>
                            </div>

                            <div class="bottom-nav">
                                <div class="nav-item active">
                                    <div class="nav-icon">🏠</div>
                                    <div>工作台</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👥</div>
                                    <div>人员</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">🗺️</div>
                                    <div>地块</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📋</div>
                                    <div>任务</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👤</div>
                                    <div>我的</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 人员查看页面 -->
            <div>
                <div class="phone-label">👥 人员查看 - 团队状态</div>
                <div class="phone">
                    <div class="screen">
                        <div class="status-bar">
                            <div class="status-left">
                                <div class="online-dot"></div>
                                <span>8人在线</span>
                            </div>
                            <div>09:24</div>
                        </div>
                        
                        <div class="page">
                            <div class="top-bar">
                                <div class="page-title">人员查看</div>
                                <div class="avatar">王</div>
                            </div>
                            
                            <div class="content">
                                <!-- 人员概况 -->
                                <div class="base-overview">
                                    <div class="base-content">
                                        <div class="base-title">团队状态</div>
                                        <div class="base-metrics">
                                            <div class="base-metric">
                                                <div class="base-metric-value">8</div>
                                                <div class="base-metric-label">在岗工作</div>
                                            </div>
                                            <div class="base-metric">
                                                <div class="base-metric-value">3</div>
                                                <div class="base-metric-label">空闲待命</div>
                                            </div>
                                            <div class="base-metric">
                                                <div class="base-metric-value">4</div>
                                                <div class="base-metric-label">离线休息</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 人员列表 -->
                                <div class="section-header">
                                    <div class="section-title">在线人员</div>
                                    <a href="#" class="section-action">全部人员</a>
                                </div>
                                
                                <div class="staff-list">
                                    <div class="staff-item">
                                        <div class="staff-avatar">李</div>
                                        <div class="staff-info">
                                            <div class="staff-name">李明</div>
                                            <div class="staff-meta">
                                                <span>📍 A-01地块</span>
                                                <span>🔧 施肥作业</span>
                                                <span>⏰ 进行中2小时</span>
                                            </div>
                                        </div>
                                        <div class="staff-status status-working">作业中</div>
                                    </div>
                                    <div class="staff-item">
                                        <div class="staff-avatar">张</div>
                                        <div class="staff-info">
                                            <div class="staff-name">张强</div>
                                            <div class="staff-meta">
                                                <span>📍 B-03地块</span>
                                                <span>🚿 设备检查</span>
                                                <span>⏰ 刚刚上报异常</span>
                                            </div>
                                        </div>
                                        <div class="staff-status status-working">作业中</div>
                                    </div>
                                    <div class="staff-item">
                                        <div class="staff-avatar">赵</div>
                                        <div class="staff-info">
                                            <div class="staff-name">赵磊</div>
                                            <div class="staff-meta">
                                                <span>📍 机库</span>
                                                <span>⚡ 空闲待命</span>
                                                <span>⏰ 刚完成任务</span>
                                            </div>
                                        </div>
                                        <div class="staff-status status-idle">空闲</div>
                                    </div>
                                    <div class="staff-item">
                                        <div class="staff-avatar">孙</div>
                                        <div class="staff-info">
                                            <div class="staff-name">孙华</div>
                                            <div class="staff-meta">
                                                <span>📍 C-05地块</span>
                                                <span>🌾 巡田检查</span>
                                                <span>⏰ 进行中30分钟</span>
                                            </div>
                                        </div>
                                        <div class="staff-status status-working">作业中</div>
                                    </div>
                                    <div class="staff-item">
                                        <div class="staff-avatar">刘</div>
                                        <div class="staff-info">
                                            <div class="staff-name">刘芳</div>
                                            <div class="staff-meta">
                                                <span>📍 食堂</span>
                                                <span>🍽️ 午休中</span>
                                                <span>⏰ 1小时前离线</span>
                                            </div>
                                        </div>
                                        <div class="staff-status status-offline">离线</div>
                                    </div>
                                </div>

                                <!-- 人员统计 -->
                                <div class="card">
                                    <h3>📊 人员工作统计</h3>
                                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                            <div style="font-size: 16px; font-weight: bold; color: #5D7C47; margin-bottom: 4px;">96.2%</div>
                                            <div style="font-size: 11px; color: #666;">今日出勤率</div>
                                        </div>
                                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                            <div style="font-size: 16px; font-weight: bold; color: #4A6B37; margin-bottom: 4px;">89.5%</div>
                                            <div style="font-size: 11px; color: #666;">任务完成率</div>
                                        </div>
                                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                            <div style="font-size: 16px; font-weight: bold; color: #6B8E5A; margin-bottom: 4px;">7.2h</div>
                                            <div style="font-size: 11px; color: #666;">平均工作时长</div>
                                        </div>
                                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                            <div style="font-size: 16px; font-weight: bold; color: #7A9B63; margin-bottom: 4px;">23</div>
                                            <div style="font-size: 11px; color: #666;">今日记录条数</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bottom-nav">
                                <div class="nav-item">
                                    <div class="nav-icon">🏠</div>
                                    <div>工作台</div>
                                </div>
                                <div class="nav-item active">
                                    <div class="nav-icon">👥</div>
                                    <div>人员</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">🗺️</div>
                                    <div>地块</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📋</div>
                                    <div>任务</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👤</div>
                                    <div>我的</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 地块管理页面 -->
            <div>
                <div class="phone-label">🗺️ 地块管理 - 大面积圈划规划</div>
                <div class="phone">
                    <div class="screen">
                        <div class="status-bar">
                            <div class="status-left">
                                <div class="online-dot"></div>
                                <span>GPS高精度模式</span>
                            </div>
                            <div>09:24</div>
                        </div>
                        
                        <div class="page">
                            <div class="top-bar">
                                <div class="page-title">地块管理</div>
                                <div class="avatar">王</div>
                            </div>
                            
                            <div class="content">
                                <!-- 地图容器 -->
                                <div class="map-container">
                                    <div class="map-overlay">
                                        <input type="text" class="map-search" placeholder="搜索地块或区域...">
                                        <button class="map-btn" onclick="startManagerDrawing()">规划圈地</button>
                                        <button class="map-btn">图层切换</button>
                                    </div>
                                    
                                    <div style="text-align: center; margin-top: 50px;">
                                        <div style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">🗺️ 华北基地全景</div>
                                        <div style="font-size: 11px; opacity: 0.8;">12个地块 · 1200亩 · 长按开始大面积规划</div>
                                    </div>
                                </div>

                                <!-- 地块概览 -->
                                <div class="section-header">
                                    <div class="section-title">地块分布</div>
                                    <a href="#" class="section-action">列表视图</a>
                                </div>
                                
                                <div class="plot-grid">
                                    <div class="plot-card">
                                        <div class="plot-header">
                                            <div class="plot-name">A区域</div>
                                            <div class="plot-area">480亩</div>
                                        </div>
                                        <div class="plot-crop">🌽 玉米主产区 · 4个地块</div>
                                        <div class="plot-status">
                                            <span class="plot-health">长势良好</span>
                                            <span class="plot-tasks">6项任务</span>
                                        </div>
                                    </div>
                                    
                                    <div class="plot-card">
                                        <div class="plot-header">
                                            <div class="plot-name">B区域</div>
                                            <div class="plot-area">320亩</div>
                                        </div>
                                        <div class="plot-crop">🌾 小麦种植区 · 3个地块</div>
                                        <div class="plot-status">
                                            <span style="color: #C4841D; font-weight: 500;">设备异常</span>
                                            <span class="plot-tasks">4项任务</span>
                                        </div>
                                    </div>
                                    
                                    <div class="plot-card">
                                        <div class="plot-header">
                                            <div class="plot-name">C区域</div>
                                            <div class="plot-area">200亩</div>
                                        </div>
                                        <div class="plot-crop">🥬 蔬菜大棚区 · 3个地块</div>
                                        <div class="plot-status">
                                            <span class="plot-health">正常运行</span>
                                            <span class="plot-tasks">2项任务</span>
                                        </div>
                                    </div>
                                    
                                    <div class="plot-card">
                                        <div class="plot-header">
                                            <div class="plot-name">D区域</div>
                                            <div class="plot-area">200亩</div>
                                        </div>
                                        <div class="plot-crop">🆕 新规划区域 · 待开发</div>
                                        <div class="plot-status">
                                            <span style="color: #666; font-weight: 500;">规划中</span>
                                            <span class="plot-tasks">0项任务</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 管理操作 -->
                                <div class="card">
                                    <h3>🔧 地块管理操作</h3>
                                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                                        <button style="padding: 12px; background: #F0F4ED; border: 1px solid #5D7C47; border-radius: 8px; color: #4A6B37; font-size: 12px; font-weight: 600;">
                                            🗺️ 大面积规划
                                        </button>
                                        <button style="padding: 12px; background: #F0F4ED; border: 1px solid #5D7C47; border-radius: 8px; color: #4A6B37; font-size: 12px; font-weight: 600;">
                                            ✏️ 边界调整
                                        </button>
                                        <button style="padding: 12px; background: #F0F4ED; border: 1px solid #5D7C47; border-radius: 8px; color: #4A6B37; font-size: 12px; font-weight: 600;">
                                            📊 面积重算
                                        </button>
                                        <button style="padding: 12px; background: #F0F4ED; border: 1px solid #5D7C47; border-radius: 8px; color: #4A6B37; font-size: 12px; font-weight: 600;">
                                            📋 地块建档
                                        </button>
                                    </div>
                                </div>

                                <!-- 圈地功能说明 -->
                                <div class="card">
                                    <h3>💡 管理员圈地功能</h3>
                                    <div style="font-size: 12px; color: #666; line-height: 1.6;">
                                        <p style="margin-bottom: 8px;"><strong>大面积规划：</strong></p>
                                        <p>• 新基地整体规划和分区</p>
                                        <p>• 现有地块边界调整</p>
                                        <p>• 多地块合并或拆分</p>
                                        <p>• 自动生成地块编号和档案</p>
                                        <p style="margin-top: 8px; color: #5D7C47;"><strong>权限：</strong>可创建正式地块，生成标准档案</p>
                                    </div>
                                </div>
                            </div>

                            <button class="fab" onclick="startManagerDrawing()">✏️</button>

                            <div class="bottom-nav">
                                <div class="nav-item">
                                    <div class="nav-icon">🏠</div>
                                    <div>工作台</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👥</div>
                                    <div>人员</div>
                                </div>
                                <div class="nav-item active">
                                    <div class="nav-icon">🗺️</div>
                                    <div>地块</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📋</div>
                                    <div>任务</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👤</div>
                                    <div>我的</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务派发页面 -->
            <div>
                <div class="phone-label">📋 任务派发 - 工单管理</div>
                <div class="phone">
                    <div class="screen">
                        <div class="status-bar">
                            <div class="status-left">
                                <div class="online-dot"></div>
                                <span>任务系统在线</span>
                            </div>
                            <div>09:24</div>
                        </div>
                        
                        <div class="page">
                            <div class="top-bar">
                                <div class="page-title">任务派发</div>
                                <div class="avatar">王</div>
                            </div>
                            
                            <div class="content">
                                <!-- 任务统计 -->
                                <div class="base-overview">
                                    <div class="base-content">
                                        <div class="base-title">任务管理概况</div>
                                        <div class="base-metrics">
                                            <div class="base-metric">
                                                <div class="base-metric-value">24</div>
                                                <div class="base-metric-label">今日派发</div>
                                            </div>
                                            <div class="base-metric">
                                                <div class="base-metric-value">12</div>
                                                <div class="base-metric-label">进行中</div>
                                            </div>
                                            <div class="base-metric">
                                                <div class="base-metric-value">5</div>
                                                <div class="base-metric-label">待审核</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 快速派发 -->
                                <div class="card">
                                    <h3>⚡ 快速派发</h3>
                                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; margin-bottom: 16px;">
                                        <button style="padding: 12px 8px; background: #F0F4ED; border: 1px solid #5D7C47; border-radius: 8px; font-size: 11px; color: #4A6B37; cursor: pointer;">
                                            🌱<br>施肥作业
                                        </button>
                                        <button style="padding: 12px 8px; background: #F0F4ED; border: 1px solid #5D7C47; border-radius: 8px; font-size: 11px; color: #4A6B37; cursor: pointer;">
                                            🚿<br>灌溉作业
                                        </button>
                                        <button style="padding: 12px 8px; background: #F0F4ED; border: 1px solid #5D7C47; border-radius: 8px; font-size: 11px; color: #4A6B37; cursor: pointer;">
                                            🔧<br>设备维护
                                        </button>
                                        <button style="padding: 12px 8px; background: #F0F4ED; border: 1px solid #5D7C47; border-radius: 8px; font-size: 11px; color: #4A6B37; cursor: pointer;">
                                            🌾<br>收获作业
                                        </button>
                                        <button style="padding: 12px 8px; background: #F0F4ED; border: 1px solid #5D7C47; border-radius: 8px; font-size: 11px; color: #4A6B37; cursor: pointer;">
                                            🗺️<br>地块测绘
                                        </button>
                                        <button style="padding: 12px 8px; background: #F0F4ED; border: 1px solid #5D7C47; border-radius: 8px; font-size: 11px; color: #4A6B37; cursor: pointer;">
                                            👁️<br>巡田检查
                                        </button>
                                    </div>
                                </div>

                                <!-- 新建任务表单 -->
                                <div class="task-form">
                                    <h3 style="color: #4A6B37; margin-bottom: 16px;">📝 新建任务</h3>
                                    
                                    <div class="form-group">
                                        <label class="form-label">任务类型</label>
                                        <select class="form-select">
                                            <option>施肥作业</option>
                                            <option>灌溉作业</option>
                                            <option>设备维护</option>
                                            <option>地块测绘</option>
                                            <option>巡田检查</option>
                                        </select>
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                                        <div class="form-group">
                                            <label class="form-label">目标地块</label>
                                            <select class="form-select">
                                                <option>A-01地块</option>
                                                <option>A-02地块</option>
                                                <option>B-01地块</option>
                                                <option>C-05大棚</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">分配人员</label>
                                            <select class="form-select">
                                                <option>李明</option>
                                                <option>张强</option>
                                                <option>赵磊(空闲)</option>
                                                <option>孙华</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">任务要求</label>
                                        <textarea class="form-textarea" placeholder="请详细描述作业要求、注意事项等...">请按照标准流程进行A区玉米施肥作业，每亩用肥50公斤，注意用量均匀，避开灌溉设备。</textarea>
                                    </div>

                                    <button class="submit-btn">立即派发任务</button>
                                </div>

                                <!-- 待审核任务 -->
                                <div class="section-header">
                                    <div class="section-title">待我审核 (5)</div>
                                    <a href="#" class="section-action">批量审核</a>
                                </div>
                                
                                <div class="task-list">
                                    <div class="task-item">
                                        <div class="task-icon">✅</div>
                                        <div class="task-info">
                                            <div class="task-title">李明：A区施肥作业完成</div>
                                            <div class="task-meta">
                                                <span>📍 A-01地块</span>
                                                <span>⏰ 刚刚提交</span>
                                                <span>📷 3张照片</span>
                                            </div>
                                        </div>
                                        <div class="task-status status-pending">待审核</div>
                                    </div>
                                    <div class="task-item">
                                        <div class="task-icon">🔧</div>
                                        <div class="task-info">
                                            <div class="task-title">赵磊：设备维护完成</div>
                                            <div class="task-meta">
                                                <span>📍 机库</span>
                                                <span>⏰ 30分钟前</span>
                                                <span>📝 详细报告</span>
                                            </div>
                                        </div>
                                        <div class="task-status status-pending">待审核</div>
                                    </div>
                                </div>
                            </div>

                            <button class="fab">+</button>

                            <div class="bottom-nav">
                                <div class="nav-item">
                                    <div class="nav-icon">🏠</div>
                                    <div>工作台</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👥</div>
                                    <div>人员</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">🗺️</div>
                                    <div>地块</div>
                                </div>
                                <div class="nav-item active">
                                    <div class="nav-icon">📋</div>
                                    <div>任务</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👤</div>
                                    <div>我的</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的页面 - 基地管理员个人中心 -->
            <div>
                <div class="phone-label">👤 我的 - 基地管理员个人中心</div>
                <div class="phone">
                    <div class="screen">
                        <div class="status-bar">
                            <div class="status-left">
                                <div class="online-dot"></div>
                                <span>管理数据已同步</span>
                            </div>
                            <div>09:24</div>
                        </div>
                        
                        <div class="page">
                            <div class="top-bar">
                                <div class="page-title">个人中心</div>
                                <div class="avatar">王</div>
                            </div>
                            
                            <div class="content">
                                <!-- 个人信息卡片 -->
                                <div class="base-overview">
                                    <div class="base-content">
                                        <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 12px;">
                                            <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #5D7C47, #7A9B63); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold;">王</div>
                                            <div>
                                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 4px;">王明</div>
                                                <div style="font-size: 12px; opacity: 0.9;">基地管理员 · 华北生产基地</div>
                                                <div style="font-size: 12px; opacity: 0.8;">工号：MGR001 · 管理经验：5年</div>
                                            </div>
                                        </div>
                                        <div class="base-metrics">
                                            <div class="base-metric">
                                                <div class="base-metric-value">1200</div>
                                                <div class="base-metric-label">管理面积(亩)</div>
                                            </div>
                                            <div class="base-metric">
                                                <div class="base-metric-value">15</div>
                                                <div class="base-metric-label">团队人数</div>
                                            </div>
                                            <div class="base-metric">
                                                <div class="base-metric-value">4.9</div>
                                                <div class="base-metric-label">管理评分</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 管理数据 -->
                                <div class="card">
                                    <h3>📊 管理数据统计</h3>
                                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                            <div style="font-size: 20px; font-weight: bold; color: #5D7C47; margin-bottom: 4px;">856</div>
                                            <div style="font-size: 12px; color: #666;">本月派发任务</div>
                                        </div>
                                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                            <div style="font-size: 20px; font-weight: bold; color: #4A6B37; margin-bottom: 4px;">23</div>
                                            <div style="font-size: 12px; color: #666;">地块规划(个)</div>
                                        </div>
                                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                            <div style="font-size: 20px; font-weight: bold; color: #6B8E5A; margin-bottom: 4px;">96.8%</div>
                                            <div style="font-size: 12px; color: #666;">任务完成率</div>
                                        </div>
                                        <div style="text-align: center; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                            <div style="font-size: 20px; font-weight: bold; color: #7A9B63; margin-bottom: 4px;">127</div>
                                            <div style="font-size: 12px; color: #666;">问题处理(个)</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 基地管理功能 -->
                                <div class="card">
                                    <h3>🏭 基地管理</h3>
                                    <div style="space-y: 12px;">
                                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <div style="width: 32px; height: 32px; background: #F0F4ED; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 16px;">🌾</div>
                                                <div>
                                                    <div style="font-size: 14px; font-weight: 500;">基地设置</div>
                                                    <div style="font-size: 11px; color: #888;">基地信息、区域配置</div>
                                                </div>
                                            </div>
                                            <div style="color: #999; font-size: 18px;">›</div>
                                        </div>
                                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <div style="width: 32px; height: 32px; background: #F0F4ED; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 16px;">👥</div>
                                                <div>
                                                    <div style="font-size: 14px; font-weight: 500;">团队管理</div>
                                                    <div style="font-size: 11px; color: #888;">人员权限、绩效考核</div>
                                                </div>
                                            </div>
                                            <div style="color: #999; font-size: 18px;">›</div>
                                        </div>
                                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <div style="width: 32px; height: 32px; background: #F0F4ED; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 16px;">📊</div>
                                                <div>
                                                    <div style="font-size: 14px; font-weight: 500;">生产报表</div>
                                                    <div style="font-size: 11px; color: #888;">产量统计、效率分析</div>
                                                </div>
                                            </div>
                                            <div style="color: #999; font-size: 18px;">›</div>
                                        </div>
                                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px 0;">
                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                <div style="width: 32px; height: 32px; background: #F0F4ED; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 16px;">⚙️</div>
                                                <div>
                                                    <div style="font-size: 14px; font-weight: 500;">系统设置</div>
                                                    <div style="font-size: 11px; color: #888;">通知设置、权限管理</div>
                                                </div>
                                            </div>
                                            <div style="color: #999; font-size: 18px;">›</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 基地状态监控 -->
                                <div class="card">
                                    <h3>📡 基地监控状态</h3>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <span style="font-size: 14px;">设备在线率</span>
                                        <span style="font-size: 12px; color: #4A6B37;">96.2%</span>
                                    </div>
                                    <div style="width: 100%; height: 6px; background: #e0e0e0; border-radius: 3px; overflow: hidden; margin-bottom: 12px;">
                                        <div style="width: 96.2%; height: 100%; background: #5D7C47; border-radius: 3px;"></div>
                                    </div>
                                    
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <span style="font-size: 14px;">人员出勤率</span>
                                        <span style="font-size: 12px; color: #4A6B37;">93.3%</span>
                                    </div>
                                    <div style="width: 100%; height: 6px; background: #e0e0e0; border-radius: 3px; overflow: hidden; margin-bottom: 12px;">
                                        <div style="width: 93.3%; height: 100%; background: #6B8E5A; border-radius: 3px;"></div>
                                    </div>

                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <span style="font-size: 14px;">任务按时完成率</span>
                                        <span style="font-size: 12px; color: #4A6B37;">89.7%</span>
                                    </div>
                                    <div style="width: 100%; height: 6px; background: #e0e0e0; border-radius: 3px; overflow: hidden;">
                                        <div style="width: 89.7%; height: 100%; background: #7A9B63; border-radius: 3px;"></div>
                                    </div>
                                </div>

                                <!-- 数据同步 -->
                                <div class="card">
                                    <h3>☁️ 数据同步管理</h3>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <span style="font-size: 14px;">基地数据同步</span>
                                        <span style="font-size: 12px; color: #4A6B37;">刚刚</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <span style="font-size: 14px;">任务数据</span>
                                        <span style="font-size: 12px; color: #666;">856条已同步</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                        <span style="font-size: 14px;">地块数据</span>
                                        <span style="font-size: 12px; color: #666;">23个地块已更新</span>
                                    </div>
                                    <div style="display: flex; gap: 8px;">
                                        <button style="flex: 1; padding: 8px; background: #5D7C47; color: white; border: none; border-radius: 6px; font-size: 12px;">同步基地数据</button>
                                        <button style="flex: 1; padding: 8px; background: transparent; color: #666; border: 1px solid #ddd; border-radius: 6px; font-size: 12px;">导出报表</button>
                                    </div>
                                </div>

                                <!-- 退出登录 -->
                                <div style="margin-top: 20px;">
                                    <button style="width: 100%; padding: 14px; background: #FFEBEE; color: #C62828; border: 1px solid #FFCDD2; border-radius: 8px; font-size: 14px; font-weight: 500;">
                                        退出登录
                                    </button>
                                </div>

                                <!-- 版本信息 -->
                                <div style="text-align: center; margin-top: 16px; padding: 12px; color: #999; font-size: 11px;">
                                    万亩数田 管理版 v1.2.0<br>
                                    © 2024 农联网科技
                                </div>
                            </div>

                            <div class="bottom-nav">
                                <div class="nav-item">
                                    <div class="nav-icon">🏠</div>
                                    <div>工作台</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">👥</div>
                                    <div>人员</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">🗺️</div>
                                    <div>地块</div>
                                </div>
                                <div class="nav-item">
                                    <div class="nav-icon">📋</div>
                                    <div>任务</div>
                                </div>
                                <div class="nav-item active">
                                    <div class="nav-icon">👤</div>
                                    <div>我的</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设计说明 -->
        <div style="background: white; border-radius: 12px; padding: 20px; margin-top: 30px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <h2 style="color: #4A6B37; margin-bottom: 16px;">🌾 基地管理员重新设计要点</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h3 style="color: #5D7C47; margin-bottom: 12px;">🏠 工作台设计</h3>
                    <ul style="font-size: 14px; color: #666; line-height: 1.8; list-style: none; padding: 0;">
                        <li><strong>基地概况</strong>：人员、任务、异常一目了然</li>
                        <li><strong>天气集成</strong>：实时天气影响作业安排</li>
                        <li><strong>告警推送</strong>：设备异常及时提醒</li>
                        <li><strong>最新动态</strong>：团队实时工作状态</li>
                    </ul>
                </div>
                
                <div>
                    <h3 style="color: #5D7C47; margin-bottom: 12px;">👥 人员查看</h3>
                    <ul style="font-size: 14px; color: #666; line-height: 1.8; list-style: none; padding: 0;">
                        <li><strong>实时状态</strong>：在线人员位置和工作状态</li>
                        <li><strong>工作统计</strong>：出勤率和任务完成情况</li>
                        <li><strong>记录查看</strong>：人员提交的作业记录</li>
                        <li><strong>效率分析</strong>：团队工作效率统计</li>
                    </ul>
                </div>
                
                <div>
                    <h3 style="color: #5D7C47; margin-bottom: 12px;">🗺️ 地块管理</h3>
                    <ul style="font-size: 14px; color: #666; line-height: 1.8; list-style: none; padding: 0;">
                        <li><strong>大面积规划</strong>：整体基地分区和规划</li>
                        <li><strong>权威圈地</strong>：可创建正式地块档案</li>
                        <li><strong>边界调整</strong>：现有地块边界修正</li>
                        <li><strong>自动建档</strong>：生成标准地块档案</li>
                    </ul>
                </div>
                
                <div>
                    <h3 style="color: #5D7C47; margin-bottom: 12px;">📋 任务派发</h3>
                    <ul style="font-size: 14px; color: #666; line-height: 1.8; list-style: none; padding: 0;">
                        <li><strong>快速派发</strong>：常用任务模板化创建</li>
                        <li><strong>智能分配</strong>：基于能力和位置分配</li>
                        <li><strong>进度监控</strong>：实时跟踪任务执行</li>
                        <li><strong>质量审核</strong>：作业结果审核确认</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 20px; padding: 16px; background: #F0F4ED; border-radius: 8px; border-left: 4px solid #5D7C47;">
                <h4 style="color: #4A6B37; margin-bottom: 8px;">💡 与作业人员的区别</h4>
                <p style="font-size: 14px; color: #4A6B37; margin: 0; line-height: 1.6;">
                    基地管理员的设计重点在<strong>"管理和监督"</strong>，而不是执行具体作业。
                    圈地功能偏向<strong>大面积规划和基地分区</strong>，具有创建正式地块的权限。
                    任务管理从<strong>"接收任务"</strong>变为<strong>"派发任务"</strong>，
                    人员管理突出<strong>状态查看和工作监督</strong>的管理职能。
                    同时保持与作业人员一致的大地绿配色，确保系统视觉统一性。
                </p>
            </div>
        </div>
    </div>

    <script>
        // 管理员圈地功能
        function startManagerDrawing() {
            alert('🗺️ 管理员圈地模式\n\n功能特点：\n• 大面积地块规划\n• 多地块合并拆分\n• 自动生成地块编号\n• 创建正式地块档案\n\n开始在地图上规划新区域...');
        }

        // 导航切换效果
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                const parent = this.closest('.bottom-nav');
                parent.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 按钮交互效果
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // 任务派发
        document.querySelectorAll('.submit-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('📋 任务派发成功！\n\n任务详情：\n• 类型：施肥作业\n• 地块：A-01地块\n• 执行人：李明\n• 状态：已派发\n\n系统已通知执行人员');
            });
        });

        // FAB按钮效果
        document.querySelectorAll('.fab').forEach(fab => {
            fab.addEventListener('click', function() {
                this.style.transform = 'rotate(45deg) scale(1.1)';
                setTimeout(() => {
                    this.style.transform = 'rotate(0deg) scale(1)';
                }, 300);
            });
        });

        // 模拟实时数据更新
        setInterval(() => {
            const onlineDots = document.querySelectorAll('.online-dot');
            onlineDots.forEach(dot => {
                dot.style.transform = `scale(${0.8 + Math.random() * 0.4})`;
            });
        }, 2000);
    </script>
</body>
</html>
                                        <div class="staff-avatar">李</div>
                                        <div class="staff-info">
                                            <div class="staff-name">李明</div>
                                            <div class="staff-meta">
                                                <span>📍 A-01地块</span>
                                                <span>🔧 施肥作业</span>
                                                <span>⏰ 进行中2小时</span>
                                            </div>
                                        </div>
                                        <div class="staff-status status-working">作业中</div>
                                    </div>
                                    <div class="staff-item">
                                        <div class="staff-avatar">张</div>
                                        <div class="staff-info">
                                            <div class="staff-name">张强</div>
                                            <div class="staff-meta">
                                                <span>📍 B-03地块</span>
                                                <span>🚿 设备检查</span>
                                                <span>⏰ 刚刚上报异常</span>
                                            </div>
                                        </div>
                                        <div class="staff-status status-working">作业中</div>
                                    </div>
                                    <div class="staff-item">
                                        