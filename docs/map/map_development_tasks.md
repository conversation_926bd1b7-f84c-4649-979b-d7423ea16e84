# 地图功能开发任务拆解

## 📋 项目概述
基于 **MapLibre GL Native** 和 **Jetpack Compose** 的农业管理应用地图功能开发任务拆解，按照优先级和依赖关系进行分阶段实施。

## 🎯 核心功能模块

### 1. **基础地图模块** (Phase 1 - 基础设施)
### 2. **多图层管理模块** (Phase 2 - 核心功能)  
### 3. **地块圈划模块** (Phase 3 - 业务功能)
### 4. **标注管理模块** (Phase 4 - 业务功能)
### 5. **离线地图模块** (Phase 5 - 高级功能)
### 6. **数据同步模块** (Phase 6 - 企业功能)

---

## 🚀 Phase 1: 基础地图模块 (1-2周)

### 1.1 项目环境搭建
**优先级**: 🔴 P0 (必须)  
**预估工时**: 1天  
**负责人**: 主开发  

**任务清单**:
- [x] 添加 MapLibre GL Native 依赖到 build.gradle
- [x] 配置 AndroidManifest.xml 权限
- [x] 创建基础项目结构 (ViewModel, Managers, etc.)
- [ ] 配置混淆规则 (proguard-rules.pro)
- [x] 验证依赖是否正常导入

**验收标准**:
- 项目能正常编译
- MapLibre 相关类能正常导入
- 无依赖冲突错误

### 1.2 基础地图显示
**优先级**: 🔴 P0 (必须)  
**预估工时**: 2天  
**负责人**: 主开发  
**依赖**: 1.1 项目环境搭建

**任务清单**:
- [ ] 创建 `MapScreen.kt` Composable 界面
- [ ] 使用 `AndroidView` 嵌入 `MapView`
- [ ] 设置默认地图样式 (在线样式)
- [ ] 实现地图基础交互 (缩放、平移)
- [ ] 管理 `MapView` 的生命周期

**验收标准**:
- `MapScreen` 能显示空白地图
- 支持手势操作 (缩放、平移、旋转)
- Composable 生命周期与 `MapView` 同步正常

**核心代码**:
```kotlin
@Composable
fun MapScreen(
    modifier: Modifier = Modifier,
    viewModel: MapViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val mapView = remember { MapView(context) }

    // Lifecycle management
    LifecycleEffect(mapView) 

    AndroidView(
        factory = { mapView },
        modifier = modifier.fillMaxSize()
    ) { view ->
        // Update the map view here based on state from ViewModel
    }
}
```

### 1.3 地图管理器架构
**优先级**: 🔴 P0 (必须)  
**预估工时**: 3天  
**负责人**: 主开发  
**依赖**: 1.2 基础地图显示

**任务清单**:
- [ ] 创建 `MapViewModel`
- [ ] 创建 `MapController` 封装地图操作
- [ ] 创建 `LayerManager` 图层管理器
- [ ] 创建 `TileSourceManager` 瓦片源管理器
- [ ] 实现基础的错误处理机制

**验收标准**:
- `MapViewModel` 能驱动UI状态
- 架构清晰，职责分离
- 有完善的错误处理

### 1.4 位置服务集成
**优先级**: 🟡 P1 (重要)  
**预估工时**: 2天  
**负责人**: 主开发  
**依赖**: 1.3 地图管理器架构

**任务清单**:
- [ ] 在 `MapViewModel` 中集成 `LocationManager`
- [ ] 实现位置权限请求 (Accompanist-Permissions)
- [ ] 在 `MapUiState` 中暴露用户位置
- [ ] 实现定位到当前位置功能
- [ ] 处理位置服务异常情况

**验收标准**:
- 能获取用户当前位置
- 地图能定位到用户位置
- 权限处理完善且符合Compose方式

---

## 🗺️ Phase 2: 多图层管理模块 (2-3周)

### 2.1 瓦片源管理系统
**优先级**: 🔴 P0 (必须)  
**预估工时**: 3天  
**负责人**: 主开发  
**依赖**: Phase 1 完成

**任务清单**:
- [ ] 实现 TileSourceManager 完整功能
- [ ] 创建吉林一号瓦片源配置
- [ ] 创建天地图瓦片源配置
- [ ] 实现瓦片源动态切换
- [ ] 添加瓦片加载状态监听

**验收标准**:
- 能正常加载不同瓦片源
- 瓦片源切换流畅
- 有加载状态反馈

### 2.2 吉林一号认证系统
**优先级**: 🔴 P0 (必须)  
**预估工时**: 4天  
**负责人**: 主开发  
**依赖**: 2.1 瓦片源管理系统

**任务清单**:
- [ ] 创建 JilinAuthManager 认证管理器
- [ ] 实现 API 认证流程
- [ ] 添加 token 自动刷新机制
- [ ] 实现认证失败重试逻辑
- [ ] 创建认证状态监听器

**验收标准**:
- 能成功获取吉林一号 API token
- token 过期自动刷新
- 认证失败有合理降级方案

**核心代码**:
```kotlin
class JilinAuthManager {
    suspend fun authenticate(): AuthResponse
    suspend fun ensureValidToken()
    fun getAuthHeaders(): Map<String, String>
}
```

### 2.3 多图层叠加显示
**优先级**: 🔴 P0 (必须)  
**预估工时**: 4天  
**负责人**: 主开发  
**依赖**: 2.2 吉林一号认证系统

**任务清单**:
- [ ] 实现卫星底图图层 (吉林一号/天地图)
- [ ] 实现路网叠加图层 (天地图路网)
- [ ] 实现农田边界图层 (GeoJSON)
- [ ] 实现 POI 标注图层 (Symbol)
- [ ] 调试图层叠加顺序和透明度

**验收标准**:
- 4个图层能正常叠加显示
- 图层顺序正确
- 透明度效果正常

### 2.4 图层控制界面
**优先级**: 🟡 P1 (重要)  
**预估工时**: 3天  
**负责人**: UI开发  
**依赖**: 2.3 多图层叠加显示

**任务清单**:
- [ ] 设计图层控制面板 `LayerControlPanel` Composable
- [ ] 实现图层显示/隐藏开关
- [ ] 实现图层透明度滑块
- [ ] 添加图层切换动画效果 (Compose Animation)
- [ ] 实现图层控制状态与 `MapViewModel` 同步

**验收标准**:
- UI 美观易用，符合 Material 3
- 图层控制响应及时
- 设置能持久保存

---

## 🎨 Phase 3: 地块圈划模块 (3-4周)

### 3.1 手工绘制地块
**优先级**: 🔴 P0 (必须)  
**预估工时**: 5天  
**负责人**: 主开发  
**依赖**: Phase 2 完成

**任务清单**:
- [ ] 创建 `PlotDrawingManager` 绘制管理器
- [ ] 实现地图点击事件监听 (在 `AndroidView` 中)
- [ ] 实现多边形顶点添加逻辑
- [ ] 添加实时绘制预览 (更新GeoJSON源)
- [ ] 实现绘制撤销/重做功能
- [ ] 添加绘制完成确认机制

**验收标准**:
- 能通过点击绘制多边形
- 实时显示绘制进度
- 支持撤销和重做操作

**核心代码**:
```kotlin
// In PlotDrawingManager
class PlotDrawingManager(private val mapController: MapController) {
    fun startDrawing() { /* ... */ }
    fun addPoint(latLng: LatLng) { /* ... */ }
    fun finishDrawing(): LandPlot { /* ... */ }
    fun cancelDrawing() { /* ... */ }
}
```

### 3.2 GPS轨迹圈划
**优先级**: 🟡 P1 (重要)  
**预估工时**: 4天  
**负责人**: 主开发  
**依赖**: 3.1 手工绘制地块

**任务清单**:
- [ ] 实现 GPS 轨迹记录功能
- [ ] 添加轨迹点过滤算法 (去除漂移)
- [ ] 实现轨迹自动闭合逻辑
- [ ] 添加轨迹优化算法 (Douglas-Peucker)
- [ ] 实现轨迹录制状态指示

**验收标准**:
- GPS 轨迹记录准确
- 轨迹优化效果良好
- 自动闭合逻辑合理

### 3.3 几何图形绘制
**优先级**: 🟢 P2 (可选)  
**预估工时**: 3天  
**负责人**: 主开发  
**依赖**: 3.1 手工绘制地块

**任务清单**:
- [ ] 实现矩形地块绘制
- [ ] 实现圆形地块绘制
- [ ] 实现椭圆地块绘制
- [ ] 添加几何图形参数调整
- [ ] 实现图形转换为多边形

**验收标准**:
- 支持常用几何图形绘制
- 参数调整直观易用
- 转换结果准确

### 3.4 面积周长计算
**优先级**: 🔴 P0 (必须)  
**预估工时**: 2天  
**负责人**: 主开发  
**依赖**: 3.1 手工绘制地块

**任务清单**:
- [ ] 集成 Turf.js 几何计算库
- [ ] 实现多边形面积计算
- [ ] 实现多边形周长计算
- [ ] 添加实时计算显示
- [ ] 实现单位转换 (平方米/亩/公顷)

**验收标准**:
- 面积计算准确 (误差<1%)
- 支持多种单位显示
- 实时计算响应快速

### 3.5 地块数据管理
**优先级**: 🔴 P0 (必须)  
**预估工时**: 3天  
**负责人**: 主开发  
**依赖**: 3.4 面积周长计算

**任务清单**:
- [ ] 创建 LandPlot 数据模型
- [ ] 实现地块数据本地存储
- [ ] 添加地块编辑功能
- [ ] 实现地块删除功能
- [ ] 添加地块列表管理界面

**验收标准**:
- 地块数据持久化正常
- 支持地块的增删改查
- 数据结构设计合理

---

## 📍 Phase 4: 标注管理模块 (2-3周)

### 4.1 标注类型系统
**优先级**: 🔴 P0 (必须)  
**预估工时**: 2天  
**负责人**: 主开发  
**依赖**: Phase 3 完成

**任务清单**:
- [ ] 定义 8 种标注类型枚举
- [ ] 创建 MapAnnotation 数据模型
- [ ] 设计标注图标资源
- [ ] 实现标注类型选择界面
- [ ] 创建标注样式配置

**验收标准**:
- 8种标注类型定义清晰
- 图标设计美观统一
- 类型选择界面易用

**标注类型**:
1. 兴趣点 (POI)
2. 问题报告
3. 测量点
4. 照片位置
5. 水源标记
6. 设备位置
7. 边界标记
8. 文字备注

### 4.2 标注创建与编辑
**优先级**: 🔴 P0 (必须)  
**预估工时**: 4天  
**负责人**: 主开发  
**依赖**: 4.1 标注类型系统

**任务清单**:
- [ ] 实现地图长按创建标注
- [ ] 创建标注信息编辑界面
- [ ] 实现标注位置拖拽调整
- [ ] 添加标注信息表单验证
- [ ] 实现标注保存和取消逻辑

**验收标准**:
- 标注创建流程顺畅
- 信息编辑界面完善
- 位置调整操作直观

### 4.3 标注附件管理
**优先级**: 🟡 P1 (重要)  
**预估工时**: 3天  
**负责人**: 主开发  
**依赖**: 4.2 标注创建与编辑

**任务清单**:
- [ ] 实现照片拍摄和选择
- [ ] 添加语音录制功能
- [ ] 实现文件附件上传
- [ ] 创建附件预览界面
- [ ] 实现附件本地缓存

**验收标准**:
- 支持多种附件类型
- 附件上传稳定可靠
- 预览功能完善

### 4.4 标注显示与交互
**优先级**: 🔴 P0 (必须)  
**预估工时**: 3天  
**负责人**: 主开发  
**依赖**: 4.2 标注创建与编辑

**任务清单**:
- [ ] 实现标注在地图上的显示
- [ ] 添加标注点击查看详情
- [ ] 实现标注聚合显示 (高密度区域)
- [ ] 添加标注筛选功能
- [ ] 实现标注搜索功能

**验收标准**:
- 标注显示性能良好
- 交互响应及时
- 聚合效果合理

---

## 💾 Phase 5: 离线地图模块 (2-3周)

### 5.1 离线地图下载
**优先级**: 🟡 P1 (重要)  
**预估工时**: 4天  
**负责人**: 主开发  
**依赖**: Phase 2 完成

**任务清单**:
- [ ] 创建 OfflineMapManager 管理器
- [ ] 实现省级区域选择界面
- [ ] 添加离线地图下载功能
- [ ] 实现下载进度显示
- [ ] 添加下载暂停/恢复功能
- [ ] 实现下载失败重试机制

**验收标准**:
- 能成功下载省级离线地图
- 下载进度显示准确
- 网络异常处理完善

### 5.2 离线地图管理
**优先级**: 🟡 P1 (重要)  
**预估工时**: 3天  
**负责人**: 主开发  
**依赖**: 5.1 离线地图下载

**任务清单**:
- [ ] 创建离线地图列表界面
- [ ] 实现离线地图删除功能
- [ ] 添加存储空间管理
- [ ] 实现离线地图更新检查
- [ ] 添加离线地图使用统计

**验收标准**:
- 离线地图管理界面完善
- 存储空间控制有效
- 更新机制工作正常

### 5.3 离线数据同步
**优先级**: 🟢 P2 (可选)  
**预估工时**: 4天  
**负责人**: 主开发  
**依赖**: 5.2 离线地图管理

**任务清单**:
- [ ] 实现离线数据本地存储
- [ ] 添加数据变更追踪
- [ ] 实现网络恢复后自动同步
- [ ] 添加同步冲突解决机制
- [ ] 创建同步状态指示

**验收标准**:
- 离线数据不丢失
- 同步机制可靠
- 冲突处理合理

---

## 🔄 Phase 6: 数据同步模块 (1-2周)

### 6.1 图册数据同步
**优先级**: 🟡 P1 (重要)  
**预估工时**: 3天  
**负责人**: 后端+前端  
**依赖**: Phase 3, 4 完成

**任务清单**:
- [ ] 设计图册数据同步 API
- [ ] 实现地块数据上传下载
- [ ] 实现标注数据同步
- [ ] 添加增量同步机制
- [ ] 实现多设备数据一致性

**验收标准**:
- 数据同步准确无误
- 支持增量同步
- 多设备数据一致

### 6.2 协作功能
**优先级**: 🟢 P2 (可选)  
**预估工时**: 4天  
**负责人**: 后端+前端  
**依赖**: 6.1 图册数据同步

**任务清单**:
- [ ] 实现图册共享功能
- [ ] 添加协作权限管理
- [ ] 实现实时协作编辑
- [ ] 添加变更历史记录
- [ ] 创建协作通知机制

**验收标准**:
- 图册共享功能完善
- 权限控制准确
- 协作体验流畅

---

## 📊 开发计划总览

### 时间安排 (总计 10-15周)
```
Phase 1: 基础地图模块        ████████░░ (1-2周)
Phase 2: 多图层管理模块      ████████████░░ (2-3周)  
Phase 3: 地块圈划模块        ████████████████░░ (3-4周)
Phase 4: 标注管理模块        ████████████░░ (2-3周)
Phase 5: 离线地图模块        ████████████░░ (2-3周)
Phase 6: 数据同步模块        ████████░░ (1-2周)
```

### 人力资源分配
- **主开发 (1人)**: 负责核心地图功能开发
- **UI开发 (1人)**: 负责界面设计和前端交互
- **后端开发 (1人)**: 负责API设计和数据同步
- **测试 (1人)**: 负责功能测试和性能优化

### 里程碑节点
- **M1 (2周后)**: 基础地图显示 + 多图层叠加
- **M2 (5周后)**: 地块圈划功能完成
- **M3 (8周后)**: 标注管理功能完成  
- **M4 (12周后)**: 离线地图功能完成
- **M5 (15周后)**: 完整功能交付

### 风险评估
🔴 **高风险**:
- 吉林一号 API 认证集成复杂度
- GPS 轨迹圈划精度要求

🟡 **中风险**:
- 离线地图存储空间管理
- 多图层性能优化

🟢 **低风险**:
- 基础地图显示
- 标注管理功能

这个任务拆解为你们提供了清晰的开发路径，可以根据实际情况调整优先级和时间安排。建议先从 Phase 1 开始，逐步推进！ 