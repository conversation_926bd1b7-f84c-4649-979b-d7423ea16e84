# Phase 1: 基础地图模块 - 详细任务拆解

## 📋 阶段概述
**目标**: 搭建 MapLibre GL Native 基础设施，实现基本地图显示和核心架构
**总工时**: 8天 (1-2周)
**里程碑**: 在 Jetpack Compose 界面中能显示基础地图并支持基本交互

---

## 🔧 Task 1.1: 项目环境搭建

### 1.1.1 添加 MapLibre 依赖 ✅
**工时**: 2小时  
**负责人**: 主开发  
**状态**: 已完成

**具体步骤**:
1. [x] 修改 `app/build.gradle` 添加 MapLibre 依赖
2. [x] 同步项目，验证依赖下载成功

**需要修改的文件**:
- `app/build.gradle`

**代码实现**:
```gradle
dependencies {
    // MapLibre GL Native
    implementation 'org.maplibre.gl:android-sdk:11.10.3'
    
    // Jetpack Compose, Hilt, Coroutines etc. as per main architecture
}
```

**验收标准**:
- [x] 项目能正常编译
- [x] 能导入 `org.maplibre.gl.MapLibreMap` 类
- [x] 无依赖冲突错误

### 1.1.2 配置 AndroidManifest 权限 ✅
**工时**: 1小时  
**负责人**: 主开发  
**状态**: 已完成

**具体步骤**:
1. [x] 添加网络权限
2. [x] 添加位置权限 (粗略和精确)
3. [x] 添加存储权限 (离线地图用)
4. [x] 添加相机权限 (标注拍照用)

**需要修改的文件**:
- `app/src/main/AndroidManifest.xml`

**代码实现**:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.CAMERA" />
```

### 1.1.3 创建基础项目结构 ✅
**工时**: 1小时  
**负责人**: 主开发  
**状态**: 已完成

**实际实现说明**:
- 创建了核心的管理器、模型和工具类
- 遵循项目的Jetpack Compose单Activity架构
- 使用 `AndroidView` 在 `MapScreen` Composable 中嵌入 `MapView`

**已创建的文件结构**:
```
app/src/main/java/cn/agrolinking/wmst/
├── map/
│   ├── manager/
│   │   ├── MapLibreMapManager.kt ✅
│   │   ├── LayerManager.kt ✅
│   │   └── TileSourceManager.kt ✅
│   ├── model/
│   │   ├── LayerType.kt ✅
│   │   ├── TileSource.kt ✅
│   │   └── MapConfig.kt ✅
│   ├── MapController.kt
│   └── util/
└── ui/screens/map/
    ├── MapScreen.kt ✅ (已更新使用新架构)
    └── MapViewModel.kt
```

### 1.1.4 配置混淆规则 ✅
**工时**: 30分钟  
**负责人**: 主开发  
**状态**: 已完成

**具体步骤**:
1. [x] 添加 MapLibre 混淆规则
2. [x] 添加网络库混淆规则

**需要修改的文件**:
- `app/proguard-rules.pro`

**代码实现**:
```proguard
# MapLibre GL Native
-keep class org.maplibre.** { *; }
-dontwarn org.maplibre.**

# OkHttp
-dontwarn okhttp3.**
-keep class okhttp3.** { *; }

# Retrofit
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
```

---

## 🗺️ Task 1.2: 基础地图显示 ✅

### 1.2.1 在 Compose 中集成 MapView ✅
**工时**: 3小时  
**负责人**: 主开发  
**状态**: 已完成

**架构决策**: 遵循项目的Jetpack Compose单Activity架构，使用 `AndroidView` 在 `MapScreen` Composable 中嵌入 `MapView`

### 1.2.2 初始化 MapLibreMapManager ✅
**工时**: 4小时  
**负责人**: 主开发  
**状态**: 已完成

**已实现功能**:
- ✅ 单例模式设计
- ✅ 地图初始化逻辑
- ✅ 错误处理机制
- ✅ 地图准备和样式加载回调
- ✅ 地图默认配置设置
- ✅ 资源清理机制

### 1.2.3 创建 LayerManager 图层管理器 ✅
**工时**: 3小时  
**负责人**: 主开发  
**状态**: 已完成

**已实现功能**:
- ✅ LayerType 枚举定义（卫星底图、路网、农田边界、POI标注、用户位置、测量工具）
- ✅ 图层添加/删除逻辑
- ✅ 图层可见性控制
- ✅ 图层顺序管理（按zIndex排序）
- ✅ 图层分类管理（基础图层、业务图层、工具图层）

### 1.2.4 创建 TileSourceManager 瓦片源管理器 ✅
**工时**: 4小时  
**负责人**: 主开发  
**状态**: 已完成

**已实现功能**:
- ✅ 瓦片源注册机制
- ✅ 瓦片源切换逻辑
- ✅ 预定义默认瓦片源（OpenStreetMap、MapLibre演示）
- ✅ 栅格和矢量瓦片源支持
- ✅ 瓦片源状态管理

### 1.2.5 架构集成和验证 ✅
**工时**: 2小时  
**负责人**: 主开发  
**状态**: 已完成

**已实现功能**:
- ✅ Compose集成：在MapScreen中正确使用AndroidView嵌入MapView
- ✅ 生命周期管理：正确处理地图的创建、暂停、恢复和销毁
- ✅ 样式切换验证：实现"明亮样式"和"默认样式"按钮，可视化验证样式切换
- ✅ 地图导航验证：实现"飞到北京"、"飞到上海"按钮，验证地图动画
- ✅ 位置信息验证：实现"显示位置"按钮，输出当前地图中心和缩放级别

---

## 🧪 验证方法

### 方法1: 日志验证
运行以下命令查看地图管理器的工作状态：
```bash
adb logcat -s "MapLibreMapManager" -s "TileSourceManager" -s "LayerManager"
```

**预期日志输出**:
```
MapLibreMapManager: 开始初始化地图
MapLibreMapManager: 地图初始化成功
MapLibreMapManager: 设置地图样式: [样式URL]
MapLibreMapManager: 地图样式加载成功
TileSourceManager: 注册默认瓦片源
TileSourceManager: 注册瓦片源: OpenStreetMap 标准 (osm-standard)
TileSourceManager: 注册瓦片源: MapLibre 演示 (maplibre-demo)
```

### 方法2: 代码验证
在 `MapScreen.kt` 中添加调试代码来验证管理器状态：

```kotlin
// 在 MapScreen 中添加以下代码来验证管理器状态
LaunchedEffect(Unit) {
    delay(3000) // 等待地图加载完成
    
    // 验证 MapLibreMapManager
    val mapInfo = mapManager.getMapInfo()
    Log.d("MapVerification", mapInfo)
    
    // 验证 TileSourceManager
    val tileSourceInfo = tileSourceManager.getTileSourceInfo()
    Log.d("MapVerification", tileSourceInfo)
    
    // 验证 LayerManager (需要先添加一些图层)
    mapManager.onStyleLoaded { style ->
        val layerManager = LayerManager()
        val layerInfo = layerManager.getLayerInfo()
        Log.d("MapVerification", layerInfo)
    }
}
```

### 方法3: 功能验证
创建一个简单的测试界面来验证管理器功能：

```kotlin
// 在 MapScreen 中添加测试按钮
@Composable
fun MapScreen(viewModel: MapViewModel = hiltViewModel()) {
    // ... 现有代码 ...
    
    // 添加测试按钮
    Box(modifier = Modifier.fillMaxSize()) {
        // 地图显示代码...
        
        // 测试按钮
        Column(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(16.dp)
        ) {
            Button(onClick = {
                // 测试瓦片源切换
                mapManager.onStyleLoaded { style ->
                    tileSourceManager.addTileSourceToStyle(style, "maplibre-demo")
                }
            }) {
                Text("切换瓦片源")
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Button(onClick = {
                // 输出管理器状态
                Log.d("MapTest", mapManager.getMapInfo())
                Log.d("MapTest", tileSourceManager.getTileSourceInfo())
            }) {
                Text("输出状态")
            }
        }
    }
}
```

### 方法4: 架构验证
验证新架构是否正确工作：

1. **单例模式验证**: 多次调用 `MapLibreMapManager.getInstance()` 应该返回同一个实例
2. **回调机制验证**: 地图初始化和样式加载应该正确触发回调
3. **生命周期验证**: 应用暂停/恢复时地图应该正确处理生命周期
4. **内存管理验证**: 退出地图页面时应该正确清理资源

---

## 📍 Task 1.4: 位置服务集成 ✅

### 1.4.1 集成 LocationEngine ✅
**工时**: 3小时  
**负责人**: 主开发  
**状态**: 已完成

**具体步骤**:
1. [x] 添加位置服务依赖 (play-services-location:21.0.1)
2. [x] 创建位置管理器 (LocationManager.kt)
3. [x] 实现位置权限请求 (PermissionUtils.kt)
4. [x] 添加位置更新监听 (位置回调和状态管理)

**已实现功能**:
- ✅ 位置权限检查和请求
- ✅ 位置更新监听机制
- ✅ 位置状态管理（Flow）
- ✅ 错误处理和日志

### 1.4.2 实现用户位置显示 ✅
**工时**: 2小时  
**负责人**: 主开发  
**状态**: 已完成

**具体步骤**:
1. [x] 在地图上显示用户位置 (LocationComponent)
2. [x] 添加位置精度圆圈 (RenderMode.COMPASS)
3. [x] 实现位置跟随模式 (CameraMode.TRACKING)
4. [x] 添加定位按钮功能 ("我的位置"按钮)

**已实现功能**:
- ✅ 位置组件集成
- ✅ 位置显示和更新
- ✅ 位置跟随模式
- ✅ 相机动画控制

---

## ✅ Phase 1 验收清单

### 核心架构验收 ✅
- [x] MapLibreMapManager 单例模式正常工作
- [x] TileSourceManager 瓦片源管理正常工作
- [x] LayerManager 图层管理基础架构完成
- [x] 地图样式切换功能正常（明亮样式 ↔ 默认样式）
- [x] 地图导航动画正常（飞到北京、上海）
- [x] 地图状态信息获取正常（位置、缩放级别）

### 功能验收
- [x] 项目能正常编译运行
- [x] 能显示基础地图
- [x] 支持地图手势操作 (缩放、平移、旋转)
- [x] 能获取用户位置权限 ✅
- [x] 能在地图上显示用户位置 ✅
- [x] 定位按钮能正常工作 ✅
- [x] 地图状态能正确保存和恢复

### 代码质量验收
- [x] 代码结构清晰，职责分离
- [x] 有完善的错误处理机制
- [x] 内存泄漏检查通过
- [x] 权限处理符合 Android 规范 ✅

### 性能验收
- [x] 地图加载时间 < 3秒
- [x] 手势操作响应流畅 (60fps)
- [x] 内存使用合理 (< 100MB)

### 架构验收
- [x] 遵循 Jetpack Compose 单Activity架构
- [x] 使用 Hilt 依赖注入
- [x] 实现 UDF (单向数据流) 模式
- [x] 符合 Material Design 3 规范

---

## 🚀 下一步计划

### 🎯 当前状态总结
**Task 1.1 ✅**: 项目环境搭建完成  
**Task 1.2 ✅**: 基础地图显示和核心架构完成  
**Task 1.3**: 混淆规则配置 (可选，发布前处理)

### 📍 下一个任务: Phase 2 地图交互功能

**目标**: 实现地图的各种交互功能  
**工时**: 待评估  
**优先级**: 高

**主要内容**:
1. 实现地图点击和长按事件处理
2. 添加手势控制（缩放、旋转、倾斜）
3. 实现地图标注和信息窗口
4. 添加地图工具栏和控件

### 🔄 后续计划
1. **Phase 2**: 实现地图交互功能（点击、长按、手势等）
2. **Phase 3**: 添加地图标注和图层切换功能
3. **Phase 4**: 农田边界绘制和编辑功能

---

## 📊 进度统计
- **已完成**: 
  - Task 1.1 ✅ 项目环境搭建
  - Task 1.2 ✅ 基础地图显示和核心架构
  - Task 1.4 ✅ 位置服务集成
- **已完成**: Task 1.3 ✅ 混淆规则配置
- **待开始**: Phase 2, Phase 3, Phase 4

**总体进度**: Phase 1 基础架构 100% 完成 ✅ 