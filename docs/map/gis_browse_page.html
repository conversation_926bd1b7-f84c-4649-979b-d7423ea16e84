        // 截图功能
        function takeScreenshot() {
            console.log('截图功能');
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            // 这里可以添加实际的截图逻辑
            // 比如调用html2canvas或其他截图库
        }        /* 左下缩放控制区 */
        .left-bottom-controls {
            position: absolute;
            left: 20px;
            bottom: 100px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            z-index: 10;
        }

        .compass-btn {
            width: 44px;
            height: 44px;
            background: rgba(255,255,255,0.5);
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 15px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 4px;
        }

        .compass-btn:hover {
            background: rgba(255,255,255,0.7);
            transform: scale(1.08);
        }

        .zoom-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.5);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 15px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .zoom-btn:hover {
            background: rgba(255,255,255,0.7);
            transform: scale(1.08);
        }<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动GIS地图浏览页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            overflow: hidden;
            height: 100vh;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            margin: 20px auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 32px;
            position: relative;
            overflow: hidden;
        }

        .map-area {
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 30% 40%, rgba(46, 204, 113, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 70% 60%, rgba(52, 152, 219, 0.3) 0%, transparent 50%),
                linear-gradient(135deg, #2ecc71 0%, #3498db 100%);
            position: relative;
        }

        /* 顶部信息栏 */
        .top-info-bar {
            position: absolute;
            top: 60px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            color: white;
            font-size: 12px;
            z-index: 20;
        }

        .coordinates {
            background: rgba(0,0,0,0.4);
            padding: 12px 16px;
            border-radius: 16px;
            backdrop-filter: blur(10px);
            line-height: 1.4;
            font-weight: 500;
        }

        .camera-btn {
            background: rgba(255,255,255,0.5);
            padding: 12px;
            border-radius: 16px;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .camera-btn:hover {
            background: rgba(255,255,255,0.7);
            transform: scale(1.05);
        }

        /* 右上搜索按钮 */
        .top-search {
            position: absolute;
            top: 130px;
            right: 20px;
            width: 48px;
            height: 48px;
            background: rgba(255,255,255,0.5);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 15;
        }

        .top-search:hover {
            background: rgba(255,255,255,0.7);
            transform: scale(1.05);
        }

        /* 右侧工具栏 */
        .right-toolbar {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 16px;
            z-index: 10;
        }

        .tool-btn {
            width: 64px;
            height: 56px;
            background: rgba(255,255,255,0.5);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 6px 25px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 22px;
            position: relative;
            border: 2px solid transparent;
        }

        .tool-btn:hover {
            transform: translateX(-4px);
            background: rgba(255,255,255,0.7);
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
        }

        .tool-btn.active {
            background: rgba(52, 152, 219, 0.9);
            color: white;
            transform: translateX(-12px);
            border-color: rgba(52, 152, 219, 1);
        }

        .tool-btn.active::after {
            content: '';
            position: absolute;
            right: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 12px solid rgba(52, 152, 219, 0.9);
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
        }

        /* 图层选项面板 */
        .layer-panel {
            position: absolute;
            right: 76px;
            top: 50%;
            transform: translateY(-50%) translateX(30px);
            height: 56px;
            background: rgba(255,255,255,0.95);
            border-radius: 12px 6px 6px 12px;
            padding: 0 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 6px 25px rgba(0,0,0,0.2);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            pointer-events: none;
            border-left: 3px solid rgba(52, 152, 219, 0.3);
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
            gap: 4px;
            width: auto;
            z-index: 12;
        }

        .layer-panel.show {
            opacity: 1;
            transform: translateY(-50%) translateX(0);
            pointer-events: all;
            border-left-color: rgba(52, 152, 219, 1);
        }

        .layer-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 2px;
            padding: 3px 6px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 9px;
            color: #2c3e50;
            border: 1px solid transparent;
            white-space: nowrap;
            min-width: 40px;
            text-align: center;
            font-weight: 500;
            height: 44px;
        }

        .layer-option:hover {
            background: rgba(52, 152, 219, 0.1);
            transform: translateY(-2px);
            border-color: rgba(52, 152, 219, 0.3);
        }

        .layer-option.active {
            background: rgba(52, 152, 219, 0.2);
            color: #2c3e50;
            font-weight: 600;
            border-color: rgba(52, 152, 219, 0.5);
        }

        .layer-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent;
            border-radius: 4px;
            transition: all 0.3s ease;
            padding: 1px;
        }

        .layer-preview {
            width: 18px;
            height: 18px;
            border-radius: 3px;
            border: 1px solid rgba(255,255,255,0.9);
            box-shadow: 0 1px 3px rgba(0,0,0,0.15);
        }

        .jilin-satellite { 
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            position: relative;
        }
        
        .jilin-satellite::after {
            content: '吉';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 8px;
            font-weight: bold;
        }
        
        .tianditu-satellite { 
            background: linear-gradient(45deg, #667eea, #764ba2);
            position: relative;
        }
        
        .tianditu-satellite::after {
            content: '天';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 8px;
            font-weight: bold;
        }
        
        .gaode-satellite { 
            background: linear-gradient(45deg, #f093fb, #f5576c);
            position: relative;
        }
        
        .gaode-satellite::after {
            content: '高';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 8px;
            font-weight: bold;
        }

        .gaode-standard { 
            background: linear-gradient(45deg, #36d1dc, #5b86e5);
            position: relative;
        }
        
        .gaode-standard::after {
            content: '标';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 7px;
            font-weight: bold;
        }

        /* 左下缩放控制区 */
        .left-bottom-controls {
            position: absolute;
            left: 20px;
            bottom: 100px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            z-index: 10;
        }

        .zoom-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 15px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .zoom-btn:hover {
            background: rgba(255,255,255,1);
            transform: scale(1.08);
        }

        /* 右下控制区 */
        .right-bottom-controls {
            position: absolute;
            right: 20px;
            bottom: 120px;
            display: flex;
            flex-direction: column;
            gap: 12px;
            z-index: 10;
        }

        .control-btn {
            width: 52px;
            height: 52px;
            background: rgba(255,255,255,0.5);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: rgba(255,255,255,0.7);
            transform: scale(1.08);
        }

        /* 左侧控制区 */
        .left-controls {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 16px;
            z-index: 10;
        }

        .left-btn {
            width: 52px;
            height: 52px;
            background: rgba(255,255,255,0.9);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .left-btn:hover {
            background: rgba(255,255,255,1);
            transform: scale(1.08);
        }

        /* 定位图标 */
        .location-icon {
            width: 24px;
            height: 24px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .location-icon::before {
            content: '';
            width: 16px;
            height: 16px;
            border: 2px solid #2c3e50;
            border-radius: 50%;
            position: absolute;
        }

        .location-icon::after {
            content: '';
            width: 4px;
            height: 4px;
            background: #2c3e50;
            border-radius: 50%;
            position: absolute;
        }

        .location-cross {
            position: absolute;
            background: #2c3e50;
        }

        .location-cross.horizontal {
            width: 6px;
            height: 2px;
            left: -11px;
        }

        .location-cross.horizontal.right {
            left: 21px;
        }

        .location-cross.vertical {
            width: 2px;
            height: 6px;
            top: -11px;
        }

        .location-cross.vertical.bottom {
            top: 21px;
        }

        /* 比例尺 */
        .scale-bar {
            position: absolute;
            left: 20px;
            bottom: 30px;
            background: rgba(0,0,0,0.5);
            color: white;
            padding: 0;
            border-radius: 8px;
            font-size: 11px;
            backdrop-filter: blur(10px);
            font-weight: 600;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .scale-content {
            padding: 6px 12px 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
        }

        .scale-line {
            width: 60px;
            height: 3px;
            background: white;
            position: relative;
            margin: 2px 0;
        }

        .scale-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: -2px;
            width: 1px;
            height: 7px;
            background: white;
        }

        .scale-line::after {
            content: '';
            position: absolute;
            right: 0;
            top: -2px;
            width: 1px;
            height: 7px;
            background: white;
        }

        .scale-text {
            font-size: 10px;
            margin-bottom: 1px;
        }

        /* 底部信息 */
        .bottom-info {
            position: absolute;
            bottom: 40px;
            right: 20px;
            color: rgba(255,255,255,0.9);
            font-size: 10px;
            background: rgba(0,0,0,0.4);
            padding: 8px 16px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .phone-container {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
                margin: 0;
                padding: 0;
            }

            .screen {
                border-radius: 0;
            }
        }

        /* 加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .tool-btn, .control-btn, .left-btn {
            animation: fadeInUp 0.6s ease forwards;
        }

        .tool-btn:nth-child(1) { animation-delay: 0.1s; }
        .tool-btn:nth-child(2) { animation-delay: 0.2s; }
        .control-btn:nth-child(1) { animation-delay: 0.3s; }
        .control-btn:nth-child(2) { animation-delay: 0.4s; }
        .control-btn:nth-child(3) { animation-delay: 0.5s; }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <div class="map-area">
                <!-- 顶部信息栏 -->
                <div class="top-info-bar">
                    <div class="coordinates">
                        经度: 121.608447°<br>
                        纬度: 31.181755°<br>
                        层级: 18级
                    </div>
                    <div class="camera-btn" onclick="takeScreenshot()">
                        📷
                    </div>
                </div>

                <!-- 搜索按钮 -->
                <div class="top-search" onclick="handleSearch()">🔍</div>

                <!-- 右侧工具栏 -->
                <div class="right-toolbar">
                    <div class="tool-btn" onclick="toggleLayer(this)">
                        🗂️
                    </div>
                    <div class="tool-btn" onclick="goDrawMode()">
                        🖊️
                    </div>
                </div>

                <!-- 图层选择面板 -->
                <div class="layer-panel" id="layerPanel">
                    <div class="layer-option active" onclick="selectLayer(this)">
                        <div class="layer-icon">
                            <div class="layer-preview jilin-satellite"></div>
                        </div>
                        <span>吉林一号</span>
                    </div>
                    <div class="layer-option" onclick="selectLayer(this)">
                        <div class="layer-icon">
                            <div class="layer-preview tianditu-satellite"></div>
                        </div>
                        <span>天地图</span>
                    </div>
                    <div class="layer-option" onclick="selectLayer(this)">
                        <div class="layer-icon">
                            <div class="layer-preview gaode-satellite"></div>
                        </div>
                        <span>高德卫星</span>
                    </div>
                    <div class="layer-option" onclick="selectLayer(this)">
                        <div class="layer-icon">
                            <div class="layer-preview gaode-standard"></div>
                        </div>
                        <span>高德标准</span>
                    </div>
                </div>

                <!-- 左下缩放控制区 -->
                <div class="left-bottom-controls">
                    <div class="zoom-btn" onclick="zoomIn()">+</div>
                    <div class="zoom-btn" onclick="zoomOut()">−</div>
                </div>

                <!-- 右下控制区 -->
                <div class="right-bottom-controls">
                    <div class="control-btn" onclick="openFiles()">📁</div>
                </div>

                <!-- 左侧控制区 -->
                <div class="left-controls">
                    <div class="left-btn" onclick="resetNorth()">🧭</div>
                    <div class="left-btn" onclick="locateMe()">
                        <div class="location-icon">
                            <div class="location-cross horizontal"></div>
                            <div class="location-cross horizontal right"></div>
                            <div class="location-cross vertical"></div>
                            <div class="location-cross vertical bottom"></div>
                        </div>
                    </div>
                </div>

                <!-- 比例尺 -->
                <div class="scale-bar">
                    <div class="scale-content">
                        <div class="scale-text">20米</div>
                        <div class="scale-line"></div>
                    </div>
                </div>

                <!-- 底部信息 -->
                <div class="bottom-info">
                    <div>GS(2024)4191号</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isLayerPanelOpen = false;

        // 图层面板切换
        function toggleLayer(btn) {
            const panel = document.getElementById('layerPanel');
            const allBtns = document.querySelectorAll('.tool-btn');
            
            if (!isLayerPanelOpen) {
                // 打开面板
                allBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                panel.classList.add('show');
                isLayerPanelOpen = true;
                
                // 触觉反馈
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            } else {
                // 关闭面板
                btn.classList.remove('active');
                panel.classList.remove('show');
                isLayerPanelOpen = false;
            }
        }

        // 选择图层
        function selectLayer(option) {
            // 移除所有激活状态
            document.querySelectorAll('.layer-option').forEach(opt => {
                opt.classList.remove('active');
            });
            
            // 激活当前选项
            option.classList.add('active');
            
            // 触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(30);
            }
            
            // 获取图层名称并执行切换逻辑
            const layerName = option.querySelector('span').textContent;
            console.log(`切换到${layerName}图层`);
            
            // 这里可以添加实际的图层切换逻辑
            switch(layerName) {
                case '吉林一号':
                    console.log('加载吉林一号卫星图层');
                    // loadJilinSatellite();
                    break;
                case '天地图':
                    console.log('加载天地图卫星图层');
                    // loadTiandituSatellite();
                    break;
                case '高德卫星':
                    console.log('加载高德卫星图层');
                    // loadGaodeSatellite();
                    break;
                case '高德标准':
                    console.log('加载高德标准地图');
                    // loadGaodeStandard();
                    break;
            }
            
            // 延迟关闭面板，让用户看到选择反馈
            setTimeout(() => {
                document.getElementById('layerPanel').classList.remove('show');
                document.querySelector('.tool-btn.active').classList.remove('active');
                isLayerPanelOpen = false;
            }, 800);
        }

        // 搜索功能
        function handleSearch() {
            console.log('打开搜索');
            // 可以添加搜索弹窗或跳转逻辑
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        }

        // 进入绘制模式
        function goDrawMode() {
            console.log('进入绘制模式');
            // 这里可以跳转到绘制页面
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            alert('点击切换到绘制模式页面');
        }

        // 缩放控制
        function zoomIn() {
            console.log('放大地图');
            if (navigator.vibrate) {
                navigator.vibrate(30);
            }
        }

        function zoomOut() {
            console.log('缩小地图');
            if (navigator.vibrate) {
                navigator.vibrate(30);
            }
        }

        // 文件管理
        function openFiles() {
            console.log('打开文件管理');
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        }

        // 指南针复位
        function resetNorth() {
            console.log('指南针复位');
            if (navigator.vibrate) {
                navigator.vibrate(30);
            }
        }

        // 定位功能
        function locateMe() {
            console.log('定位到当前位置');
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        }

        // 点击地图区域关闭面板
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.right-toolbar') && 
                !e.target.closest('.layer-panel')) {
                if (isLayerPanelOpen) {
                    document.getElementById('layerPanel').classList.remove('show');
                    document.querySelector('.tool-btn.active')?.classList.remove('active');
                    isLayerPanelOpen = false;
                }
            }
        });

        // 防止面板内点击事件冒泡
        document.getElementById('layerPanel').addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'l':
                case 'L':
                    document.querySelector('.tool-btn').click();
                    break;
                case 'd':
                case 'D':
                    document.querySelectorAll('.tool-btn')[1].click();
                    break;
                case '+':
                case '=':
                    zoomIn();
                    break;
                case '-':
                    zoomOut();
                    break;
                case '/':
                    e.preventDefault();
                    handleSearch();
                    break;
            }
        });
    </script>
</body>
</html>