# 地图模块依赖版本统一规范

## MapLibre GL Native 统一版本
- 主SDK: org.maplibre.gl:android-sdk:11.10.3
- 注解插件: org.maplibre.gl:android-plugin-annotation-v9:3.0.0
- 位置插件: org.maplibre.gl:android-plugin-locationlayer-v9:0.12.0

## 版本更新原则
1. 所有文档必须使用统一版本号
2. 版本更新时需要同步更新所有相关文档
3. 新版本发布前需要进行兼容性测试

## 包名规范
- 基础包名: cn.agrolinking.wmst
- 地图模块: cn.agrolinking.wmst.map
- 管理器类: cn.agrolinking.wmst.map.manager
- 数据模型: cn.agrolinking.wmst.map.model

## 命名规范
- 地图管理器: MapLibreMapManager
- 图层管理器: LayerManager
- 瓦片源管理器: TileSourceManager
- 认证管理器: AuthManager

## 版本选择说明
- **11.10.3**: 最新稳定版，修复了getSource崩溃问题
- **更新时间**: 2025年6月13日
- **关键修复**: 解决多图层管理中的崩溃风险
