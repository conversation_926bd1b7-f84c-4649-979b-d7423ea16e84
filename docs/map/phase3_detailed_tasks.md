# Phase 3: 地块圈划模块 - 详细任务拆解 (架构重构版)

## 🎯 架构设计概述

### **核心设计原则**
1. **基于GeoJSON标准**：所有几何数据使用GeoJSON格式，确保标准化和互操作性
2. **支持复杂几何体**：地块支持多多边形+孔洞结构
3. **统一交互模式**：绘制外边界、孔洞、新多边形使用相同的交互逻辑
4. **明确的状态管理**：清晰的状态机设计，支持绘制、查看、编辑模式
5. **临时数据清理**：未显式保存的内容可以完全清理

### **数据模型架构**
```
地图要素 (MapFeature)
├── 地块 (LandPlot) - MultiPolygon GeoJSON
├── 路径 (Path) - LineString GeoJSON  
└── 地标 (Landmark) - Point GeoJSON

地块结构：
├── 多边形1 (Polygon + Holes)
│   ├── 外边界 (Exterior Ring)
│   └── 孔洞1,2,3... (Interior Rings)
├── 多边形2 (Polygon + Holes)
└── ...
```

### **交互流程设计**
```
浏览模式 (BROWSE)
├── 点击绘制工具 → 绘制模式 (DRAWING)
├── 点击已有地块 → 查看模式 (VIEWING)
└── 长按已有地块 → 编辑模式 (EDITING)

绘制模式 (DRAWING)
├── 绘制第一个多边形外边界
├── [添加新多边形] 或 [添加孔洞] 
├── 实时顶点调整（拖拽、中点插入）
└── [完成地块] → 保存对话框 → 浏览模式

查看模式 (VIEWING)
├── 显示地块信息面板
└── 点击其他地方 → 浏览模式

编辑模式 (EDITING)
├── 编辑几何形状（同绘制模式交互）
├── [保存] → 更新对话框 → 浏览模式
└── [取消] → 浏览模式
```

---

## 🔧 Task 3.1: 数据模型重构

### 3.1.1 设计GeoJSON数据结构 ✅ 已完成
**工时**: 2小时 (预计7小时)  
**负责人**: 主开发  
**依赖**: 无
**完成时间**: 当前

**实际完成步骤**:
- [x] 创建LandPlotFeature完整数据模型 (支持MultiPolygon+孔洞)
- [x] 设计LandPlotInProgress临时绘制数据结构
- [x] 实现GeometryTypes抽象基类 (为Point/LineString扩展预留)
- [x] 完成GeoJsonUtils工具类 (序列化+MapLibre集成)
- [x] 实现面积/周长地理计算算法
- [x] 添加完整的GeoJSON验证机制

**已创建的文件**:
- `app/src/main/java/cn/agrolinking/wmst/domain/geojson/LandPlotFeature.kt` ✅
- `app/src/main/java/cn/agrolinking/wmst/domain/drawing/LandPlotInProgress.kt` ✅
- `app/src/main/java/cn/agrolinking/wmst/domain/geojson/GeometryTypes.kt` ✅
- `app/src/main/java/cn/agrolinking/wmst/domain/geojson/GeoJsonUtils.kt` ✅

**关键成果**:
- 完全符合GeoJSON RFC 7946标准
- 充分利用MapLibre GeoJsonSource内置能力
- 支持复杂几何体 (多多边形+孔洞)
- 双向MapLibre Feature转换
- 为路径/地标扩展预留接口

**验收标准**:
- [x] 支持MultiPolygon几何体
- [x] 孔洞功能完整实现
- [x] GeoJSON序列化/反序列化正常
- [x] MapLibre集成接口完整
- [x] 代码编译通过，仅3个警告

### 3.1.2 数据库实体映射
**工时**: 3小时  
**负责人**: 主开发  
**依赖**: 3.1.1 完成
**状态**: ✅ 已完成

**具体步骤**:
1. [x] 创建LandPlotEntity Room实体
2. [x] 设计GeoJSON字符串存储策略
3. [x] 实现领域模型转换方法 (GeoJSON <-> Entity)
4. [x] 创建LandPlotDao数据访问对象
5. [x] 添加数据库版本升级支持
6. [x] 创建LandPlotRepository数据仓库层

**已创建的文件**:
- `app/src/main/java/cn/agrolinking/wmst/database/entity/LandPlotEntity.kt` ✅
- `app/src/main/java/cn/agrolinking/wmst/database/dao/LandPlotDao.kt` ✅  
- `app/src/main/java/cn/agrolinking/wmst/repository/LandPlotRepository.kt` ✅
- 更新 `app/src/main/java/cn/agrolinking/wmst/database/Room.kt` ✅
- 更新 `app/src/main/java/cn/agrolinking/wmst/di/DatabaseModule.kt` ✅

**验收标准**:
- [x] Room实体正确定义 - 使用GeoJSON字符串存储，支持索引和软删除
- [x] GeoJSON存储/读取正常 - 通过Entity的转换方法实现
- [x] 数据库操作CRUD完整 - 包含完整CRUD、统计、搜索、事务操作
- [x] 数据转换工作正常 - LandPlotFeature与LandPlotEntity双向转换
- [x] 编译测试通过 - 所有代码编译成功，仅有预期警告

---

## 🔧 Task 3.2: 状态管理重构

### 3.2.1 设计复杂几何体状态机
**工时**: 5小时  
**负责人**: 主开发  
**依赖**: 3.1 完成
**状态**: ✅ 已完成

**具体步骤**:
1. [x] 重构DrawingState支持复杂几何体
2. [x] 实现状态转换逻辑
3. [x] 添加状态验证机制
4. [x] 更新MapViewModel状态管理

**已更新的文件**:
- ✅ `app/src/main/java/cn/agrolinking/wmst/ui/screens/map/MapViewModel.kt` - 完全重构状态管理
- ✅ `app/src/main/java/cn/agrolinking/wmst/ui/screens/map/MapUiState.kt` - 新增统一状态架构

**代码实现**:
```kotlin
data class DrawingState(
    val mode: DrawingMode = DrawingMode.NONE,
    val workingLandPlot: LandPlotInProgress? = null,
    val selectedLandPlotId: String? = null,
    val isActive: Boolean = false,
    val isDragMode: Boolean = false,
    val showMidpoints: Boolean = false,
    val canUndo: Boolean = false,
    val canClear: Boolean = false
) {
    // 当前正在绘制的多边形
    val currentPolygon: PolygonInProgress?
        get() = workingLandPlot?.polygons?.getOrNull(workingLandPlot.activePolygonIndex)
    
    // 当前正在绘制的环（外边界或孔洞）
    val currentRing: List<LatLng>
        get() = when (workingLandPlot?.activeRingIndex) {
            0 -> currentPolygon?.exterior ?: emptyList()
            else -> currentPolygon?.holes?.getOrNull((workingLandPlot?.activeRingIndex ?: 1) - 1) ?: emptyList()
        }
    
    // 状态指示文本
    val statusText: String
        get() = workingLandPlot?.let { plot ->
            val polygonIndex = plot.activePolygonIndex + 1
            val ringType = if (plot.activeRingIndex == 0) "外边界" else "孔洞${plot.activeRingIndex}"
            "正在绘制：地块 > 多边形$polygonIndex > $ringType"
        } ?: ""
}

enum class DrawingMode {
    NONE,           // 浏览模式
    DRAWING,        // 绘制模式
    VIEWING,        // 查看模式
    EDITING         // 编辑模式
}

class MapViewModel @Inject constructor() : ViewModel() {
    
    // 开始绘制新地块
    fun startDrawingLandPlot() {
        val newPlot = LandPlotInProgress(
            polygons = listOf(PolygonInProgress(
                exterior = emptyList(),
                holes = emptyList()
            )),
            activePolygonIndex = 0,
            activeRingIndex = 0
        )
        
        _uiState.value = _uiState.value.copy(
            currentTool = MapTool.DRAW,
            drawingState = _uiState.value.drawingState.copy(
                mode = DrawingMode.DRAWING,
                workingLandPlot = newPlot,
                isActive = true,
                canUndo = false,
                canClear = false
            )
        )
    }
    
    // 添加新多边形到当前地块
    fun addNewPolygonToCurrentPlot() {
        val currentState = _uiState.value.drawingState
        val currentPlot = currentState.workingLandPlot ?: return
        
        val newPolygon = PolygonInProgress(
            exterior = emptyList(),
            holes = emptyList()
        )
        
        val updatedPlot = currentPlot.copy(
            polygons = currentPlot.polygons + newPolygon,
            activePolygonIndex = currentPlot.polygons.size,
            activeRingIndex = 0
        )
        
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                workingLandPlot = updatedPlot
            )
        )
    }
    
    // 开始绘制孔洞
    fun startDrawingHole() {
        val currentState = _uiState.value.drawingState
        val currentPlot = currentState.workingLandPlot ?: return
        val currentPolygon = currentPlot.polygons[currentPlot.activePolygonIndex]
        
        // 确保当前多边形的外边界已完成
        if (currentPolygon.exterior.size < 3) {
            Log.w("MapViewModel", "外边界未完成，无法添加孔洞")
            return
        }
        
        val updatedPolygon = currentPolygon.copy(
            holes = currentPolygon.holes + emptyList()
        )
        
        val updatedPolygons = currentPlot.polygons.toMutableList()
        updatedPolygons[currentPlot.activePolygonIndex] = updatedPolygon
        
        val updatedPlot = currentPlot.copy(
            polygons = updatedPolygons,
            activeRingIndex = currentPolygon.holes.size + 1
        )
        
        _uiState.value = _uiState.value.copy(
            drawingState = currentState.copy(
                workingLandPlot = updatedPlot
            )
        )
    }
    
    // 完成当前地块绘制
    fun completeCurrentLandPlot() {
        val currentState = _uiState.value.drawingState
        val currentPlot = currentState.workingLandPlot ?: return
        
        // 验证地块数据完整性
        if (!isLandPlotValid(currentPlot)) {
            Log.w("MapViewModel", "地块数据不完整，无法完成")
            return
        }
        
        // 触发保存对话框
        // TODO: 显示保存对话框
        Log.d("MapViewModel", "✅ 地块绘制完成，准备保存")
    }
    
    private fun isLandPlotValid(plot: LandPlotInProgress): Boolean {
        return plot.polygons.any { polygon ->
            polygon.exterior.size >= 3
        }
    }
}
```

**验收标准**:
- [x] 状态机逻辑清晰正确 - 实现了BROWSING/DRAWING/VIEWING/EDITING四种操作模式
- [x] 支持多多边形+孔洞状态管理 - 与LandPlotInProgress完美集成
- [x] 状态转换安全可靠 - 通过MapEvent统一事件系统管理
- [x] 状态指示信息准确 - 提供丰富的状态计算属性

**实际成果**:
- 创建了完整的状态管理架构：DrawingState, ViewingState, EditingState
- 实现了统一的MapEvent事件系统，支持所有绘制、编辑、查看操作
- 支持复杂几何体状态管理，包括多多边形、孔洞、顶点拖拽
- 提供向后兼容性，保证现有MapScreen正常工作
- 编译成功，仅有预期的unchecked cast警告

### 3.2.2 实现统一的交互处理 ✅ 已完成
**工时**: 6小时 (预计6小时，实际4小时)  
**负责人**: 主开发  
**依赖**: 3.2.1 完成
**完成时间**: 当前

**实际完成步骤**:
1. [x] 重构MapView参数接口 - 简化为统一状态驱动
2. [x] 创建MapEventDispatcher统一事件分发器  
3. [x] 扩展MapEvent支持兼容性事件类型
4. [x] 完善MapViewModel统一事件处理

**已更新的文件**:
- ✅ `app/src/main/java/cn/agrolinking/wmst/ui/screens/map/components/MapView.kt` - 参数接口重构
- ✅ `app/src/main/java/cn/agrolinking/wmst/ui/screens/map/MapUiState.kt` - 扩展MapEvent定义
- ✅ `app/src/main/java/cn/agrolinking/wmst/ui/screens/map/MapViewModel.kt` - 完善事件处理

**关键成果**:
- 统一状态接口：mapUiState + onMapEvent 替代15个分散回调
- 向后兼容性：现有代码无需修改即可正常运行
- 类型安全：通过MapEvent密封类避免事件类型错误
- 编译验证通过：BUILD SUCCESSFUL，无错误

**验收标准**:
- [x] 新旧接口完美兼容 - MapEventDispatcher桥接机制
- [x] 统一事件处理完整 - 支持所有15种事件类型
- [x] 状态驱动架构清晰 - mapUiState统一状态管理
- [x] 代码质量良好 - 编译通过，注释完善

---

## 🔧 Task 3.3: UI组件重构

### 3.3.1 设计复杂操作栏
**工时**: 4小时  
**负责人**: 主开发  
**依赖**: 3.2 完成

**具体步骤**:
1. [ ] 更新DrawingOperationBar支持新按钮
2. [ ] 添加状态指示器组件
3. [ ] 实现动态按钮启用/禁用逻辑
4. [ ] 优化操作栏布局

**需要更新的文件**:
- `app/src/main/java/cn/agrolinking/wmst/ui/screens/map/components/DrawingOperationBar.kt`

**代码实现**:
```kotlin
@Composable
fun DrawingOperationBar(
    drawingState: DrawingState,
    onUndo: () -> Unit,
    onClear: () -> Unit,
    onAddNewPolygon: () -> Unit,
    onAddHole: () -> Unit,
    onCompletePlot: () -> Unit,
    onClose: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface)
            .padding(16.dp)
    ) {
        // 状态指示器
        if (drawingState.statusText.isNotEmpty()) {
            Text(
                text = drawingState.statusText,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：基础操作
            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                IconButton(
                    onClick = onUndo,
                    enabled = drawingState.canUndo
                ) {
                    Icon(Icons.Default.Undo, contentDescription = "撤销")
                }
                
                IconButton(
                    onClick = onClear,
                    enabled = drawingState.canClear
                ) {
                    Icon(Icons.Default.Clear, contentDescription = "清除")
                }
            }
            
            // 中间：几何操作
            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                Button(
                    onClick = onAddNewPolygon,
                    enabled = canAddNewPolygon(drawingState),
                    modifier = Modifier.height(36.dp)
                ) {
                    Text("添加多边形", fontSize = 12.sp)
                }
                
                Button(
                    onClick = onAddHole,
                    enabled = canAddHole(drawingState),
                    modifier = Modifier.height(36.dp)
                ) {
                    Text("添加孔洞", fontSize = 12.sp)
                }
            }
            
            // 右侧：完成操作
            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                TextButton(onClick = onClose) {
                    Text("取消", color = MaterialTheme.colorScheme.error)
                }
                
                Button(
                    onClick = onCompletePlot,
                    enabled = canCompletePlot(drawingState)
                ) {
                    Text("完成地块")
                }
            }
        }
    }
}

private fun canAddNewPolygon(state: DrawingState): Boolean {
    val currentPolygon = state.currentPolygon ?: return false
    return currentPolygon.exterior.size >= 3
}

private fun canAddHole(state: DrawingState): Boolean {
    val currentPolygon = state.currentPolygon ?: return false
    return currentPolygon.exterior.size >= 3 && state.workingLandPlot?.activeRingIndex == 0
}

private fun canCompletePlot(state: DrawingState): Boolean {
    return state.workingLandPlot?.polygons?.any { it.exterior.size >= 3 } == true
}
```

**验收标准**:
- [ ] 操作栏布局美观合理
- [ ] 按钮启用/禁用逻辑正确
- [ ] 状态指示器信息准确
- [ ] 交互反馈及时

### 3.3.2 创建地块保存对话框
**工时**: 4小时  
**负责人**: 主开发  
**依赖**: 3.3.1 完成

**具体步骤**:
1. [ ] 设计地块信息输入对话框
2. [ ] 添加表单验证逻辑
3. [ ] 实现自动面积计算显示
4. [ ] 集成保存功能

**需要创建的文件**:
- `app/src/main/java/cn/agrolinking/wmst/ui/screens/map/components/LandPlotSaveDialog.kt`

**验收标准**:
- [ ] 对话框UI美观易用
- [ ] 表单验证完善
- [ ] 面积计算准确
- [ ] 保存流程流畅

---

## 🔧 Task 3.4: 地理计算引擎

### 3.4.1 实现GeoJSON几何计算
**工时**: 5小时  
**负责人**: 主开发  
**依赖**: 3.3 完成

**具体步骤**:
1. [ ] 实现MultiPolygon面积计算
2. [ ] 实现含孔洞多边形面积计算
3. [ ] 实现周长计算
4. [ ] 添加几何验证功能

**需要创建的文件**:
- `app/src/main/java/cn/agrolinking/wmst/map/calculation/GeoJsonCalculator.kt`

**代码实现**:
```kotlin
class GeoJsonCalculator {
    
    /**
     * 计算MultiPolygon面积（支持孔洞）
     */
    fun calculateMultiPolygonArea(geometry: MultiPolygonGeometry): Double {
        var totalArea = 0.0
        
        geometry.coordinates.forEach { polygon ->
            if (polygon.isNotEmpty()) {
                // 外边界面积（正值）
                val exteriorArea = calculatePolygonArea(polygon[0])
                totalArea += exteriorArea
                
                // 减去孔洞面积
                for (i in 1 until polygon.size) {
                    val holeArea = calculatePolygonArea(polygon[i])
                    totalArea -= holeArea
                }
            }
        }
        
        return Math.abs(totalArea)
    }
    
    /**
     * 计算单个多边形面积（球面）
     */
    private fun calculatePolygonArea(ring: List<List<Double>>): Double {
        if (ring.size < 3) return 0.0
        
        val coordinates = ring.map { 
            LatLng(it[1], it[0]) // GeoJSON是[lng, lat]，LatLng是(lat, lng)
        }
        
        return GeometryUtils.calculateSphericalArea(coordinates)
    }
    
    /**
     * 验证MultiPolygon几何合法性
     */
    fun validateMultiPolygon(geometry: MultiPolygonGeometry): ValidationResult {
        val errors = mutableListOf<String>()
        
        geometry.coordinates.forEachIndexed { polygonIndex, polygon ->
            if (polygon.isEmpty()) {
                errors.add("多边形$polygonIndex 为空")
                return@forEachIndexed
            }
            
            // 验证外边界
            val exterior = polygon[0]
            if (exterior.size < 4) { // GeoJSON要求首尾重复
                errors.add("多边形$polygonIndex 外边界顶点不足")
            }
            
            if (!isRingClosed(exterior)) {
                errors.add("多边形$polygonIndex 外边界未闭合")
            }
            
            // 验证孔洞
            for (holeIndex in 1 until polygon.size) {
                val hole = polygon[holeIndex]
                if (hole.size < 4) {
                    errors.add("多边形$polygonIndex 孔洞$holeIndex 顶点不足")
                }
                
                if (!isRingClosed(hole)) {
                    errors.add("多边形$polygonIndex 孔洞$holeIndex 未闭合")
                }
            }
        }
        
        return ValidationResult(errors.isEmpty(), errors)
    }
    
    private fun isRingClosed(ring: List<List<Double>>): Boolean {
        if (ring.size < 2) return false
        val first = ring.first()
        val last = ring.last()
        return Math.abs(first[0] - last[0]) < 1e-10 && Math.abs(first[1] - last[1]) < 1e-10
    }
}

data class ValidationResult(
    val isValid: Boolean,
    val errors: List<String>
)
```

**验收标准**:
- [ ] MultiPolygon面积计算准确
- [ ] 孔洞面积正确减除
- [ ] 几何验证逻辑完善
- [ ] 计算性能良好

---

## 🔧 Task 3.5: 地块查看和编辑

### 3.5.1 实现地块选择和查看
**工时**: 4小时  
**负责人**: 主开发  
**依赖**: 3.4 完成

**具体步骤**:
1. [ ] 实现地块点击检测
2. [ ] 创建地块信息面板
3. [ ] 添加地块高亮显示
4. [ ] 实现地块详情查看

**需要创建的文件**:
- `app/src/main/java/cn/agrolinking/wmst/ui/screens/map/components/LandPlotInfoPanel.kt`

**验收标准**:
- [ ] 地块点击检测准确
- [ ] 信息面板显示完整
- [ ] 高亮效果清晰
- [ ] 交互体验流畅

### 3.5.2 实现地块编辑功能
**工时**: 5小时  
**负责人**: 主开发  
**依赖**: 3.5.1 完成

**具体步骤**:
1. [ ] 实现长按进入编辑模式
2. [ ] 复用绘制模式的编辑逻辑
3. [ ] 添加编辑历史备份
4. [ ] 实现编辑保存/取消

**验收标准**:
- [ ] 长按触发编辑正确
- [ ] 编辑交互与绘制一致
- [ ] 支持取消编辑恢复
- [ ] 保存更新正常

---

## 🔧 Task 3.6: 扩展性设计

### 3.6.1 抽象几何类型处理
**工时**: 3小时  
**负责人**: 主开发  
**依赖**: 3.5 完成

**具体步骤**:
1. [ ] 设计通用几何类型接口
2. [ ] 为路径和地标预留扩展点
3. [ ] 抽象通用的绘制/编辑逻辑
4. [ ] 设计可扩展的渲染系统

**需要创建的文件**:
- `app/src/main/java/cn/agrolinking/wmst/domain/geojson/GeometryFeature.kt`
- `app/src/main/java/cn/agrolinking/wmst/map/interaction/GeometryEditor.kt`

**验收标准**:
- [ ] 接口设计合理可扩展
- [ ] 为其他几何类型预留空间
- [ ] 代码结构清晰
- [ ] 扩展成本可控

---

## 🔧 Task 3.7: 扩展绘制模式 (后续任务)

### 3.7.1 实现GPS轨迹圈划
**工时**: 8小时  
**负责人**: 主开发  
**依赖**: 3.6 完成
**优先级**: 中等

**具体步骤**:
1. [ ] 创建GPS轨迹记录器
2. [ ] 实现轨迹点过滤算法 (Douglas-Peucker简化)
3. [ ] 添加轨迹实时显示
4. [ ] 集成到GeoJSON数据模型
5. [ ] 实现轨迹自动闭合为多边形

**需要创建的文件**:
- `app/src/main/java/cn/agrolinking/wmst/map/drawing/GpsTrackingHandler.kt`
- `app/src/main/java/cn/agrolinking/wmst/map/util/TrackingUtils.kt`

**技术要点**:
- GPS精度过滤 (≤10m精度)
- 距离过滤 (相邻点>5m)
- 轨迹简化算法减少顶点数量
- 支持暂停/继续记录

**验收标准**:
- [ ] 能正确记录GPS轨迹
- [ ] 轨迹点过滤算法有效
- [ ] 轨迹实时显示流畅
- [ ] 能自动生成合理的地块边界

### 3.7.2 实现几何图形快速绘制
**工时**: 6小时  
**负责人**: 主开发  
**依赖**: 3.7.1 完成
**优先级**: 低

**具体步骤**:

**矩形绘制 (3小时)**:
1. [ ] 实现两点确定矩形
2. [ ] 添加矩形实时预览
3. [ ] 支持矩形旋转调整
4. [ ] 转换为GeoJSON多边形

**圆形绘制 (3小时)**:
1. [ ] 实现中心点+半径绘制
2. [ ] 添加圆形实时预览
3. [ ] 支持半径拖拽调整
4. [ ] 圆形转多边形近似 (36边形)

**需要创建的文件**:
- `app/src/main/java/cn/agrolinking/wmst/map/drawing/RectangleDrawingHandler.kt`
- `app/src/main/java/cn/agrolinking/wmst/map/drawing/CircleDrawingHandler.kt`

**技术要点**:
- 矩形支持任意角度旋转
- 圆形转多边形保持视觉平滑
- 集成到统一的绘制状态机
- 支持转换后的拖拽编辑

**验收标准**:
- [ ] 矩形：两点绘制，支持旋转，显示尺寸
- [ ] 圆形：中心+半径绘制，显示半径数值
- [ ] 几何图形与手工绘制无缝集成
- [ ] 转换后支持相同的编辑功能

### 3.7.3 绘制模式管理器
**工时**: 4小时  
**负责人**: 主开发  
**依赖**: 3.7.2 完成
**优先级**: 低

**具体步骤**:
1. [ ] 创建统一的绘制模式管理器
2. [ ] 实现模式切换UI组件
3. [ ] 集成所有绘制处理器
4. [ ] 添加模式状态指示

**需要创建的文件**:
- `app/src/main/java/cn/agrolinking/wmst/map/drawing/DrawingModeManager.kt`
- `app/src/main/java/cn/agrolinking/wmst/ui/screens/map/components/DrawingModeSelector.kt`

**代码实现**:
```kotlin
enum class DrawingMode {
    NONE,               // 浏览模式
    MANUAL_POLYGON,     // 手工点击绘制
    GPS_TRACKING,       // GPS轨迹绘制
    RECTANGLE,          // 矩形绘制
    CIRCLE,             // 圆形绘制
    EDITING             // 编辑模式
}

class DrawingModeManager {
    fun setDrawingMode(mode: DrawingMode, context: DrawingContext) {
        // 清理当前绘制状态
        currentHandler?.cleanup()
        
        currentHandler = when (mode) {
            DrawingMode.MANUAL_POLYGON -> ManualDrawingHandler(context)
            DrawingMode.GPS_TRACKING -> GpsTrackingHandler(context)
            DrawingMode.RECTANGLE -> RectangleDrawingHandler(context)
            DrawingMode.CIRCLE -> CircleDrawingHandler(context)
            DrawingMode.EDITING -> EditingHandler(context)
            DrawingMode.NONE -> null
        }
        
        notifyModeChanged(mode)
    }
}
```

**验收标准**:
- [ ] 模式切换流畅无冲突
- [ ] UI指示清晰准确
- [ ] 所有模式集成完善
- [ ] 状态管理统一

---

## ✅ Phase 3 验收清单

### 功能验收
- [ ] 支持复杂地块绘制（多多边形+孔洞）
- [ ] 统一的交互模式（外边界、孔洞一致）
- [ ] 通过按钮添加新多边形和孔洞
- [ ] 完整的地块保存流程
- [ ] 地块查看和编辑功能
- [ ] 基于GeoJSON的数据标准化

### 架构验收
- [ ] 状态管理清晰可维护
- [ ] 数据模型标准化（GeoJSON）
- [ ] 代码结构支持扩展
- [ ] 交互逻辑统一一致

### 性能验收
- [ ] 复杂几何体渲染流畅
- [ ] 大量地块加载性能良好
- [ ] 内存使用合理
- [ ] 几何计算准确高效

### 用户体验验收
- [ ] 操作流程直观易懂
- [ ] 状态指示清晰准确
- [ ] 错误处理友好
- [ ] 临时数据清理彻底

---

## 📊 实施计划

### 核心功能工时估算 (Phase 3.1-3.6)
- **数据模型重构**: 7小时 - ✅ 已完成 (5小时实际，比预期快2小时)
- **状态管理重构**: 11小时 - ✅ 已完成 (Task 3.2.1: 4小时实际, Task 3.2.2: 4小时实际)
- **UI组件重构**: 8小时
- **地理计算引擎**: 5小时
- **地块查看编辑**: 9小时
- **扩展性设计**: 3小时
- **测试和优化**: 8小时

**核心功能小计**: 51小时 (约6.5个工作日)
**已完成**: 13小时，剩余38小时

### 扩展绘制模式工时估算 (Phase 3.7 - 后续任务)
- **GPS轨迹圈划**: 8小时
- **几何图形绘制**: 6小时
- **绘制模式管理器**: 4小时
- **集成测试**: 4小时

**扩展功能小计**: 22小时 (约3个工作日)

**总计**: 73小时 (约9.5个工作日)

### 里程碑计划

#### Phase 3A: 核心功能 (优先级: 高)
- **Week 1**: ✅ 数据模型重构已完成，✅ 状态管理重构已完成
- **Week 2**: 完成UI组件和计算引擎  
- **Week 3**: 完成查看编辑和扩展性设计
- **Week 4**: 测试、优化和文档

#### Phase 3B: 扩展绘制模式 (优先级: 中-低)
- **Week 5**: GPS轨迹圈划实现
- **Week 6**: 几何图形绘制和模式管理器
- **Week 7**: 集成测试和文档完善

### 分阶段交付策略
1. **MVP版本**: 完成Task 3.1-3.6，支持手工绘制复杂地块
2. **增强版本**: 完成Task 3.7，支持多种绘制模式

这个分阶段的任务拆解确保了核心功能优先实现，扩展功能可以根据实际需求和时间安排灵活调整。 