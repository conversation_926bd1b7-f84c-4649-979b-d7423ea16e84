# Phase 4: 标注管理模块详细任务

## 🔍 技术调研结果总结 ✅

### MapLibre 标注功能技术选型

基于Phase 3的调研结果，Phase 4标注管理模块将采用以下技术方案：

| 技术组件 | 选型方案 | 实现方式 | 说明 |
|---------|---------|---------|------|
| **标注显示** | MapLibre Annotation Plugin | SymbolManager | 地图标记点显示 |
| **标注管理** | 自定义AnnotationManager | 结合SymbolManager | 8种标注类型管理 |
| **UI交互** | Jetpack Compose | 自定义UI组件 | 创建/编辑/删除界面 |
| **数据存储** | Room Database | 本地持久化 | 标注和附件数据 |

### 🎯 核心依赖 (与Phase 3一致)
```gradle
dependencies {
    // 核心SDK
    implementation 'org.maplibre.gl:android-sdk:11.10.3'
    
    // 标注功能必需插件
    implementation 'org.maplibre.gl:android-plugin-annotation-v9:3.0.2'  // SymbolManager
    implementation 'org.maplibre.gl:android-plugin-scalebar-v9:3.0.2'     // 比例尺UI
}
```

### 🚧 实现策略
- **标注显示**: 使用SymbolManager管理地图标记
- **标注类型**: 支持8种预定义标注类型 + 自定义
- **交互方式**: 长按地图创建，点击标注查看详情
- **UI策略**: 全部使用Jetpack Compose自定义UI
- **数据管理**: Room数据库 + 附件文件系统

---

## 阶段概述
- **目标**: 实现地图标注功能，支持8种标注类型和附件管理
- **时间**: 第7-8周
- **前置条件**: Phase 3地块圈划模块完成
- **验收标准**: 标注创建、编辑、删除、附件管理功能完整

## 4.1 标注数据模型设计

### 4.1.1 MapAnnotation实体类
**任务**: 创建标注数据模型
**文件**: `app/src/main/java/cn/agrolinking/wmst/database/entity/MapAnnotation.kt`

```kotlin
@Entity(tableName = "map_annotations")
data class MapAnnotation(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val atlasId: String,
    val type: AnnotationType,
    val title: String,
    val description: String?,
    val latitude: Double,
    val longitude: Double,
    val iconType: String,
    val color: String,
    val attachments: List<String> = emptyList(),
    val metadata: Map<String, Any> = emptyMap(),
    val createdBy: String,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

enum class AnnotationType {
    POINT_OF_INTEREST,    // 兴趣点
    FACILITY,             // 设施
    HAZARD,              // 危险点
    OBSERVATION,         // 观察点
    MEASUREMENT,         // 测量点
    PHOTO_POINT,         // 拍照点
    SAMPLE_POINT,        // 采样点
    CUSTOM               // 自定义
}
```

### 4.1.2 AnnotationAttachment实体类
**任务**: 创建附件数据模型
**文件**: `app/src/main/java/cn/agrolinking/wmst/database/entity/AnnotationAttachment.kt`

```kotlin
@Entity(tableName = "annotation_attachments")
data class AnnotationAttachment(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val annotationId: String,
    val type: AttachmentType,
    val fileName: String,
    val filePath: String,
    val fileSize: Long,
    val mimeType: String,
    val uploadedAt: Long = System.currentTimeMillis()
)

enum class AttachmentType {
    PHOTO, VIDEO, AUDIO, DOCUMENT
}
```

### 4.1.3 数据库DAO接口
**任务**: 创建标注数据访问接口
**文件**: `app/src/main/java/cn/agrolinking/wmst/database/dao/MapAnnotationDao.kt`

```kotlin
@Dao
interface MapAnnotationDao {
    @Query("SELECT * FROM map_annotations WHERE atlasId = :atlasId")
    suspend fun getAnnotationsByAtlas(atlasId: String): List<MapAnnotation>
    
    @Query("SELECT * FROM map_annotations WHERE type = :type")
    suspend fun getAnnotationsByType(type: AnnotationType): List<MapAnnotation>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAnnotation(annotation: MapAnnotation)
    
    @Update
    suspend fun updateAnnotation(annotation: MapAnnotation)
    
    @Delete
    suspend fun deleteAnnotation(annotation: MapAnnotation)
    
    @Query("DELETE FROM map_annotations WHERE id = :id")
    suspend fun deleteAnnotationById(id: String)
}
```

## 4.2 标注管理器核心实现

### 4.2.1 MapAnnotationManager
**任务**: 创建标注管理核心类
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/MapAnnotationManager.kt`

```kotlin
class MapAnnotationManager(
    private val mapView: MapView,
    private val mapLibreMap: MapLibreMap,
    private val style: Style,
    private val annotationDao: MapAnnotationDao,
    private val attachmentManager: AttachmentManager
) {
    private val annotations = mutableMapOf<String, MapAnnotation>()
    private var symbolManager: SymbolManager? = null
    private val annotationSymbols = mutableMapOf<String, Symbol>()
    
    init {
        setupSymbolManager()
    }
    
    private fun setupSymbolManager() {
        // 🎯 使用 MapLibre Annotation Plugin 的 SymbolManager
        symbolManager = SymbolManager(mapView, mapLibreMap, style).apply {
            // 配置标注样式
            iconAllowOverlap = true
            iconIgnorePlacement = true
            
            // 设置点击监听
            addClickListener { symbol ->
                val annotation = findAnnotationBySymbol(symbol)
                annotation?.let { onAnnotationClicked?.invoke(it) }
                true
            }
        }
    }
    
    suspend fun loadAnnotations(atlasId: String) {
        val annotationList = annotationDao.getAnnotationsByAtlas(atlasId)
        annotations.clear()
        annotationSymbols.clear()
        
        annotationList.forEach { annotation ->
            annotations[annotation.id] = annotation
            addAnnotationToMap(annotation)
        }
    }
    
    suspend fun createAnnotation(
        atlasId: String,
        type: AnnotationType,
        latLng: LatLng,
        title: String,
        description: String? = null
    ): MapAnnotation {
        val annotation = MapAnnotation(
            atlasId = atlasId,
            type = type,
            title = title,
            description = description,
            latitude = latLng.latitude,
            longitude = latLng.longitude,
            iconType = getDefaultIcon(type),
            color = getDefaultColor(type),
            createdBy = getCurrentUserId()
        )
        
        annotationDao.insertAnnotation(annotation)
        annotations[annotation.id] = annotation
        addAnnotationToMap(annotation)
        
        return annotation
    }
    
    private fun addAnnotationToMap(annotation: MapAnnotation) {
        // 🎯 使用 SymbolManager 添加标注到地图
        val symbolOptions = SymbolOptions()
            .withLatLng(LatLng(annotation.latitude, annotation.longitude))
            .withIconImage(annotation.iconType)
            .withIconSize(1.0f)
            .withTextField(annotation.title)
            .withTextColor(annotation.color)
            .withTextSize(12.0f)
            .withTextOffset(arrayOf(0.0f, 1.5f))
            .withData(JsonPrimitive(annotation.id))
        
        val symbol = symbolManager?.create(symbolOptions)
        symbol?.let { 
            annotationSymbols[annotation.id] = it 
        }
    }
    
    suspend fun deleteAnnotation(annotationId: String) {
        annotations[annotationId]?.let { annotation ->
            // 从地图移除
            annotationSymbols[annotationId]?.let { symbol ->
                symbolManager?.delete(symbol)
                annotationSymbols.remove(annotationId)
            }
            
            // 从数据库删除
            annotationDao.deleteAnnotationById(annotationId)
            annotations.remove(annotationId)
        }
    }
    
    private fun findAnnotationBySymbol(symbol: Symbol): MapAnnotation? {
        val annotationId = symbol.data?.asString
        return annotationId?.let { annotations[it] }
    }
    
    fun cleanup() {
        symbolManager?.onDestroy()
        annotations.clear()
        annotationSymbols.clear()
    }
    
    var onAnnotationClicked: ((MapAnnotation) -> Unit)? = null
}
```

### 4.2.2 AttachmentManager
**任务**: 创建附件管理器
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/AttachmentManager.kt`

```kotlin
class AttachmentManager(
    private val context: Context,
    private val attachmentDao: AnnotationAttachmentDao
) {
    private val storageDir = File(context.filesDir, "attachments")
    
    init {
        if (!storageDir.exists()) {
            storageDir.mkdirs()
        }
    }
    
    suspend fun addPhotoAttachment(
        annotationId: String,
        photoUri: Uri
    ): AnnotationAttachment {
        val fileName = "photo_${System.currentTimeMillis()}.jpg"
        val targetFile = File(storageDir, fileName)
        
        // 复制文件到应用目录
        context.contentResolver.openInputStream(photoUri)?.use { input ->
            targetFile.outputStream().use { output ->
                input.copyTo(output)
            }
        }
        
        val attachment = AnnotationAttachment(
            annotationId = annotationId,
            type = AttachmentType.PHOTO,
            fileName = fileName,
            filePath = targetFile.absolutePath,
            fileSize = targetFile.length(),
            mimeType = "image/jpeg"
        )
        
        attachmentDao.insertAttachment(attachment)
        return attachment
    }
    
    suspend fun getAttachments(annotationId: String): List<AnnotationAttachment> {
        return attachmentDao.getAttachmentsByAnnotation(annotationId)
    }
}
```

## 4.3 标注UI组件

### 4.3.1 AnnotationCreationDialog
**任务**: 创建标注创建对话框
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/AnnotationCreationDialog.kt`

```kotlin
@Composable
fun AnnotationCreationDialog(
    isVisible: Boolean,
    annotationType: AnnotationType,
    onDismiss: () -> Unit,
    onConfirm: (title: String, description: String) -> Unit
) {
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    
    if (isVisible) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text("创建${getAnnotationTypeName(annotationType)}") },
            text = {
                Column {
                    OutlinedTextField(
                        value = title,
                        onValueChange = { title = it },
                        label = { Text("标题") },
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    OutlinedTextField(
                        value = description,
                        onValueChange = { description = it },
                        label = { Text("描述（可选）") },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 3
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { onConfirm(title, description) },
                    enabled = title.isNotBlank()
                ) {
                    Text("创建")
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text("取消")
                }
            }
        )
    }
}
```

### 4.3.2 AnnotationTypeSelector
**任务**: 创建标注类型选择器
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/AnnotationTypeSelector.kt`

```kotlin
@Composable
fun AnnotationTypeSelector(
    selectedType: AnnotationType?,
    onTypeSelected: (AnnotationType) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier.padding(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(AnnotationType.values()) { type ->
            AnnotationTypeChip(
                type = type,
                isSelected = type == selectedType,
                onClick = { onTypeSelected(type) }
            )
        }
    }
}

@Composable
private fun AnnotationTypeChip(
    type: AnnotationType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    FilterChip(
        selected = isSelected,
        onClick = onClick,
        label = { Text(getAnnotationTypeName(type)) },
        leadingIcon = {
            Icon(
                imageVector = getAnnotationTypeIcon(type),
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
        }
    )
}
```

### 4.3.3 AnnotationDetailsBottomSheet
**任务**: 创建标注详情底部弹窗
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/AnnotationDetailsBottomSheet.kt`

```kotlin
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AnnotationDetailsBottomSheet(
    annotation: MapAnnotation?,
    attachments: List<AnnotationAttachment>,
    onDismiss: () -> Unit,
    onEdit: (MapAnnotation) -> Unit,
    onDelete: (MapAnnotation) -> Unit,
    onAddAttachment: (MapAnnotation) -> Unit
) {
    val bottomSheetState = rememberModalBottomSheetState()
    
    if (annotation != null) {
        ModalBottomSheet(
            onDismissRequest = onDismiss,
            sheetState = bottomSheetState
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                // 标注基本信息
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = annotation.title,
                            style = MaterialTheme.typography.headlineSmall
                        )
                        Text(
                            text = getAnnotationTypeName(annotation.type),
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    Row {
                        IconButton(onClick = { onEdit(annotation) }) {
                            Icon(Icons.Default.Edit, contentDescription = "编辑")
                        }
                        IconButton(onClick = { onDelete(annotation) }) {
                            Icon(Icons.Default.Delete, contentDescription = "删除")
                        }
                    }
                }
                
                // 描述
                if (!annotation.description.isNullOrBlank()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = annotation.description,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                
                // 附件列表
                Spacer(modifier = Modifier.height(16.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "附件 (${attachments.size})",
                        style = MaterialTheme.typography.titleMedium
                    )
                    IconButton(onClick = { onAddAttachment(annotation) }) {
                        Icon(Icons.Default.Add, contentDescription = "添加附件")
                    }
                }
                
                LazyColumn {
                    items(attachments) { attachment ->
                        AttachmentItem(
                            attachment = attachment,
                            onClick = { /* 打开附件 */ }
                        )
                    }
                }
            }
        }
    }
}
```

## 4.4 标注交互功能

### 4.4.1 地图点击标注创建
**任务**: 实现地图长按创建标注
**文件**: 在MapActivity中添加标注创建逻辑

```kotlin
// 在MapActivity中添加
private fun setupAnnotationCreation() {
    mapView.getMapAsync { mapboxMap ->
        mapboxMap.addOnMapLongClickListener { point ->
            showAnnotationTypeSelector(point)
            true
        }
    }
}

private fun showAnnotationTypeSelector(point: Point) {
    // 显示标注类型选择器
    annotationTypeSelector.show()
    selectedPoint = point
}

private fun createAnnotationAtPoint(type: AnnotationType, point: Point) {
    lifecycleScope.launch {
        val latLng = LatLng(point.latitude(), point.longitude())
        showAnnotationCreationDialog(type, latLng)
    }
}
```

### 4.4.2 标注点击交互
**任务**: 实现标注点击显示详情
**文件**: 在MapAnnotationManager中添加点击处理

```kotlin
private fun setupAnnotationClickListener() {
    mapView.getMapAsync { mapboxMap ->
        mapboxMap.addOnMapClickListener { point ->
            val annotation = findAnnotationAtPoint(point)
            if (annotation != null) {
                showAnnotationDetails(annotation)
                true
            } else {
                false
            }
        }
    }
}

private fun findAnnotationAtPoint(point: Point): MapAnnotation? {
    // 查找点击位置附近的标注
    val clickLatLng = LatLng(point.latitude(), point.longitude())
    return annotations.values.find { annotation ->
        val annotationLatLng = LatLng(annotation.latitude, annotation.longitude)
        calculateDistance(clickLatLng, annotationLatLng) < CLICK_TOLERANCE
    }
}
```

## 4.5 附件管理功能

### 4.5.1 相机拍照功能
**任务**: 集成相机拍照
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/CameraManager.kt`

```kotlin
class CameraManager(private val activity: Activity) {
    private var photoUri: Uri? = null
    private val takePictureLauncher = activity.registerForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success ->
        if (success && photoUri != null) {
            onPhotoTaken?.invoke(photoUri!!)
        }
    }
    
    var onPhotoTaken: ((Uri) -> Unit)? = null
    
    fun takePhoto() {
        val photoFile = File(
            activity.getExternalFilesDir(Environment.DIRECTORY_PICTURES),
            "photo_${System.currentTimeMillis()}.jpg"
        )
        
        photoUri = FileProvider.getUriForFile(
            activity,
            "${activity.packageName}.fileprovider",
            photoFile
        )
        
        takePictureLauncher.launch(photoUri)
    }
}
```

### 4.5.2 文件选择功能
**任务**: 实现文件选择器
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/FilePickerManager.kt`

```kotlin
class FilePickerManager(private val activity: Activity) {
    private val pickFileLauncher = activity.registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { onFileSelected?.invoke(it) }
    }
    
    var onFileSelected: ((Uri) -> Unit)? = null
    
    fun pickImage() {
        pickFileLauncher.launch("image/*")
    }
    
    fun pickDocument() {
        pickFileLauncher.launch("*/*")
    }
}
```

## 4.6 标注样式自定义

### 4.6.1 标注图标管理
**任务**: 创建标注图标管理器
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/AnnotationIconManager.kt`

```kotlin
object AnnotationIconManager {
    private val iconMap = mapOf(
        AnnotationType.POINT_OF_INTEREST to R.drawable.ic_poi,
        AnnotationType.FACILITY to R.drawable.ic_facility,
        AnnotationType.HAZARD to R.drawable.ic_hazard,
        AnnotationType.OBSERVATION to R.drawable.ic_observation,
        AnnotationType.MEASUREMENT to R.drawable.ic_measurement,
        AnnotationType.PHOTO_POINT to R.drawable.ic_photo,
        AnnotationType.SAMPLE_POINT to R.drawable.ic_sample,
        AnnotationType.CUSTOM to R.drawable.ic_custom
    )
    
    private val colorMap = mapOf(
        AnnotationType.POINT_OF_INTEREST to "#2196F3",
        AnnotationType.FACILITY to "#4CAF50",
        AnnotationType.HAZARD to "#F44336",
        AnnotationType.OBSERVATION to "#FF9800",
        AnnotationType.MEASUREMENT to "#9C27B0",
        AnnotationType.PHOTO_POINT to "#00BCD4",
        AnnotationType.SAMPLE_POINT to "#795548",
        AnnotationType.CUSTOM to "#607D8B"
    )
    
    fun getIcon(type: AnnotationType): Int {
        return iconMap[type] ?: R.drawable.ic_custom
    }
    
    fun getColor(type: AnnotationType): String {
        return colorMap[type] ?: "#607D8B"
    }
}
```

### 4.6.2 自定义标注样式
**任务**: 实现标注样式自定义
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/AnnotationStyleCustomizer.kt`

```kotlin
@Composable
fun AnnotationStyleCustomizer(
    annotation: MapAnnotation,
    onStyleChanged: (String, String) -> Unit // iconType, color
) {
    var selectedIcon by remember { mutableStateOf(annotation.iconType) }
    var selectedColor by remember { mutableStateOf(annotation.color) }
    
    Column {
        Text("图标选择", style = MaterialTheme.typography.titleMedium)
        LazyRow {
            items(getAvailableIcons()) { icon ->
                IconButton(
                    onClick = {
                        selectedIcon = icon
                        onStyleChanged(icon, selectedColor)
                    }
                ) {
                    Icon(
                        painter = painterResource(getIconResource(icon)),
                        contentDescription = null,
                        tint = if (icon == selectedIcon) 
                            MaterialTheme.colorScheme.primary 
                        else 
                            MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text("颜色选择", style = MaterialTheme.typography.titleMedium)
        LazyRow {
            items(getAvailableColors()) { color ->
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            Color(android.graphics.Color.parseColor(color)),
                            CircleShape
                        )
                        .border(
                            2.dp,
                            if (color == selectedColor) 
                                MaterialTheme.colorScheme.primary 
                            else 
                                Color.Transparent,
                            CircleShape
                        )
                        .clickable {
                            selectedColor = color
                            onStyleChanged(selectedIcon, color)
                        }
                )
            }
        }
    }
}
```

## 4.7 数据同步与缓存

### 4.7.1 标注数据同步
**任务**: 实现标注数据同步
**文件**: `app/src/main/java/cn/agrolinking/wmst/repository/AnnotationSyncRepository.kt`

```kotlin
class AnnotationSyncRepository(
    private val localDao: MapAnnotationDao,
    private val remoteApi: AnnotationApiService,
    private val syncStateDao: SyncStateDao
) {
    suspend fun syncAnnotations(atlasId: String) {
        try {
            // 上传本地新增/修改的标注
            val localAnnotations = localDao.getUnsyncedAnnotations(atlasId)
            localAnnotations.forEach { annotation ->
                val response = if (annotation.isNew) {
                    remoteApi.createAnnotation(annotation)
                } else {
                    remoteApi.updateAnnotation(annotation.id, annotation)
                }
                
                if (response.isSuccessful) {
                    localDao.markAsSynced(annotation.id)
                }
            }
            
            // 下载服务器端更新
            val lastSyncTime = syncStateDao.getLastSyncTime(atlasId)
            val remoteAnnotations = remoteApi.getAnnotationsSince(atlasId, lastSyncTime)
            
            remoteAnnotations.forEach { annotation ->
                localDao.insertOrUpdate(annotation)
            }
            
            syncStateDao.updateLastSyncTime(atlasId, System.currentTimeMillis())
            
        } catch (e: Exception) {
            // 处理同步错误
            throw SyncException("标注同步失败", e)
        }
    }
}
```

## 4.8 测试验证

### 4.8.1 单元测试
**任务**: 创建标注管理器测试
**文件**: `app/src/test/java/cn/agrolinking/wmst/ui/components/MapAnnotationManagerTest.kt`

### 4.8.2 UI测试
**任务**: 创建标注UI测试
**文件**: `app/src/androidTest/java/cn/agrolinking/wmst/ui/AnnotationUITest.kt`

### 4.8.3 集成测试
**任务**: 创建标注功能集成测试
**文件**: `app/src/androidTest/java/cn/agrolinking/wmst/AnnotationIntegrationTest.kt`

## 验收标准

### 功能验收
- [ ] 支持8种标注类型创建
- [ ] 标注点击显示详情
- [ ] 标注编辑和删除功能
- [ ] 附件添加（拍照、选择文件）
- [ ] 标注样式自定义
- [ ] 标注数据持久化
- [ ] 标注列表查看和筛选

### 性能验收
- [ ] 1000个标注流畅显示
- [ ] 标注创建响应时间 < 500ms
- [ ] 附件上传进度显示
- [ ] 内存使用合理

### 用户体验验收
- [ ] 操作流程直观易懂
- [ ] 错误提示友好
- [ ] 离线模式正常工作
- [ ] 数据同步状态清晰

## 风险点与解决方案

### 主要风险
1. **大量标注性能问题** - 使用聚合显示和分层加载
2. **附件存储空间** - 实现附件压缩和清理机制
3. **标注精度问题** - 提供手动位置调整功能
4. **数据同步冲突** - 实现冲突检测和解决机制

### 开发建议
1. 先实现基础标注功能，再添加高级特性
2. 重点测试大数据量场景下的性能
3. 提供标注导入导出功能
4. 考虑标注权限管理需求 