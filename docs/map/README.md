# 万亩数田App - 地图模块文档

## 📖 文档概述

本目录包含万亩数田App中**地图模块**的技术文档。地图模块是整个农业管理系统中的一个重要功能模块，主要用于地块圈划、位置导航和可视化管理。

## 🌾 万亩数田App整体架构

万亩数田是一个完整的**多租户企业农业管理移动应用**，包含以下核心模块：

```
万亩数田App
├── 🏠 工作台模块 - 基地概况、天气信息、告警通知
├── 👥 人员管理模块 - 团队状态、位置追踪、工作统计  
├── 📋 任务管理模块 - 工单派发/接收、执行跟踪、审核
├── 🗺️ 地图模块 - 地块圈划、位置导航、多图层显示 ⭐
├── 📝 记录模块 - 作业记录、巡田记录、问题上报
└── 👤 个人中心模块 - 个人信息、数据统计、系统设置
```

## 🗺️ 地图模块定位

### 功能定位
- **专业测绘工具**：精确的地块圈划和面积计算
- **可视化管理**：地块、人员、任务的地图可视化
- **位置服务**：GPS导航和位置记录

### 使用场景
- **基地管理员**：大面积规划、边界调整、区域管理
- **作业人员**：精确测绘、位置记录、导航辅助

### 核心价值
- 提供高精度的地块圈划能力
- 支持多种卫星影像数据源
- 离线优先的设计理念
- 与业务模块深度集成

## 📚 文档结构

### 核心文档
- [`app_structure_plan.md`](./app_structure_plan.md) - **App整体架构规划**
- [`simple_tasks.md`](./simple_tasks.md) - **简化开发任务清单**

### 详细设计文档
- [`phase1_detailed_tasks.md`](./phase1_detailed_tasks.md) - Phase 1详细任务
- [`phase2_detailed_tasks.md`](./phase2_detailed_tasks.md) - Phase 2详细任务
- [`VERSIONS.md`](./VERSIONS.md) - 版本和命名规范

## 🚀 快速开始

### 新手开发者
1. 先阅读 [`app_structure_plan.md`](./app_structure_plan.md) 了解整体架构
2. 查看 [`simple_tasks.md`](./simple_tasks.md) 获取开发路线图
3. 按Phase顺序开始开发

### 技术选型说明
- **地图引擎**：MapLibre GL Native（开源、高性能）
- **架构模式**：MVVM + Jetpack Compose
- **数据源**：高德地图 + 天地图 + 吉林一号卫星影像
- **离线支持**：完整的离线地图和圈划功能

## 🎯 开发计划

### 10周开发计划
- **Week 1-2**：核心框架搭建
- **Week 3-5**：工作台和任务管理
- **Week 6-8**：地图模块开发 ⭐
- **Week 9-10**：高级功能和优化

### 地图模块里程碑
- ✅ 技术选型完成（MapLibre GL Native）
- ⏳ 基础地图显示
- ⏳ 多图层支持
- ⏳ 地块圈划功能
- ⏳ 离线地图支持

## 💡 设计理念

### 用户体验优先
- **简单易用**：符合农业作业人员使用习惯
- **离线优先**：田间网络不稳定时正常使用
- **快速响应**：关键操作响应时间<300ms

### 技术可靠性
- **开源技术栈**：避免商业限制，降低成本
- **模块化设计**：便于功能扩展和维护
- **数据安全**：本地加密存储，安全传输

## 🔗 相关资源

### 原型参考
- [`../ui/wmst_manager_prototype.html`](../ui/wmst_manager_prototype.html) - 基地管理员原型
- [`../ui/wmst_worker_prototype.html`](../ui/wmst_worker_prototype.html) - 作业人员原型

### 技术文档
- [MapLibre GL Native官方文档](https://maplibre.org/maplibre-gl-native/)
- [Android开发指南](https://developer.android.com/)
- [Jetpack Compose文档](https://developer.android.com/jetpack/compose)

---

**注意**：地图模块是万亩数田App的重要组成部分，但不是唯一功能。开发时需要考虑与其他模块的集成和数据共享。 