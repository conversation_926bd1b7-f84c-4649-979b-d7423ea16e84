# MapLibre 绘图功能调研总结 ✅

**调研时间**: 2025年1月
**目标**: 为万亩数田(WMST)地块圈定功能选择合适的绘图技术方案

## 🔍 调研范围

### MapLibre生态系统架构
```
MapLibre 生态系统
├── MapLibre Native (iOS/Android)     ← 我们的项目
└── MapLibre GL JS (Web)              ← 大部分绘图插件的目标平台
```

### 三大绘图插件全面对比

| 插件名称 | 平台支持 | 开发状态 | 核心功能 | 项目适用性 |
|---------|---------|---------|---------|-----------|
| **MapLibre Annotation Plugin** | Android Native | ✅ 活跃 | 现代注释管理器<br/>• SymbolManager<br/>• FillManager<br/>• LineManager | ✅ **适用** |
| **MapLibre GL TerraDraw** | Web (GL JS) | ✅ 活跃 | 完整绘图工具包<br/>• 内置UI控件<br/>• 多种几何图形<br/>• 事件处理 | ❌ Web专用 |
| **MapLibre Geoman** | Web (GL JS) | ✅ 活跃 | 最强大绘图插件<br/>• Draw/Edit/Drag/Cut/Rotate<br/>• 多种几何类型<br/>• 免费版+专业版 | ❌ Web专用 |

## 🎯 最终技术决策

### ✅ 选择方案: MapLibre Annotation Plugin
**理由**: 唯一适用于MapLibre Native Android平台的绘图解决方案

### 📦 依赖配置
```gradle
dependencies {
    // 核心SDK
    implementation 'org.maplibre.gl:android-sdk:11.10.3'
    
    // 绘图必需插件
    implementation 'org.maplibre.gl:android-plugin-annotation-v9:3.0.2'
    implementation 'org.maplibre.gl:android-plugin-scalebar-v9:3.0.2'
    
    // 已移除的非必需插件
    // implementation 'org.maplibre.gl:android-plugin-building-v9:3.0.2'      // 3D建筑
    // implementation 'org.maplibre.gl:android-plugin-localization-v9:3.0.2'  // 地图本地化  
    // implementation 'org.maplibre.gl:android-plugin-markerview-v9:3.0.2'    // Android View标记
    // implementation 'org.maplibre.gl:android-plugin-offline-v9:3.0.2'       // 离线功能(待验证)
}
```

### 🚧 实现挑战与解决方案
- **挑战**: 需要自定义UI控件（不像Web插件提供现成UI）
- **解决方案**: 使用Jetpack Compose开发原生绘图工具栏
- **预估工作量**: 30小时（4个工作日）

## 📋 实现计划概要

### 核心架构
```
地块圈定模块
├── UI层 (Jetpack Compose)
│   ├── 绘图工具栏
│   └── 地图视图集成
├── 绘图管理器 (FillManager)
│   ├── 多边形绘制
│   └── 编辑功能
└── 数据层 (Room + Repository)
    └── GeoJSON存储
```

### 技术特点
- **绘图引擎**: MapLibre Annotation Plugin FillManager
- **UI框架**: Jetpack Compose + Material Design 3  
- **架构模式**: Clean Architecture + MVVM
- **数据存储**: Room数据库 + GeoJSON格式

## 🔄 调研价值总结

1. **避免技术选型错误**: 差点选择Web专用插件
2. **确认唯一可行方案**: MapLibre Annotation Plugin
3. **明确开发工作量**: 需自定义UI，预估30小时
4. **优化依赖配置**: 移除4个非必需插件

这次调研确保了技术选型的准确性，为Phase 3开发奠定了坚实基础。

# MapLibre Native Android 功能能力总结

## 基于官方文档和实际验证的准确信息

### 技术栈组成
- **核心SDK**: `org.maplibre.gl:android-sdk:11.10.3`
- **必要插件**: 仅保留地块圈选功能必需的插件

---

## SDK内置功能 (无需额外依赖)

### 1. 基础地图功能
- **MapView, MapLibreMap** - 核心地图组件
- **Style管理** - 样式加载和配置
- **Camera控制** - 相机位置、缩放、动画
- **手势交互** - 平移、缩放、旋转、倾斜

### 2. 传统注释系统 (内置)
- **包路径**: `org.maplibre.android.annotations`
- **功能**: 
  - PolygonOptions - 多边形绘制
  - PolylineOptions - 线条绘制
  - MarkerOptions - 标记添加
  - **特点**: 基础功能，API较传统

### 3. 定位功能 (内置)
- **包路径**: `org.maplibre.android.location`
- **功能**: 
  - LocationComponent - 定位组件
  - 位置跟踪和显示
  - GPS轨迹记录

### 4. 离线地图 (内置)
- **包路径**: `org.maplibre.android.offline`
- **功能**:
  - 基础离线地图下载
  - 离线瓦片缓存管理
  - **注意**: 可能与插件功能重复，需要验证

### 5. 数据源支持 (内置)
- **GeoJSON数据源** - 完全支持
- **Vector Tiles** - 矢量瓦片
- **Raster Sources** - 栅格数据源

### 6. 快照功能 (内置)
- **包路径**: `org.maplibre.android.snapshotter`
- **功能**: MapSnapshotter - 地图截图生成

---

## 插件扩展功能 (需要额外依赖)

### 1. 比例尺插件 ✅ 必需
```gradle
implementation 'org.maplibre.gl:android-plugin-scalebar-v9:3.0.2'
```
- **功能**: ScaleBarPlugin - 比例尺显示
- **原因**: SDK未内置比例尺UI组件
- **使用**: 代码中已使用

### 2. 现代注释管理器 ✅ 必需
```gradle
implementation 'org.maplibre.gl:android-plugin-annotation-v9:3.0.2'
```
- **功能**: 
  - SymbolManager - 标记管理器
  - FillManager - 填充管理器  
  - LineManager - 线条管理器
  - CircleManager - 圆形管理器
- **原因**: 比传统API更强大，支持批量操作、点击监听等
- **用途**: 地块圈选核心功能

### 3. 离线地图插件 ⚠️ 待验证
```gradle
implementation 'org.maplibre.gl:android-plugin-offline-v9:3.0.2'
```
- **状态**: 需要验证是否与SDK内置功能重复
- **可能性**: 如果SDK内置功能足够，可以移除

### 4. 已移除的插件 ❌ 不需要
- ~~`plugin-building`~~ - 3D建筑渲染，地块圈选用不到
- ~~`plugin-localization`~~ - 地图本地化，非核心功能
- ~~`plugin-markerview`~~ - Android View标记，使用Compose UI

---

## 地块圈选模块支持情况

### ✅ 完全支持的功能
1. **手动点击圈选** - 使用现代注释管理器 (FillManager)
2. **GPS轨迹圈选** - 使用内置定位功能 + 线条管理器
3. **几何形状绘制** - 使用现代注释管理器
4. **面积计算** - 使用内置geometry包
5. **数据管理** - 使用GeoJSON数据源 + Room
6. **离线地图** - 使用内置或插件离线功能
7. **地图快照** - 使用内置快照功能

### 🎯 推荐技术实现
- **主要依赖**: 现代注释管理器插件
- **备选方案**: 传统注释API (功能有限)
- **数据流**: MapLibre → 注释管理器 → Repository → Room

---

## 开发资源
- **官方示例**: [MapLibre Android Examples](https://maplibre.org/maplibre-native/android/examples/)
- **API文档**: [MapLibre Native Android API](https://maplibre.org/maplibre-native/android/api/)
- **快速开始**: [Getting Started Guide](https://maplibre.org/maplibre-native/android/examples/getting-started/)

---

## 总结

**精简后的依赖策略**:
1. ✅ **核心SDK** - 提供基础地图和传统注释功能
2. ✅ **比例尺插件** - 必需的UI组件
3. ✅ **注释管理器插件** - 地块圈选核心功能
4. ⚠️ **离线插件** - 待验证是否必需

这样的配置既满足功能需求，又保持依赖最小化，避免不必要的插件引入。

# MapLibre 能力总结 - 基于官方文档调研 ✅

## 绘图插件选项对比

### 1. MapLibre Annotation Plugin (Android Native) ✅
- **类型**: Android原生插件
- **状态**: 已在项目中配置
- **功能**: 提供现代注释管理器 (SymbolManager, FillManager, LineManager)
- **优势**: 原生集成，性能好
- **劣势**: 需要自定义UI控件

### 2. MapLibre GL TerraDraw ❌ 
- **类型**: Web专用 (MapLibre GL JS)
- **状态**: 不适用于Android
- **功能**: 完整的绘图工具包
- **适用平台**: Web/JavaScript

### 3. MapLibre Geoman ❌
- **类型**: Web专用 (MapLibre GL JS)
- **状态**: 不适用于Android  
- **功能**: 功能强大的绘图和编辑插件
- **特性**: 
  - 绘制、编辑、拖拽、切割、旋转、分割、缩放、测量
  - 支持多种几何类型：Markers, Polylines, Polygons, Circles, Rectangles等
  - 提供完整的UI控件
- **NPM包**: `@geoman-io/maplibre-geoman-free` / `@geoman-io/maplibre-geoman-pro`
- **适用平台**: Web/JavaScript (Vue, React, Svelte, Angular等)

## ✅ Android项目最终选择
**MapLibre Annotation Plugin** - 唯一适用于Android Native的绘图解决方案 