# Phase 6: 数据同步模块详细任务

## 🔍 技术调研结果总结 ✅

### MapLibre 数据同步技术方案

基于MapLibre技术栈，Phase 6数据同步模块的技术选型：

| 同步内容 | 数据格式 | 技术方案 | 说明 |
|---------|---------|---------|------|
| **地块数据** | GeoJSON | 标准地理数据格式 | 与MapLibre完全兼容 |
| **标注数据** | Symbol配置 | JSON + SymbolManager | 标注位置和样式信息 |
| **地图样式** | MapLibre Style | JSON格式 | 可选的地图样式同步 |
| **离线瓦片** | 二进制文件 | 文件同步 | 离线地图数据同步 |

### 🎯 核心依赖 (与Phase 3-5一致)
```gradle
dependencies {
    // 核心SDK - 数据同步不直接依赖MapLibre，但需保证数据格式兼容
    implementation 'org.maplibre.gl:android-sdk:11.10.3'
    
    // 网络和数据处理
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'androidx.work:work-runtime-ktx:2.8.1'
    implementation 'androidx.room:room-runtime:2.5.0'
}
```

### 🚧 数据同步策略
- **地理数据兼容**: 确保所有地理数据使用GeoJSON标准格式
- **MapLibre样式**: 标注和地块样式遵循MapLibre Style规范
- **离线优先**: 本地数据优先，网络恢复时自动同步
- **冲突处理**: 基于时间戳和版本号的智能冲突解决
- **增量同步**: 只同步变更的数据，减少网络开销

### ✅ MapLibre集成优势
- **标准格式**: GeoJSON是地理数据行业标准，便于跨平台同步
- **样式一致**: MapLibre Style规范确保样式在不同设备上一致
- **开源生态**: 便于与其他GIS系统进行数据交换
- **性能优化**: 原生支持大量地理数据的高效渲染

---

## 阶段概述
- **目标**: 实现地图数据与服务器的双向同步
- **时间**: 第10周
- **前置条件**: Phase 1-5所有功能完成
- **验收标准**: 数据同步稳定、冲突处理完善、离线优先策略

## 6.1 同步数据模型

### 6.1.1 SyncState实体类
**任务**: 创建同步状态数据模型
**文件**: `app/src/main/java/cn/agrolinking/wmst/database/entity/SyncState.kt`

```kotlin
@Entity(tableName = "sync_states")
data class SyncState(
    @PrimaryKey val entityType: String,     // 实体类型：atlas, plot, annotation
    val entityId: String,                   // 实体ID
    val lastSyncTime: Long = 0,            // 最后同步时间
    val localVersion: Long = 0,            // 本地版本号
    val serverVersion: Long = 0,           // 服务器版本号
    val syncStatus: SyncStatus,            // 同步状态
    val conflictData: String? = null,      // 冲突数据JSON
    val retryCount: Int = 0,               // 重试次数
    val lastErrorMessage: String? = null   // 最后错误信息
)

enum class SyncStatus {
    SYNCED,           // 已同步
    PENDING_UPLOAD,   // 待上传
    PENDING_DOWNLOAD, // 待下载
    CONFLICT,         // 冲突
    ERROR,            // 错误
    UPLOADING,        // 上传中
    DOWNLOADING       // 下载中
}
```

### 6.1.2 SyncOperation实体类
**任务**: 创建同步操作记录
**文件**: `app/src/main/java/cn/agrolinking/wmst/database/entity/SyncOperation.kt`

```kotlin
@Entity(tableName = "sync_operations")
data class SyncOperation(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val entityType: String,
    val entityId: String,
    val operation: OperationType,
    val operationData: String,             // 操作数据JSON
    val timestamp: Long = System.currentTimeMillis(),
    val userId: String,
    val status: OperationStatus = OperationStatus.PENDING
)

enum class OperationType {
    CREATE, UPDATE, DELETE
}

enum class OperationStatus {
    PENDING, COMPLETED, FAILED
}
```

### 6.1.3 数据库DAO接口
**任务**: 创建同步数据访问接口
**文件**: `app/src/main/java/cn/agrolinking/wmst/database/dao/SyncDao.kt`

```kotlin
@Dao
interface SyncDao {
    @Query("SELECT * FROM sync_states WHERE syncStatus != 'SYNCED'")
    suspend fun getPendingSyncStates(): List<SyncState>
    
    @Query("SELECT * FROM sync_states WHERE entityType = :type AND entityId = :id")
    suspend fun getSyncState(type: String, id: String): SyncState?
    
    @Query("SELECT * FROM sync_operations WHERE status = 'PENDING' ORDER BY timestamp ASC")
    suspend fun getPendingOperations(): List<SyncOperation>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSyncState(syncState: SyncState)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOperation(operation: SyncOperation)
    
    @Update
    suspend fun updateSyncState(syncState: SyncState)
    
    @Update
    suspend fun updateOperation(operation: SyncOperation)
    
    @Query("DELETE FROM sync_operations WHERE status = 'COMPLETED' AND timestamp < :cutoffTime")
    suspend fun cleanupCompletedOperations(cutoffTime: Long)
}
```

## 6.2 数据同步管理器

### 6.2.1 DataSyncManager
**任务**: 创建数据同步核心管理器
**文件**: `app/src/main/java/cn/agrolinking/wmst/repository/DataSyncManager.kt`

```kotlin
class DataSyncManager(
    private val syncDao: SyncDao,
    private val apiService: SyncApiService,
    private val networkStateManager: NetworkStateManager,
    private val conflictResolver: ConflictResolver
) {
    private val syncScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var syncJob: Job? = null
    
    private val _syncProgress = MutableStateFlow<SyncProgress?>(null)
    val syncProgress: StateFlow<SyncProgress?> = _syncProgress.asStateFlow()
    
    data class SyncProgress(
        val totalItems: Int,
        val completedItems: Int,
        val currentOperation: String,
        val hasErrors: Boolean
    )
    
    fun startPeriodicSync(intervalMinutes: Long = 15) {
        syncJob?.cancel()
        syncJob = syncScope.launch {
            while (isActive) {
                if (networkStateManager.networkState.value == NetworkState.CONNECTED) {
                    performSync()
                }
                delay(intervalMinutes * 60 * 1000)
            }
        }
    }
    
    suspend fun performManualSync(): Boolean {
        return try {
            performSync()
            true
        } catch (e: Exception) {
            Log.e("DataSync", "Manual sync failed", e)
            false
        }
    }
    
    private suspend fun performSync() {
        val pendingStates = syncDao.getPendingSyncStates()
        val pendingOperations = syncDao.getPendingOperations()
        
        val totalItems = pendingStates.size + pendingOperations.size
        var completedItems = 0
        
        _syncProgress.value = SyncProgress(
            totalItems = totalItems,
            completedItems = 0,
            currentOperation = "开始同步",
            hasErrors = false
        )
        
        // 1. 处理本地待上传的操作
        for (operation in pendingOperations) {
            try {
                _syncProgress.value = _syncProgress.value?.copy(
                    completedItems = completedItems,
                    currentOperation = "上传${operation.entityType}"
                )
                
                when (operation.operation) {
                    OperationType.CREATE -> handleCreateOperation(operation)
                    OperationType.UPDATE -> handleUpdateOperation(operation)
                    OperationType.DELETE -> handleDeleteOperation(operation)
                }
                
                syncDao.updateOperation(operation.copy(status = OperationStatus.COMPLETED))
                completedItems++
                
            } catch (e: Exception) {
                Log.e("DataSync", "Failed to sync operation ${operation.id}", e)
                syncDao.updateOperation(
                    operation.copy(
                        status = OperationStatus.FAILED,
                        operationData = e.message ?: "Unknown error"
                    )
                )
                _syncProgress.value = _syncProgress.value?.copy(hasErrors = true)
            }
        }
        
        // 2. 处理服务器端更新
        for (syncState in pendingStates) {
            try {
                _syncProgress.value = _syncProgress.value?.copy(
                    completedItems = completedItems,
                    currentOperation = "下载${syncState.entityType}"
                )
                
                when (syncState.syncStatus) {
                    SyncStatus.PENDING_DOWNLOAD -> handleDownload(syncState)
                    SyncStatus.CONFLICT -> handleConflict(syncState)
                    else -> {}
                }
                
                completedItems++
                
            } catch (e: Exception) {
                Log.e("DataSync", "Failed to sync state ${syncState.entityId}", e)
                syncDao.updateSyncState(
                    syncState.copy(
                        syncStatus = SyncStatus.ERROR,
                        lastErrorMessage = e.message,
                        retryCount = syncState.retryCount + 1
                    )
                )
                _syncProgress.value = _syncProgress.value?.copy(hasErrors = true)
            }
        }
        
        _syncProgress.value = _syncProgress.value?.copy(
            completedItems = totalItems,
            currentOperation = "同步完成"
        )
        
        // 清理已完成的操作记录
        val cutoffTime = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000L) // 7天前
        syncDao.cleanupCompletedOperations(cutoffTime)
    }
    
    private suspend fun handleCreateOperation(operation: SyncOperation) {
        when (operation.entityType) {
            "atlas" -> {
                val atlas = Json.decodeFromString<MapAtlas>(operation.operationData)
                val response = apiService.createAtlas(atlas)
                if (response.isSuccessful) {
                    // 更新本地ID映射
                    updateLocalAtlasId(atlas.id, response.body()!!.id)
                }
            }
            "plot" -> {
                val plot = Json.decodeFromString<LandPlot>(operation.operationData)
                apiService.createPlot(plot)
            }
            "annotation" -> {
                val annotation = Json.decodeFromString<MapAnnotation>(operation.operationData)
                apiService.createAnnotation(annotation)
            }
        }
    }
    
    private suspend fun handleConflict(syncState: SyncState) {
        val resolution = conflictResolver.resolveConflict(syncState)
        when (resolution.action) {
            ConflictAction.USE_LOCAL -> {
                // 使用本地版本，上传到服务器
                uploadLocalVersion(syncState)
            }
            ConflictAction.USE_SERVER -> {
                // 使用服务器版本，下载覆盖本地
                downloadServerVersion(syncState)
            }
            ConflictAction.MERGE -> {
                // 合并版本
                mergeVersions(syncState, resolution.mergedData)
            }
            ConflictAction.MANUAL -> {
                // 需要用户手动解决
                return
            }
        }
        
        syncDao.updateSyncState(
            syncState.copy(
                syncStatus = SyncStatus.SYNCED,
                conflictData = null
            )
        )
    }
}
```

### 6.2.2 ConflictResolver
**任务**: 创建冲突解决器
**文件**: `app/src/main/java/cn/agrolinking/wmst/repository/ConflictResolver.kt`

```kotlin
class ConflictResolver {
    
    data class ConflictResolution(
        val action: ConflictAction,
        val mergedData: String? = null
    )
    
    enum class ConflictAction {
        USE_LOCAL,    // 使用本地版本
        USE_SERVER,   // 使用服务器版本
        MERGE,        // 自动合并
        MANUAL        // 需要手动解决
    }
    
    suspend fun resolveConflict(syncState: SyncState): ConflictResolution {
        return when (syncState.entityType) {
            "atlas" -> resolveAtlasConflict(syncState)
            "plot" -> resolvePlotConflict(syncState)
            "annotation" -> resolveAnnotationConflict(syncState)
            else -> ConflictResolution(ConflictAction.MANUAL)
        }
    }
    
    private fun resolveAtlasConflict(syncState: SyncState): ConflictResolution {
        val conflictData = Json.decodeFromString<ConflictData>(syncState.conflictData!!)
        val localAtlas = Json.decodeFromString<MapAtlas>(conflictData.localData)
        val serverAtlas = Json.decodeFromString<MapAtlas>(conflictData.serverData)
        
        // 简单策略：如果只是名称或描述冲突，可以自动合并
        if (localAtlas.bounds == serverAtlas.bounds && 
            localAtlas.createdBy == serverAtlas.createdBy) {
            
            val mergedAtlas = localAtlas.copy(
                name = if (localAtlas.updatedAt > serverAtlas.updatedAt) 
                    localAtlas.name else serverAtlas.name,
                description = if (localAtlas.updatedAt > serverAtlas.updatedAt) 
                    localAtlas.description else serverAtlas.description,
                updatedAt = maxOf(localAtlas.updatedAt, serverAtlas.updatedAt)
            )
            
            return ConflictResolution(
                action = ConflictAction.MERGE,
                mergedData = Json.encodeToString(mergedAtlas)
            )
        }
        
        // 复杂冲突需要手动解决
        return ConflictResolution(ConflictAction.MANUAL)
    }
    
    private fun resolvePlotConflict(syncState: SyncState): ConflictResolution {
        val conflictData = Json.decodeFromString<ConflictData>(syncState.conflictData!!)
        val localPlot = Json.decodeFromString<LandPlot>(conflictData.localData)
        val serverPlot = Json.decodeFromString<LandPlot>(conflictData.serverData)
        
        // 地块边界冲突通常需要手动解决
        if (localPlot.coordinates != serverPlot.coordinates) {
            return ConflictResolution(ConflictAction.MANUAL)
        }
        
        // 只是属性信息冲突，使用最新的版本
        return if (localPlot.updatedAt > serverPlot.updatedAt) {
            ConflictResolution(ConflictAction.USE_LOCAL)
        } else {
            ConflictResolution(ConflictAction.USE_SERVER)
        }
    }
    
    private fun resolveAnnotationConflict(syncState: SyncState): ConflictResolution {
        // 标注冲突策略：使用最后修改的版本
        val conflictData = Json.decodeFromString<ConflictData>(syncState.conflictData!!)
        val localAnnotation = Json.decodeFromString<MapAnnotation>(conflictData.localData)
        val serverAnnotation = Json.decodeFromString<MapAnnotation>(conflictData.serverData)
        
        return if (localAnnotation.updatedAt > serverAnnotation.updatedAt) {
            ConflictResolution(ConflictAction.USE_LOCAL)
        } else {
            ConflictResolution(ConflictAction.USE_SERVER)
        }
    }
}

data class ConflictData(
    val localData: String,
    val serverData: String,
    val conflictFields: List<String>
)
```

## 6.3 同步UI组件

### 6.3.1 SyncStatusIndicator
**任务**: 创建同步状态指示器
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/SyncStatusIndicator.kt`

```kotlin
@Composable
fun SyncStatusIndicator(
    syncProgress: DataSyncManager.SyncProgress?,
    lastSyncTime: Long?,
    onManualSync: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = if (syncProgress != null) "同步中..." else "数据同步",
                        style = MaterialTheme.typography.titleMedium
                    )
                    
                    if (lastSyncTime != null) {
                        Text(
                            text = "最后同步: ${formatRelativeTime(lastSyncTime)}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                IconButton(
                    onClick = onManualSync,
                    enabled = syncProgress == null
                ) {
                    Icon(
                        imageVector = Icons.Default.Sync,
                        contentDescription = "手动同步",
                        modifier = Modifier.then(
                            if (syncProgress != null) {
                                Modifier.rotate(
                                    animateFloatAsState(
                                        targetValue = 360f,
                                        animationSpec = infiniteRepeatable(
                                            animation = tween(1000, easing = LinearEasing),
                                            repeatMode = RepeatMode.Restart
                                        )
                                    ).value
                                )
                            } else Modifier
                        )
                    )
                }
            }
            
            syncProgress?.let { progress ->
                Spacer(modifier = Modifier.height(8.dp))
                
                LinearProgressIndicator(
                    progress = if (progress.totalItems > 0) {
                        progress.completedItems.toFloat() / progress.totalItems
                    } else 0f,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = progress.currentOperation,
                        style = MaterialTheme.typography.bodySmall
                    )
                    Text(
                        text = "${progress.completedItems}/${progress.totalItems}",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                if (progress.hasErrors) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "部分数据同步失败",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
        }
    }
}
```

### 6.3.2 ConflictResolutionDialog
**任务**: 创建冲突解决对话框
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/ConflictResolutionDialog.kt`

```kotlin
@Composable
fun ConflictResolutionDialog(
    conflict: SyncConflict?,
    onResolve: (ConflictResolver.ConflictAction) -> Unit,
    onDismiss: () -> Unit
) {
    if (conflict != null) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text("数据冲突") },
            text = {
                Column {
                    Text("${conflict.entityType}数据在本地和服务器上都有修改，请选择处理方式：")
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 显示冲突详情
                    ConflictDetailsCard(conflict = conflict)
                }
            },
            confirmButton = {
                Row {
                    TextButton(onClick = { onResolve(ConflictResolver.ConflictAction.USE_LOCAL) }) {
                        Text("使用本地版本")
                    }
                    TextButton(onClick = { onResolve(ConflictResolver.ConflictAction.USE_SERVER) }) {
                        Text("使用服务器版本")
                    }
                    if (conflict.canAutoMerge) {
                        TextButton(onClick = { onResolve(ConflictResolver.ConflictAction.MERGE) }) {
                            Text("自动合并")
                        }
                    }
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text("稍后处理")
                }
            }
        )
    }
}

@Composable
private fun ConflictDetailsCard(conflict: SyncConflict) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "冲突字段:",
                style = MaterialTheme.typography.labelMedium
            )
            
            conflict.conflictFields.forEach { field ->
                Spacer(modifier = Modifier.height(4.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = field,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.weight(1f)
                    )
                    Column {
                        Text(
                            text = "本地: ${conflict.localValues[field]}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Text(
                            text = "服务器: ${conflict.serverValues[field]}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.secondary
                        )
                    }
                }
            }
        }
    }
}

data class SyncConflict(
    val entityType: String,
    val entityId: String,
    val conflictFields: List<String>,
    val localValues: Map<String, String>,
    val serverValues: Map<String, String>,
    val canAutoMerge: Boolean
)
```

### 6.3.3 SyncSettingsScreen
**任务**: 创建同步设置界面
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/screens/SyncSettingsScreen.kt`

```kotlin
@Composable
fun SyncSettingsScreen(
    viewModel: SyncSettingsViewModel = hiltViewModel()
) {
    val syncSettings by viewModel.syncSettings.collectAsState()
    val syncHistory by viewModel.syncHistory.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 自动同步设置
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "自动同步设置",
                    style = MaterialTheme.typography.titleMedium
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("启用自动同步")
                    Switch(
                        checked = syncSettings.autoSyncEnabled,
                        onCheckedChange = viewModel::setAutoSyncEnabled
                    )
                }
                
                if (syncSettings.autoSyncEnabled) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text("同步间隔")
                    Slider(
                        value = syncSettings.syncIntervalMinutes.toFloat(),
                        onValueChange = { viewModel.setSyncInterval(it.toInt()) },
                        valueRange = 5f..60f,
                        steps = 10
                    )
                    Text(
                        text = "${syncSettings.syncIntervalMinutes} 分钟",
                        style = MaterialTheme.typography.bodySmall
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text("仅在WiFi下同步")
                        Switch(
                            checked = syncSettings.wifiOnlySync,
                            onCheckedChange = viewModel::setWifiOnlySync
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 同步历史
        Text(
            text = "同步历史",
            style = MaterialTheme.typography.titleMedium
        )
        
        LazyColumn {
            items(syncHistory) { record ->
                SyncHistoryItem(record = record)
            }
        }
    }
}

@Composable
private fun SyncHistoryItem(record: SyncHistoryRecord) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = formatDateTime(record.timestamp),
                    style = MaterialTheme.typography.bodyMedium
                )
                Icon(
                    imageVector = if (record.success) Icons.Default.CheckCircle else Icons.Default.Error,
                    contentDescription = null,
                    tint = if (record.success) Color.Green else Color.Red,
                    modifier = Modifier.size(16.dp)
                )
            }
            
            if (record.itemsCount > 0) {
                Text(
                    text = "同步了 ${record.itemsCount} 项数据",
                    style = MaterialTheme.typography.bodySmall
                )
            }
            
            if (!record.success && record.errorMessage != null) {
                Text(
                    text = "错误: ${record.errorMessage}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}
```

## 6.4 离线优先策略

### 6.4.1 OfflineFirstRepository
**任务**: 实现离线优先数据仓库
**文件**: `app/src/main/java/cn/agrolinking/wmst/repository/OfflineFirstRepository.kt`

```kotlin
abstract class OfflineFirstRepository<T, ID> {
    
    abstract suspend fun getLocalData(id: ID): T?
    abstract suspend fun getAllLocalData(): List<T>
    abstract suspend fun saveLocalData(data: T)
    abstract suspend fun deleteLocalData(id: ID)
    
    abstract suspend fun getRemoteData(id: ID): T?
    abstract suspend fun getAllRemoteData(): List<T>
    abstract suspend fun saveRemoteData(data: T): T
    abstract suspend fun deleteRemoteData(id: ID)
    
    abstract fun getEntityType(): String
    abstract fun getEntityId(data: T): ID
    
    // 离线优先的CRUD操作
    suspend fun get(id: ID): T? {
        // 优先从本地获取
        val localData = getLocalData(id)
        if (localData != null) {
            return localData
        }
        
        // 如果本地没有且有网络，从远程获取
        if (isNetworkAvailable()) {
            try {
                val remoteData = getRemoteData(id)
                if (remoteData != null) {
                    saveLocalData(remoteData)
                    markAsSynced(id)
                }
                return remoteData
            } catch (e: Exception) {
                Log.w("OfflineFirst", "Failed to fetch remote data", e)
            }
        }
        
        return null
    }
    
    suspend fun getAll(): List<T> {
        val localData = getAllLocalData()
        
        // 如果有网络，尝试同步最新数据
        if (isNetworkAvailable()) {
            try {
                val remoteData = getAllRemoteData()
                // 合并本地和远程数据
                val mergedData = mergeData(localData, remoteData)
                mergedData.forEach { saveLocalData(it) }
                return mergedData
            } catch (e: Exception) {
                Log.w("OfflineFirst", "Failed to sync remote data", e)
            }
        }
        
        return localData
    }
    
    suspend fun save(data: T): T {
        // 先保存到本地
        saveLocalData(data)
        
        // 标记为待同步
        markForSync(getEntityId(data), SyncStatus.PENDING_UPLOAD)
        
        // 如果有网络，立即尝试同步
        if (isNetworkAvailable()) {
            try {
                val savedData = saveRemoteData(data)
                markAsSynced(getEntityId(data))
                return savedData
            } catch (e: Exception) {
                Log.w("OfflineFirst", "Failed to sync to remote", e)
            }
        }
        
        return data
    }
    
    suspend fun delete(id: ID) {
        // 先从本地删除
        deleteLocalData(id)
        
        // 如果有网络，立即尝试从远程删除
        if (isNetworkAvailable()) {
            try {
                deleteRemoteData(id)
            } catch (e: Exception) {
                Log.w("OfflineFirst", "Failed to delete from remote", e)
                // 记录删除操作，稍后同步
                recordDeleteOperation(id)
            }
        } else {
            // 记录删除操作，稍后同步
            recordDeleteOperation(id)
        }
    }
    
    private suspend fun mergeData(localData: List<T>, remoteData: List<T>): List<T> {
        // 简单合并策略：以远程数据为准，但保留本地未同步的数据
        val localMap = localData.associateBy { getEntityId(it) }
        val remoteMap = remoteData.associateBy { getEntityId(it) }
        val result = mutableListOf<T>()
        
        // 添加所有远程数据
        result.addAll(remoteData)
        
        // 添加本地独有的数据（未同步的）
        localMap.forEach { (id, data) ->
            if (!remoteMap.containsKey(id) && isPendingSync(id)) {
                result.add(data)
            }
        }
        
        return result
    }
    
    private suspend fun markForSync(id: ID, status: SyncStatus) {
        // 实现同步状态标记
    }
    
    private suspend fun markAsSynced(id: ID) {
        // 实现同步完成标记
    }
    
    private suspend fun isPendingSync(id: ID): Boolean {
        // 检查是否待同步
        return false
    }
    
    private suspend fun recordDeleteOperation(id: ID) {
        // 记录删除操作
    }
    
    private fun isNetworkAvailable(): Boolean {
        // 检查网络状态
        return true
    }
}
```

## 6.5 数据版本控制

### 6.5.1 DataVersionManager
**任务**: 创建数据版本管理器
**文件**: `app/src/main/java/cn/agrolinking/wmst/util/DataVersionManager.kt`

```kotlin
class DataVersionManager {
    
    fun generateVersion(): Long {
        return System.currentTimeMillis()
    }
    
    fun compareVersions(local: Long, remote: Long): VersionComparison {
        return when {
            local > remote -> VersionComparison.LOCAL_NEWER
            local < remote -> VersionComparison.REMOTE_NEWER
            else -> VersionComparison.SAME
        }
    }
    
    fun createVersionedData(data: Any, version: Long): VersionedData {
        return VersionedData(
            data = Json.encodeToString(data),
            version = version,
            timestamp = System.currentTimeMillis(),
            checksum = calculateChecksum(data)
        )
    }
    
    private fun calculateChecksum(data: Any): String {
        val json = Json.encodeToString(data)
        return MessageDigest.getInstance("MD5")
            .digest(json.toByteArray())
            .joinToString("") { "%02x".format(it) }
    }
    
    enum class VersionComparison {
        LOCAL_NEWER, REMOTE_NEWER, SAME
    }
    
    data class VersionedData(
        val data: String,
        val version: Long,
        val timestamp: Long,
        val checksum: String
    )
}
```

## 6.6 测试验证

### 6.6.1 同步功能测试
**任务**: 创建数据同步测试
**文件**: `app/src/test/java/cn/agrolinking/wmst/DataSyncTest.kt`

### 6.6.2 冲突解决测试
**任务**: 创建冲突解决测试
**文件**: `app/src/test/java/cn/agrolinking/wmst/ConflictResolverTest.kt`

### 6.6.3 离线优先测试
**任务**: 创建离线优先策略测试
**文件**: `app/src/androidTest/java/cn/agrolinking/wmst/OfflineFirstTest.kt`

## 验收标准

### 功能验收
- [ ] 自动数据同步
- [ ] 手动同步触发
- [ ] 冲突检测和解决
- [ ] 离线优先策略
- [ ] 同步状态显示
- [ ] 同步历史记录

### 性能验收
- [ ] 同步不影响UI响应
- [ ] 大数据量同步稳定
- [ ] 网络异常恢复正常
- [ ] 内存使用合理

### 用户体验验收
- [ ] 同步进度清晰显示
- [ ] 冲突解决界面友好
- [ ] 离线模式无感知
- [ ] 错误提示明确

## 风险点与解决方案

### 主要风险
1. **数据冲突复杂** - 实现智能冲突检测和解决策略
2. **同步性能问题** - 使用增量同步和批量操作
3. **网络不稳定** - 实现重试机制和断点续传
4. **数据一致性** - 使用事务和版本控制

### 开发建议
1. 优先实现基础同步功能，再添加高级冲突解决
2. 重点测试各种网络环境下的同步表现
3. 提供灵活的同步策略配置
4. 考虑实现数据备份和恢复机制 