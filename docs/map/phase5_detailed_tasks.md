# Phase 5: 离线地图模块详细任务

## 🔍 技术调研结果总结 ✅

### MapLibre 离线地图技术方案

基于前期技术调研，MapLibre Native Android的离线功能需要验证和自定义实现：

| 技术组件 | 现状分析 | 实现方案 | 说明 |
|---------|---------|---------|------|
| **离线瓦片** | 内置支持待验证 | 自定义TileSource + 本地存储 | 基于瓦片的离线下载 |
| **下载管理** | 无内置管理器 | 自定义DownloadManager | 省级区域下载控制 |
| **瓦片存储** | 支持本地文件 | 文件系统 + 数据库索引 | 瓦片缓存和管理 |
| **离线切换** | 支持TileSource切换 | OfflineTileSource | 在线/离线无缝切换 |

### 🎯 核心依赖 (与Phase 3-4一致)
```gradle
dependencies {
    // 核心SDK
    implementation 'org.maplibre.gl:android-sdk:11.10.3'
    
    // 比例尺UI (离线模式状态显示)
    implementation 'org.maplibre.gl:android-plugin-scalebar-v9:3.0.2'
    
    // 注意：android-plugin-offline-v9插件需要验证功能完整性
    // 可能需要自定义实现离线下载管理
}
```

### 🚧 实现策略调整
- **离线验证**: 首先验证MapLibre内置离线功能的完整性
- **自定义实现**: 如内置功能不足，基于瓦片系统自定义实现
- **存储策略**: 使用文件系统存储瓦片 + Room数据库管理元数据
- **UI控制**: 全部使用Jetpack Compose自定义下载管理界面
- **网络检测**: 集成网络状态监听，自动切换在线/离线模式

### ⚠️ 风险评估
- **内置离线功能不完整**: 需要验证官方offline插件的功能范围
- **自定义开发工作量**: 如需完全自定义，工时可能增加1-2天
- **瓦片下载性能**: 需要优化下载速度和存储效率

---

## 阶段概述
- **目标**: 实现离线地图下载、存储和管理功能
- **时间**: 第9周
- **前置条件**: Phase 1-4基础功能完成
- **验收标准**: 省级离线地图下载、离线模式正常使用

## 5.1 离线地图数据模型

### 5.1.1 OfflineMapRegion实体类
**任务**: 创建离线地图区域数据模型
**文件**: `app/src/main/java/cn/agrolinking/wmst/database/entity/OfflineMapRegion.kt`

```kotlin
@Entity(tableName = "offline_map_regions")
data class OfflineMapRegion(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val name: String,                    // 区域名称（如"河北省"）
    val bounds: String,                  // 边界坐标JSON
    val tileSource: String,              // 瓦片源类型
    val minZoom: Int = 10,              // 最小缩放级别
    val maxZoom: Int = 18,              // 最大缩放级别
    val downloadStatus: DownloadStatus,  // 下载状态
    val totalTiles: Long = 0,           // 总瓦片数
    val downloadedTiles: Long = 0,      // 已下载瓦片数
    val estimatedSize: Long = 0,        // 预估大小（字节）
    val actualSize: Long = 0,           // 实际大小（字节）
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val lastUsedAt: Long = System.currentTimeMillis()
)

enum class DownloadStatus {
    PENDING,      // 等待下载
    DOWNLOADING,  // 下载中
    COMPLETED,    // 下载完成
    PAUSED,       // 暂停
    FAILED,       // 下载失败
    EXPIRED       // 已过期
}
```

### 5.1.2 OfflineMapTile实体类
**任务**: 创建离线瓦片数据模型
**文件**: `app/src/main/java/cn/agrolinking/wmst/database/entity/OfflineMapTile.kt`

```kotlin
@Entity(tableName = "offline_map_tiles")
data class OfflineMapTile(
    @PrimaryKey val id: String,         // 瓦片ID: "z/x/y"
    val regionId: String,               // 所属区域ID
    val tileSource: String,             // 瓦片源
    val zoom: Int,                      // 缩放级别
    val x: Int,                         // X坐标
    val y: Int,                         // Y坐标
    val filePath: String,               // 本地文件路径
    val fileSize: Long,                 // 文件大小
    val downloadedAt: Long = System.currentTimeMillis(),
    val lastAccessedAt: Long = System.currentTimeMillis()
)
```

### 5.1.3 数据库DAO接口
**任务**: 创建离线地图数据访问接口
**文件**: `app/src/main/java/cn/agrolinking/wmst/database/dao/OfflineMapDao.kt`

```kotlin
@Dao
interface OfflineMapDao {
    @Query("SELECT * FROM offline_map_regions ORDER BY lastUsedAt DESC")
    suspend fun getAllRegions(): List<OfflineMapRegion>
    
    @Query("SELECT * FROM offline_map_regions WHERE downloadStatus = :status")
    suspend fun getRegionsByStatus(status: DownloadStatus): List<OfflineMapRegion>
    
    @Query("SELECT * FROM offline_map_tiles WHERE regionId = :regionId")
    suspend fun getTilesByRegion(regionId: String): List<OfflineMapTile>
    
    @Query("SELECT * FROM offline_map_tiles WHERE id = :tileId")
    suspend fun getTileById(tileId: String): OfflineMapTile?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRegion(region: OfflineMapRegion)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTile(tile: OfflineMapTile)
    
    @Update
    suspend fun updateRegion(region: OfflineMapRegion)
    
    @Query("DELETE FROM offline_map_regions WHERE id = :regionId")
    suspend fun deleteRegion(regionId: String)
    
    @Query("DELETE FROM offline_map_tiles WHERE regionId = :regionId")
    suspend fun deleteTilesByRegion(regionId: String)
    
    @Query("SELECT SUM(actualSize) FROM offline_map_regions WHERE downloadStatus = 'COMPLETED'")
    suspend fun getTotalOfflineSize(): Long?
}
```

## 5.2 离线地图下载管理器

### 5.2.1 OfflineMapDownloadManager
**任务**: 创建离线地图下载管理器
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/OfflineMapDownloadManager.kt`

```kotlin
class OfflineMapDownloadManager(
    private val context: Context,
    private val offlineMapDao: OfflineMapDao,
    private val tileSourceManager: TileSourceManager
) {
    private val downloadJobs = mutableMapOf<String, Job>()
    private val downloadScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val _downloadProgress = MutableStateFlow<Map<String, DownloadProgress>>(emptyMap())
    val downloadProgress: StateFlow<Map<String, DownloadProgress>> = _downloadProgress.asStateFlow()
    
    data class DownloadProgress(
        val regionId: String,
        val totalTiles: Long,
        val downloadedTiles: Long,
        val failedTiles: Long,
        val downloadSpeed: Long, // tiles/second
        val estimatedTimeRemaining: Long // seconds
    )
    
    suspend fun startDownload(region: OfflineMapRegion) {
        if (downloadJobs.containsKey(region.id)) {
            return // 已在下载中
        }
        
        val updatedRegion = region.copy(
            downloadStatus = DownloadStatus.DOWNLOADING,
            updatedAt = System.currentTimeMillis()
        )
        offlineMapDao.updateRegion(updatedRegion)
        
        val job = downloadScope.launch {
            try {
                downloadRegionTiles(updatedRegion)
            } catch (e: Exception) {
                handleDownloadError(region.id, e)
            } finally {
                downloadJobs.remove(region.id)
            }
        }
        
        downloadJobs[region.id] = job
    }
    
    private suspend fun downloadRegionTiles(region: OfflineMapRegion) {
        val bounds = parseBounds(region.bounds)
        val tileSource = tileSourceManager.getTileSource(region.tileSource)
        
        var downloadedCount = 0L
        var failedCount = 0L
        val startTime = System.currentTimeMillis()
        
        for (zoom in region.minZoom..region.maxZoom) {
            val tileRange = calculateTileRange(bounds, zoom)
            
            for (x in tileRange.minX..tileRange.maxX) {
                for (y in tileRange.minY..tileRange.maxY) {
                    if (!isActive) break // 检查是否被取消
                    
                    val tileId = "$zoom/$x/$y"
                    
                    try {
                        val tileData = tileSource.getTileData(zoom, x, y)
                        val filePath = saveTileToFile(region.id, tileId, tileData)
                        
                        val tile = OfflineMapTile(
                            id = tileId,
                            regionId = region.id,
                            tileSource = region.tileSource,
                            zoom = zoom,
                            x = x,
                            y = y,
                            filePath = filePath,
                            fileSize = tileData.size.toLong()
                        )
                        
                        offlineMapDao.insertTile(tile)
                        downloadedCount++
                        
                    } catch (e: Exception) {
                        failedCount++
                        Log.w("OfflineDownload", "Failed to download tile $tileId", e)
                    }
                    
                    // 更新进度
                    updateDownloadProgress(
                        region.id,
                        region.totalTiles,
                        downloadedCount,
                        failedCount,
                        startTime
                    )
                    
                    // 控制下载速度，避免过度占用网络
                    delay(50)
                }
            }
        }
        
        // 下载完成，更新区域状态
        val completedRegion = region.copy(
            downloadStatus = DownloadStatus.COMPLETED,
            downloadedTiles = downloadedCount,
            actualSize = calculateActualSize(region.id),
            updatedAt = System.currentTimeMillis()
        )
        offlineMapDao.updateRegion(completedRegion)
    }
    
    private fun saveTileToFile(regionId: String, tileId: String, tileData: ByteArray): String {
        val regionDir = File(context.filesDir, "offline_maps/$regionId")
        if (!regionDir.exists()) {
            regionDir.mkdirs()
        }
        
        val tileFile = File(regionDir, "${tileId.replace("/", "_")}.png")
        tileFile.writeBytes(tileData)
        return tileFile.absolutePath
    }
    
    fun pauseDownload(regionId: String) {
        downloadJobs[regionId]?.cancel()
        downloadJobs.remove(regionId)
        
        downloadScope.launch {
            val region = offlineMapDao.getAllRegions().find { it.id == regionId }
            region?.let {
                val pausedRegion = it.copy(
                    downloadStatus = DownloadStatus.PAUSED,
                    updatedAt = System.currentTimeMillis()
                )
                offlineMapDao.updateRegion(pausedRegion)
            }
        }
    }
    
    fun cancelDownload(regionId: String) {
        pauseDownload(regionId)
        
        downloadScope.launch {
            // 删除已下载的文件
            deleteRegionFiles(regionId)
            offlineMapDao.deleteRegion(regionId)
            offlineMapDao.deleteTilesByRegion(regionId)
        }
    }
}
```

### 5.2.2 TileCalculator工具类
**任务**: 创建瓦片计算工具
**文件**: `app/src/main/java/cn/agrolinking/wmst/util/TileCalculator.kt`

```kotlin
object TileCalculator {
    data class TileRange(
        val minX: Int,
        val maxX: Int,
        val minY: Int,
        val maxY: Int
    )
    
    data class LatLngBounds(
        val north: Double,
        val south: Double,
        val east: Double,
        val west: Double
    )
    
    fun calculateTileRange(bounds: LatLngBounds, zoom: Int): TileRange {
        val minX = longitudeToTileX(bounds.west, zoom)
        val maxX = longitudeToTileX(bounds.east, zoom)
        val minY = latitudeToTileY(bounds.north, zoom)
        val maxY = latitudeToTileY(bounds.south, zoom)
        
        return TileRange(
            minX = minOf(minX, maxX),
            maxX = maxOf(minX, maxX),
            minY = minOf(minY, maxY),
            maxY = maxOf(minY, maxY)
        )
    }
    
    fun calculateTotalTiles(bounds: LatLngBounds, minZoom: Int, maxZoom: Int): Long {
        var total = 0L
        for (zoom in minZoom..maxZoom) {
            val range = calculateTileRange(bounds, zoom)
            val tilesInZoom = (range.maxX - range.minX + 1).toLong() * 
                             (range.maxY - range.minY + 1).toLong()
            total += tilesInZoom
        }
        return total
    }
    
    fun estimateDownloadSize(totalTiles: Long, avgTileSize: Int = 15000): Long {
        return totalTiles * avgTileSize // 平均每个瓦片15KB
    }
    
    private fun longitudeToTileX(longitude: Double, zoom: Int): Int {
        return ((longitude + 180.0) / 360.0 * (1 shl zoom)).toInt()
    }
    
    private fun latitudeToTileY(latitude: Double, zoom: Int): Int {
        val latRad = Math.toRadians(latitude)
        return ((1.0 - asinh(tan(latRad)) / PI) / 2.0 * (1 shl zoom)).toInt()
    }
    
    private fun asinh(x: Double): Double {
        return ln(x + sqrt(x * x + 1.0))
    }
}
```

## 5.3 离线地图UI组件

### 5.3.1 OfflineMapManagementScreen
**任务**: 创建离线地图管理界面
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/screens/OfflineMapManagementScreen.kt`

```kotlin
@Composable
fun OfflineMapManagementScreen(
    navController: NavController,
    viewModel: OfflineMapViewModel = hiltViewModel()
) {
    val regions by viewModel.regions.collectAsState()
    val downloadProgress by viewModel.downloadProgress.collectAsState()
    val totalOfflineSize by viewModel.totalOfflineSize.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 存储使用情况
        StorageUsageCard(
            totalSize = totalOfflineSize,
            availableSpace = getAvailableStorageSpace()
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 添加新区域按钮
        Button(
            onClick = { navController.navigate("region_selection") },
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(Icons.Default.Add, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text("下载新区域")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 已下载区域列表
        Text(
            text = "已下载区域",
            style = MaterialTheme.typography.titleMedium
        )
        
        LazyColumn {
            items(regions) { region ->
                OfflineRegionItem(
                    region = region,
                    progress = downloadProgress[region.id],
                    onPause = { viewModel.pauseDownload(region.id) },
                    onResume = { viewModel.resumeDownload(region.id) },
                    onDelete = { viewModel.deleteRegion(region.id) }
                )
            }
        }
    }
}

@Composable
private fun StorageUsageCard(
    totalSize: Long,
    availableSpace: Long
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "存储使用情况",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("离线地图: ${formatFileSize(totalSize)}")
                Text("可用空间: ${formatFileSize(availableSpace)}")
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LinearProgressIndicator(
                progress = if (availableSpace > 0) {
                    totalSize.toFloat() / (totalSize + availableSpace)
                } else 0f,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}
```

### 5.3.2 RegionSelectionScreen
**任务**: 创建区域选择界面
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/screens/RegionSelectionScreen.kt`

```kotlin
@Composable
fun RegionSelectionScreen(
    navController: NavController,
    viewModel: RegionSelectionViewModel = hiltViewModel()
) {
    val availableRegions by viewModel.availableRegions.collectAsState()
    val selectedRegion by viewModel.selectedRegion.collectAsState()
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 地图显示区域边界
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp)
        ) {
            RegionPreviewMap(
                regions = availableRegions,
                selectedRegion = selectedRegion,
                onRegionSelected = viewModel::selectRegion
            )
        }
        
        // 区域列表
        LazyColumn(
            modifier = Modifier.weight(1f)
        ) {
            items(availableRegions) { region ->
                RegionSelectionItem(
                    region = region,
                    isSelected = region == selectedRegion,
                    onClick = { viewModel.selectRegion(region) }
                )
            }
        }
        
        // 下载按钮
        selectedRegion?.let { region ->
            DownloadConfirmationCard(
                region = region,
                onConfirm = {
                    viewModel.startDownload(region)
                    navController.popBackStack()
                }
            )
        }
    }
}

@Composable
private fun DownloadConfirmationCard(
    region: RegionInfo,
    onConfirm: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "下载 ${region.name}",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("预估大小: ${formatFileSize(region.estimatedSize)}")
                Text("瓦片数量: ${region.totalTiles}")
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onConfirm,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("开始下载")
            }
        }
    }
}
```

### 5.3.3 OfflineRegionItem组件
**任务**: 创建离线区域列表项
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/OfflineRegionItem.kt`

```kotlin
@Composable
fun OfflineRegionItem(
    region: OfflineMapRegion,
    progress: OfflineMapDownloadManager.DownloadProgress?,
    onPause: () -> Unit,
    onResume: () -> Unit,
    onDelete: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = region.name,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = getStatusText(region.downloadStatus),
                        style = MaterialTheme.typography.bodySmall,
                        color = getStatusColor(region.downloadStatus)
                    )
                }
                
                Row {
                    when (region.downloadStatus) {
                        DownloadStatus.DOWNLOADING -> {
                            IconButton(onClick = onPause) {
                                Icon(Icons.Default.Pause, contentDescription = "暂停")
                            }
                        }
                        DownloadStatus.PAUSED -> {
                            IconButton(onClick = onResume) {
                                Icon(Icons.Default.PlayArrow, contentDescription = "继续")
                            }
                        }
                        else -> {}
                    }
                    
                    IconButton(onClick = onDelete) {
                        Icon(Icons.Default.Delete, contentDescription = "删除")
                    }
                }
            }
            
            // 下载进度
            when (region.downloadStatus) {
                DownloadStatus.DOWNLOADING, DownloadStatus.PAUSED -> {
                    progress?.let { p ->
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        LinearProgressIndicator(
                            progress = if (p.totalTiles > 0) {
                                p.downloadedTiles.toFloat() / p.totalTiles
                            } else 0f,
                            modifier = Modifier.fillMaxWidth()
                        )
                        
                        Spacer(modifier = Modifier.height(4.dp))
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "${p.downloadedTiles}/${p.totalTiles} 瓦片",
                                style = MaterialTheme.typography.bodySmall
                            )
                            if (p.estimatedTimeRemaining > 0) {
                                Text(
                                    text = "剩余 ${formatDuration(p.estimatedTimeRemaining)}",
                                    style = MaterialTheme.typography.bodySmall
                                )
                            }
                        }
                    }
                }
                DownloadStatus.COMPLETED -> {
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "大小: ${formatFileSize(region.actualSize)}",
                            style = MaterialTheme.typography.bodySmall
                        )
                        Text(
                            text = "最后使用: ${formatDate(region.lastUsedAt)}",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
                else -> {}
            }
        }
    }
}
```

## 5.4 离线瓦片服务

### 5.4.1 OfflineTileSource
**任务**: 创建离线瓦片源
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/OfflineTileSource.kt`

```kotlin
class OfflineTileSource(
    private val offlineMapDao: OfflineMapDao,
    private val fallbackTileSource: TileSource?
) : TileSource {
    
    override suspend fun getTileData(zoom: Int, x: Int, y: Int): ByteArray {
        val tileId = "$zoom/$x/$y"
        
        // 首先尝试从离线缓存获取
        val offlineTile = offlineMapDao.getTileById(tileId)
        if (offlineTile != null) {
            val file = File(offlineTile.filePath)
            if (file.exists()) {
                // 更新最后访问时间
                updateLastAccessTime(offlineTile)
                return file.readBytes()
            }
        }
        
        // 如果离线没有，尝试从网络获取
        fallbackTileSource?.let { source ->
            try {
                return source.getTileData(zoom, x, y)
            } catch (e: Exception) {
                // 网络获取失败，返回空白瓦片
                return createBlankTile()
            }
        }
        
        // 没有备用源，返回空白瓦片
        return createBlankTile()
    }
    
    private suspend fun updateLastAccessTime(tile: OfflineMapTile) {
        val updatedTile = tile.copy(
            lastAccessedAt = System.currentTimeMillis()
        )
        offlineMapDao.insertTile(updatedTile)
    }
    
    private fun createBlankTile(): ByteArray {
        // 创建256x256的空白PNG瓦片
        val bitmap = Bitmap.createBitmap(256, 256, Bitmap.Config.ARGB_8888)
        bitmap.eraseColor(Color.TRANSPARENT)
        
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
        return outputStream.toByteArray()
    }
}
```

### 5.4.2 OfflineMapManager
**任务**: 创建离线地图管理器
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/OfflineMapManager.kt`

```kotlin
class OfflineMapManager(
    private val context: Context,
    private val offlineMapDao: OfflineMapDao,
    private val tileSourceManager: TileSourceManager
) {
    private var isOfflineMode = false
    
    fun enableOfflineMode() {
        isOfflineMode = true
        // 切换所有图层到离线模式
        tileSourceManager.switchToOfflineMode()
    }
    
    fun disableOfflineMode() {
        isOfflineMode = false
        // 切换回在线模式
        tileSourceManager.switchToOnlineMode()
    }
    
    suspend fun isRegionAvailableOffline(bounds: LatLngBounds, zoom: Int): Boolean {
        val regions = offlineMapDao.getRegionsByStatus(DownloadStatus.COMPLETED)
        
        return regions.any { region ->
            val regionBounds = parseBounds(region.bounds)
            zoom >= region.minZoom && 
            zoom <= region.maxZoom &&
            boundsIntersect(bounds, regionBounds)
        }
    }
    
    suspend fun getOfflineRegionsInBounds(bounds: LatLngBounds): List<OfflineMapRegion> {
        val allRegions = offlineMapDao.getRegionsByStatus(DownloadStatus.COMPLETED)
        
        return allRegions.filter { region ->
            val regionBounds = parseBounds(region.bounds)
            boundsIntersect(bounds, regionBounds)
        }
    }
    
    suspend fun cleanupOldTiles(maxAge: Long = 30 * 24 * 60 * 60 * 1000L) {
        val cutoffTime = System.currentTimeMillis() - maxAge
        val allRegions = offlineMapDao.getAllRegions()
        
        allRegions.forEach { region ->
            val tiles = offlineMapDao.getTilesByRegion(region.id)
            val oldTiles = tiles.filter { it.lastAccessedAt < cutoffTime }
            
            oldTiles.forEach { tile ->
                // 删除文件
                File(tile.filePath).delete()
                // 从数据库删除记录
                offlineMapDao.deleteTilesByRegion(tile.id)
            }
        }
    }
    
    private fun boundsIntersect(bounds1: LatLngBounds, bounds2: LatLngBounds): Boolean {
        return !(bounds1.east < bounds2.west || 
                bounds1.west > bounds2.east || 
                bounds1.north < bounds2.south || 
                bounds1.south > bounds2.north)
    }
}
```

## 5.5 离线模式检测

### 5.5.1 NetworkStateManager
**任务**: 创建网络状态管理器
**文件**: `app/src/main/java/cn/agrolinking/wmst/util/NetworkStateManager.kt`

```kotlin
class NetworkStateManager(private val context: Context) {
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    private val _networkState = MutableStateFlow(getCurrentNetworkState())
    val networkState: StateFlow<NetworkState> = _networkState.asStateFlow()
    
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            _networkState.value = NetworkState.CONNECTED
        }
        
        override fun onLost(network: Network) {
            _networkState.value = NetworkState.DISCONNECTED
        }
        
        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            val hasValidated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            
            _networkState.value = if (hasInternet && hasValidated) {
                NetworkState.CONNECTED
            } else {
                NetworkState.LIMITED
            }
        }
    }
    
    fun startMonitoring() {
        val request = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
        
        connectivityManager.registerNetworkCallback(request, networkCallback)
    }
    
    fun stopMonitoring() {
        connectivityManager.unregisterNetworkCallback(networkCallback)
    }
    
    private fun getCurrentNetworkState(): NetworkState {
        val activeNetwork = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        
        return when {
            networkCapabilities == null -> NetworkState.DISCONNECTED
            networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
            networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED) -> NetworkState.CONNECTED
            else -> NetworkState.LIMITED
        }
    }
}

enum class NetworkState {
    CONNECTED,    // 网络连接正常
    LIMITED,      // 网络连接受限
    DISCONNECTED  // 无网络连接
}
```

### 5.5.2 OfflineModeIndicator
**任务**: 创建离线模式指示器
**文件**: `app/src/main/java/cn/agrolinking/wmst/ui/components/OfflineModeIndicator.kt`

```kotlin
@Composable
fun OfflineModeIndicator(
    isOfflineMode: Boolean,
    networkState: NetworkState,
    onToggleOfflineMode: () -> Unit
) {
    AnimatedVisibility(
        visible = isOfflineMode || networkState != NetworkState.CONNECTED,
        enter = slideInVertically() + fadeIn(),
        exit = slideOutVertically() + fadeOut()
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),
            colors = CardDefaults.cardColors(
                containerColor = when {
                    isOfflineMode -> MaterialTheme.colorScheme.primaryContainer
                    networkState == NetworkState.DISCONNECTED -> MaterialTheme.colorScheme.errorContainer
                    else -> MaterialTheme.colorScheme.warningContainer
                }
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = when {
                            isOfflineMode -> Icons.Default.CloudOff
                            networkState == NetworkState.DISCONNECTED -> Icons.Default.SignalWifiOff
                            else -> Icons.Default.SignalWifiStatusbarConnectedNoInternet4
                        },
                        contentDescription = null,
                        modifier = Modifier.size(20.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = when {
                            isOfflineMode -> "离线模式"
                            networkState == NetworkState.DISCONNECTED -> "无网络连接"
                            else -> "网络连接受限"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                
                if (networkState == NetworkState.CONNECTED) {
                    TextButton(onClick = onToggleOfflineMode) {
                        Text(if (isOfflineMode) "切换到在线" else "切换到离线")
                    }
                }
            }
        }
    }
}
```

## 5.6 存储空间管理

### 5.6.1 StorageManager
**任务**: 创建存储空间管理器
**文件**: `app/src/main/java/cn/agrolinking/wmst/util/StorageManager.kt`

```kotlin
class StorageManager(private val context: Context) {
    
    fun getAvailableStorageSpace(): Long {
        val stat = StatFs(context.filesDir.path)
        return stat.availableBytes
    }
    
    fun getTotalStorageSpace(): Long {
        val stat = StatFs(context.filesDir.path)
        return stat.totalBytes
    }
    
    suspend fun getOfflineMapStorageUsage(): Long {
        val offlineMapDir = File(context.filesDir, "offline_maps")
        return if (offlineMapDir.exists()) {
            calculateDirectorySize(offlineMapDir)
        } else {
            0L
        }
    }
    
    private fun calculateDirectorySize(directory: File): Long {
        var size = 0L
        directory.listFiles()?.forEach { file ->
            size += if (file.isDirectory) {
                calculateDirectorySize(file)
            } else {
                file.length()
            }
        }
        return size
    }
    
    suspend fun cleanupExpiredRegions(offlineMapDao: OfflineMapDao, maxAge: Long) {
        val cutoffTime = System.currentTimeMillis() - maxAge
        val expiredRegions = offlineMapDao.getAllRegions().filter { 
            it.lastUsedAt < cutoffTime 
        }
        
        expiredRegions.forEach { region ->
            deleteRegionFiles(region.id)
            offlineMapDao.deleteRegion(region.id)
            offlineMapDao.deleteTilesByRegion(region.id)
        }
    }
    
    private fun deleteRegionFiles(regionId: String) {
        val regionDir = File(context.filesDir, "offline_maps/$regionId")
        if (regionDir.exists()) {
            regionDir.deleteRecursively()
        }
    }
    
    fun isStorageSpaceSufficient(requiredSpace: Long): Boolean {
        val availableSpace = getAvailableStorageSpace()
        val bufferSpace = 100 * 1024 * 1024L // 保留100MB缓冲空间
        return availableSpace > (requiredSpace + bufferSpace)
    }
}
```

## 5.7 测试验证

### 5.7.1 离线下载测试
**任务**: 创建离线下载功能测试
**文件**: `app/src/test/java/cn/agrolinking/wmst/OfflineDownloadTest.kt`

### 5.7.2 离线模式测试
**任务**: 创建离线模式功能测试
**文件**: `app/src/androidTest/java/cn/agrolinking/wmst/OfflineModeTest.kt`

### 5.7.3 存储管理测试
**任务**: 创建存储管理测试
**文件**: `app/src/test/java/cn/agrolinking/wmst/StorageManagerTest.kt`

## 验收标准

### 功能验收
- [ ] 省级区域离线地图下载
- [ ] 下载进度显示和控制（暂停/继续/取消）
- [ ] 离线模式自动切换
- [ ] 离线瓦片正常显示
- [ ] 存储空间管理
- [ ] 过期数据清理

### 性能验收
- [ ] 下载速度合理（不影响其他网络使用）
- [ ] 离线瓦片加载速度 < 200ms
- [ ] 内存使用稳定
- [ ] 存储空间使用效率高

### 用户体验验收
- [ ] 下载进度清晰显示
- [ ] 网络状态变化响应及时
- [ ] 离线模式提示友好
- [ ] 存储空间不足时有提醒

## 风险点与解决方案

### 主要风险
1. **存储空间不足** - 实现智能清理和空间预警
2. **下载中断处理** - 支持断点续传
3. **瓦片数据损坏** - 实现数据完整性检查
4. **网络切换卡顿** - 优化在线/离线模式切换

### 开发建议
1. 优先实现核心下载功能，再添加高级管理特性
2. 重点测试网络不稳定环境下的表现
3. 提供灵活的存储策略配置
4. 考虑实现增量更新机制