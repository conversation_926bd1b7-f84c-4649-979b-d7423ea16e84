# 🎯 万亩数田(WMST) - Android开发调试完整指南

本文档为**万亩数田**农业种植管理系统的Android开发调试指南，适合Android开发新手和有经验的开发者参考。

## 📋 目录

- [环境准备](#环境准备)
  - [🍎 Apple Silicon Mac 特殊配置](#apple-silicon-mac-特殊配置)
- [项目架构理解](#项目架构理解)
- [开发调试流程](#开发调试流程)
- [日志调试](#日志调试)
- [设备调试](#设备调试)
- [常用调试命令](#常用调试命令)
- [实战案例](#实战案例)
  - [🍎 案例4：Apple Silicon Mac 模拟器崩溃](#案例4apple-silicon-mac-模拟器崩溃)
- [性能调试](#性能调试)
- [最佳实践](#最佳实践)

## 🏗️ 环境准备

### 必需工具检查

```bash
# 检查Java版本 (需要Java 17+)
java -version

# 检查Gradle版本
./gradlew --version

# 检查ADB调试工具
adb version

# 检查连接的设备
adb devices
```

### 环境要求

- **Android Studio**: Iguana (2023.2.1) 或更高版本
- **JDK**: 17 或更高版本
- **Kotlin**: 1.9.22 或更高版本
- **Android Gradle Plugin**: 8.2.2 或更高版本
- **编译SDK**: Android 14 (API 34)
- **最低支持**: Android 6.0 (API 23)

### 🍎 Apple Silicon Mac 特殊配置

#### 模拟器崩溃问题解决方案

**问题描述**：在Apple Silicon Mac (M1/M2/M3/M4) 上运行Android模拟器时，可能遇到以下崩溃错误：
```
error messaging the mach port for IMKCFRunLoopWakeUpReliable
```

**根本原因**：
- macOS Sequoia (15.x) 与QEMU虚拟化的兼容性问题
- 系统输入法服务 (IMK) 与模拟器GUI的冲突
- GPU渲染在Apple Silicon上的兼容性问题

**✅ 解决方案：使用无头模式**

1. **检查可用的AVD**：
   ```bash
   $ANDROID_HOME/emulator/emulator -list-avds
   ```

2. **使用无头模式启动模拟器**：
   ```bash
   # 推荐的启动命令
   $ANDROID_HOME/emulator/emulator -avd YOUR_AVD_NAME \
     -gpu swiftshader_indirect \
     -no-snapshot \
     -no-audio \
     -no-window
   ```

3. **验证模拟器状态**：
   ```bash
   # 等待模拟器启动（通常需要20-30秒）
   sleep 20 && adb devices
   
   # 应该看到类似输出：
   # List of devices attached
   # emulator-5554   device
   ```

4. **完整的开发工作流**：
   ```bash
   # 1. 启动无头模拟器（后台运行）
   $ANDROID_HOME/emulator/emulator -avd Medium_Phone_API_35 \
     -gpu swiftshader_indirect -no-snapshot -no-audio -no-window &
   
   # 2. 等待启动完成
   sleep 30
   
   # 3. 构建并安装应用
   ./gradlew clean assembleDebug
   ./gradlew installDebug
   
   # 4. 启动应用
   adb shell am start -n cn.agrolinking.wmst/.ui.MainActivity
   
   # 5. 查看应用日志
   adb logcat -s "WMST"
   ```

**🔧 故障排除**：

- **模拟器显示offline**：等待更长时间，模拟器启动需要时间
- **安装失败**：确保模拟器完全启动后再安装应用
- **进程检查**：
  ```bash
  # 检查模拟器进程
  ps aux | grep emulator
  
  # 检查应用进程
  adb shell ps | grep wmst
  ```

**📱 无头模式的优势**：
- ✅ 避免GUI相关的崩溃问题
- ✅ 更稳定的运行环境
- ✅ 更少的系统资源占用
- ✅ 完整的ADB调试功能
- ✅ 支持所有开发和测试功能

**⚠️ 注意事项**：
- 无头模式下无法看到模拟器界面，但所有功能正常
- 可以通过ADB命令进行所有操作和调试
- 建议在Apple Silicon Mac上优先使用此方案

## 📱 项目架构理解

### 项目结构

```
app/src/main/java/cn/agrolinking/wmst/
├── 📁 database/          # Room数据库
│   ├── UserEntity.kt     # 用户实体
│   ├── DetailsEntity.kt  # 详情实体
│   └── Room.kt          # 数据库配置
├── 📁 di/               # 依赖注入
│   ├── DatabaseModule.kt # 数据库模块
│   └── NetworkModule.kt  # 网络模块
├── 📁 domain/           # 领域模型
│   ├── User.kt          # 用户领域模型
│   └── Details.kt       # 详情领域模型
├── 📁 network/          # 网络层
│   ├── UsersApi.kt      # 用户API接口
│   ├── DetailsApi.kt    # 详情API接口
│   └── model/           # API数据模型
├── 📁 repository/       # 仓库层
│   ├── UsersRepository.kt    # 用户仓库
│   └── DetailsRepository.kt  # 详情仓库
├── 📁 ui/               # UI层
│   ├── MainActivity.kt  # 主Activity
│   ├── ComposeApp.kt    # 应用导航
│   ├── users/           # 用户模块UI
│   ├── details/         # 详情模块UI
│   ├── components/      # 通用组件
│   └── theme/           # 主题配置
├── 📁 util/             # 工具类
└── ComposeApplication.kt # 应用入口
```

### 架构流程

```
🏠 MainActivity.kt        # 应用入口，设置主题
📱 ComposeApp.kt          # 导航控制器，管理页面跳转
├── 👥 UsersScreen         # 用户列表页面
└── 📄 DetailsScreen       # 用户详情页面
```

### 数据流转

```
API Response → UserApiModel → UserEntity → User (Domain) → UI State
```

## 🚀 开发调试流程

### 阶段1：项目构建

#### 基础构建命令

```bash
# 清理项目
./gradlew clean

# 构建项目
./gradlew build

# 构建Debug版本
./gradlew assembleDebug

# 构建Release版本
./gradlew assembleRelease
```

#### 安装到设备

```bash
# 安装Debug版本
./gradlew installDebug

# 安装Release版本
./gradlew installRelease

# 安装并运行
./gradlew installDebug && adb shell am start -n cn.agrolinking.wmst/.MainActivity
```

### 阶段2：设备准备

#### 选择1：使用真实设备

1. **启用开发者选项**：
   - 设置 → 关于手机 → 连续点击"版本号"7次
   - 返回设置 → 开发者选项 → 启用"USB调试"

2. **连接设备**：
   ```bash
   adb devices                     # 确认设备连接
   ./gradlew installDebug          # 安装应用
   ```

#### 选择2：使用Android模拟器

**标准模式（适用于Intel Mac和Windows）**：
```bash
# 启动模拟器（需要Android Studio）
emulator @your_avd_name

# 列出可用的AVD
emulator -list-avds

# 启动指定模拟器
emulator @Pixel_6_API_34
```

**🍎 Apple Silicon Mac 无头模式（推荐）**：
```bash
# 1. 列出可用的AVD
$ANDROID_HOME/emulator/emulator -list-avds

# 2. 启动无头模拟器（后台运行）
$ANDROID_HOME/emulator/emulator -avd Medium_Phone_API_35 \
  -gpu swiftshader_indirect -no-snapshot -no-audio -no-window &

# 3. 等待启动完成并验证
sleep 30 && adb devices

# 4. 安装并启动应用
./gradlew installDebug
adb shell am start -n cn.agrolinking.wmst/.ui.MainActivity
```

**无头模式调试技巧**：
```bash
# 检查模拟器状态
adb devices

# 检查应用是否运行
adb shell ps | grep wmst

# 模拟用户操作（点击、滑动等）
adb shell input tap 540 1200        # 点击屏幕中心
adb shell input swipe 540 1200 540 800  # 向上滑动

# 截取屏幕截图
adb shell screencap -p /sdcard/screenshot.png
adb pull /sdcard/screenshot.png .

# 录制屏幕视频
adb shell screenrecord /sdcard/demo.mp4
# 停止录制：Ctrl+C
adb pull /sdcard/demo.mp4 .
```

## 📝 日志调试

### 项目日志配置

万亩数田项目使用**Timber**日志库，已在`ComposeApplication.kt`中配置：

```kotlin
@HiltAndroidApp
class ComposeApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        }
    }
}
```

### 日志使用方法

#### 在代码中添加日志

```kotlin
import timber.log.Timber

class YourClass {
    fun yourFunction() {
        Timber.d("调试信息: 函数开始执行")
        Timber.i("信息: 用户点击了按钮")
        Timber.w("警告: 网络连接缓慢")
        Timber.e("错误: 数据加载失败")
    }
}
```

#### 实时日志查看

```bash
# 查看所有日志
adb logcat

# 过滤应用日志
adb logcat | grep "cn.agrolinking.wmst"

# 过滤特定标签
adb logcat -s "WMST"

# 清空日志后查看
adb logcat -c && adb logcat

# 保存日志到文件
adb logcat > debug.log
```

### 专项日志调试

#### 网络请求调试

```bash
# 查看网络请求日志
adb logcat | grep -E "(OkHttp|Retrofit)"

# 查看HTTP请求详情
adb logcat | grep "HTTP"

# 查看UsersRepository日志
adb logcat | grep "UsersRepository"
```

#### 数据库调试

```bash
# 查看Room数据库操作
adb logcat | grep "Room"

# 查看SQL查询
adb logcat | grep "SQLite"
```

#### UI调试

```bash
# 查看Compose重组信息
adb logcat | grep "Compose"

# 查看布局问题
adb logcat | grep "Layout"
```

## 🔧 常用调试命令

### APK管理

```bash
# 构建Debug APK
./gradlew assembleDebug

# 构建Release APK
./gradlew assembleRelease

# 手动安装APK
adb install app/build/outputs/apk/debug/app-debug.apk

# 查找生成的APK文件
find . -name "*.apk" -type f
```

### 应用管理

```bash
# 查看已安装的应用
adb shell pm list packages | grep wmst

# 卸载应用
adb uninstall cn.agrolinking.wmst

# 启动应用
adb shell am start -n cn.agrolinking.wmst/.MainActivity

# 关闭应用
adb shell am force-stop cn.agrolinking.wmst
```

### 日志管理

```bash
# 清空日志
adb logcat -c

# 保存日志到文件
adb logcat > app.log

# 过滤无用日志
adb logcat | grep -v "chatty"

# 按级别过滤日志
adb logcat *:W  # 只显示警告及以上级别
```

### 文件调试

```bash
# 进入应用沙盒
adb shell run-as cn.agrolinking.wmst

# 下载设备文件
adb pull /sdcard/Download/app.log .

# 上传文件到设备
adb push local-file.txt /sdcard/
```

## 🎯 实战调试案例

### 案例1：用户列表加载失败

#### 问题现象
- 用户列表页面显示空白
- 网络请求失败

#### 调试步骤

1. **查看网络日志**
   ```bash
   adb logcat | grep -E "(UsersRepository|网络|HTTP)"
   ```

2. **查看错误日志**
   ```bash
   adb logcat | grep -E "(ERROR|Exception)"
   ```

3. **在代码中添加更多日志**
   ```kotlin
   // 在 UsersRepository.kt 中
   suspend fun refreshUsers() {
       try {
           Timber.d("开始请求用户数据")
           val users = usersApi.getUsers()
           Timber.i("成功获取${users.size}个用户")
           appDatabase.usersDao.insertUsers(users.asDatabaseModel())
           Timber.d("用户数据已保存到数据库")
       } catch (e: Exception) {
           Timber.e(e, "获取用户数据失败")
       }
   }
   ```

4. **检查网络配置**
   ```bash
   # 查看网络权限
   adb shell dumpsys package cn.agrolinking.wmst | grep permission
   ```

### 案例2：应用崩溃

#### 问题现象
- 应用启动后立即崩溃
- 某个功能触发崩溃

#### 调试步骤

1. **查看崩溃日志**
   ```bash
   adb logcat | grep -E "(FATAL|AndroidRuntime)"
   ```

2. **查看具体异常**
   ```bash
   adb logcat | grep "cn.agrolinking.wmst"
   ```

3. **分析堆栈信息**
   ```
   FATAL EXCEPTION: main
   Process: cn.agrolinking.wmst, PID: 12345
   java.lang.RuntimeException: Unable to start activity
   ```

### 案例3：性能问题

#### 问题现象
- 应用启动缓慢
- 页面切换卡顿
- 内存使用过高

#### 调试步骤

1. **查看内存使用**
   ```bash
   adb shell dumpsys meminfo cn.agrolinking.wmst
   ```

2. **查看CPU使用**
   ```bash
   adb shell top | grep wmst
   ```

3. **查看启动时间**
   ```bash
   adb logcat | grep "Displayed cn.agrolinking.wmst"
   ```

### 🍎 案例4：Apple Silicon Mac 模拟器崩溃

#### 问题现象
- 模拟器启动后立即崩溃
- 错误信息：`error messaging the mach port for IMKCFRunLoopWakeUpReliable`
- 崩溃报告显示 `qemu-system-aarch64-headless` 进程异常

#### 问题分析
```bash
# 检查崩溃日志中的关键信息
# 1. 系统版本：macOS 15.5.0 (Sequoia)
# 2. 架构：ARM64 (Apple Silicon)
# 3. 崩溃位置：libsystem_kernel.dylib
# 4. 涉及进程：qemu-system-aarch64-headless
```

#### 解决步骤

1. **立即停止所有模拟器进程**
   ```bash
   # 查找并终止模拟器进程
   ps aux | grep emulator
   kill -9 [PID]  # 替换为实际的进程ID
   ```

2. **清理ADB连接**
   ```bash
   adb kill-server
   adb start-server
   ```

3. **使用无头模式重新启动**
   ```bash
   # 使用经过验证的启动参数
   $ANDROID_HOME/emulator/emulator -avd Medium_Phone_API_35 \
     -gpu swiftshader_indirect \
     -no-snapshot \
     -no-audio \
     -no-window &
   ```

4. **验证启动成功**
   ```bash
   # 等待足够的启动时间
   sleep 30
   
   # 检查设备连接
   adb devices
   # 期望输出：emulator-5554   device
   
   # 检查模拟器进程
   ps aux | grep emulator | grep -v grep
   ```

5. **测试应用部署**
   ```bash
   # 构建并安装应用
   ./gradlew clean assembleDebug
   ./gradlew installDebug
   
   # 启动应用
   adb shell am start -n cn.agrolinking.wmst/.ui.MainActivity
   
   # 验证应用运行
   adb shell ps | grep wmst
   ```

#### 预防措施

1. **使用项目提供的启动脚本**
   ```bash
   # 使用项目中的专用启动脚本
   ./scripts/start-emulator-apple-silicon.sh
   
   # 或指定特定的AVD
   ./scripts/start-emulator-apple-silicon.sh Pixel_6_API_34
   ```

2. **手动创建启动脚本**
   ```bash
   # 创建 start-emulator.sh
   #!/bin/bash
   echo "🚀 启动Apple Silicon Mac无头模拟器..."
   
   # 检查并清理现有进程
   pkill -f emulator
   adb kill-server
   adb start-server
   
   # 启动无头模拟器
   $ANDROID_HOME/emulator/emulator -avd Medium_Phone_API_35 \
     -gpu swiftshader_indirect \
     -no-snapshot \
     -no-audio \
     -no-window &
   
   echo "⏳ 等待模拟器启动..."
   sleep 30
   
   # 验证启动
   echo "📱 检查设备状态："
   adb devices
   
   echo "✅ 模拟器启动完成！"
   ```

2. **设置环境变量**
   ```bash
   # 在 ~/.zshrc 或 ~/.bash_profile 中添加
   export ANDROID_EMULATOR_USE_SYSTEM_LIBS=1
   export ANDROID_EMULATOR_FORCE_32BIT=0
   ```

#### 成功指标
- ✅ `adb devices` 显示 `emulator-5554   device`
- ✅ 应用成功安装：`./gradlew installDebug` 无错误
- ✅ 应用成功启动：`adb shell ps | grep wmst` 显示进程
- ✅ 日志正常输出：`adb logcat -s "WMST"` 有内容

## 📊 性能调试

### 内存调试

```bash
# 详细内存信息
adb shell dumpsys meminfo cn.agrolinking.wmst

# 内存使用概览
adb shell dumpsys meminfo cn.agrolinking.wmst | grep -E "(TOTAL|Native|Dalvik)"

# GC信息
adb logcat | grep "GC"
```

### CPU调试

```bash
# 实时CPU使用
adb shell top | grep wmst

# CPU使用历史
adb shell dumpsys cpuinfo | grep wmst
```

### 网络调试

```bash
# 网络统计
adb shell dumpsys netstats detail

# 网络使用情况
adb shell dumpsys netstats | grep cn.agrolinking.wmst
```

### 启动时间分析

```bash
# 应用启动时间
adb logcat | grep "Displayed cn.agrolinking.wmst"

# Activity启动时间
adb shell am start -W cn.agrolinking.wmst/.MainActivity
```

## 🏆 最佳实践

### 日志规范

1. **日志级别使用**：
   - `Timber.d()` - 调试信息（开发时使用）
   - `Timber.i()` - 一般信息（重要流程节点）
   - `Timber.w()` - 警告信息（可能的问题）
   - `Timber.e()` - 错误信息（异常和错误）

2. **日志内容规范**：
   ```kotlin
   // ✅ 好的日志
   Timber.d("用户点击登录按钮，用户名: $username")
   Timber.i("用户登录成功，用户ID: $userId")
   Timber.w("网络请求超时，正在重试第${retryCount}次")
   Timber.e(exception, "登录失败，错误码: $errorCode")
   
   // ❌ 不好的日志
   Timber.d("click")
   Timber.i("success")
   Timber.e("error")
   ```

### 调试策略

1. **渐进式调试**：
   - 从最简单的地方开始
   - 一次只改一个地方
   - 先验证假设，再修改代码

2. **系统性调试**：
   - 先看日志，再看代码
   - 理解数据流向
   - 检查边界条件

3. **工具结合使用**：
   - 日志 + 断点调试
   - 静态分析 + 动态调试
   - 本地测试 + 真机测试

### 开发节奏

```
编写代码 → 添加日志 → 构建安装 → 查看日志 → 修复问题 → 重复
```

### 版本管理

1. **提交前检查**：
   ```bash
   ./gradlew clean build          # 确保构建成功
   ./gradlew test                 # 运行单元测试
   ./gradlew lint                 # 代码风格检查
   ```

2. **调试信息清理**：
   - 提交前删除调试用的临时日志
   - 保留重要的业务日志
   - 确保Release版本不输出调试信息

## 📚 常见问题与解决方案

### 构建问题

#### Java版本不兼容
```bash
# 问题：Android Gradle Plugin 8.2.2 requires Java 11
# 解决：设置正确的JAVA_HOME
export JAVA_HOME=/opt/homebrew/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home
```

#### Gradle同步失败
```bash
# 清理缓存
./gradlew clean
rm -rf .gradle
./gradlew build
```

### 设备连接问题

#### 设备未识别
```bash
# 重启ADB服务
adb kill-server
adb start-server
adb devices
```

#### 权限问题
```bash
# 重新授权设备
adb shell settings put global development_settings_enabled 1
```

### 应用问题

#### 应用无法启动
```bash
# 查看启动异常
adb logcat | grep -E "(FATAL|AndroidRuntime)"

# 检查清单文件配置
adb shell dumpsys package cn.agrolinking.wmst
```

#### 网络请求失败
```bash
# 检查网络权限
adb shell dumpsys package cn.agrolinking.wmst | grep permission

# 查看网络状态
adb shell dumpsys connectivity
```

## 🎯 快速开始调试

### 第一次调试步骤

1. **环境检查**
   ```bash
   java --version                 # 确认Java 17+
   adb devices                    # 确认设备连接
   ```

2. **构建安装**
   ```bash
   ./gradlew clean
   ./gradlew installDebug
   ```

3. **启动日志监控**
   ```bash
   adb logcat -c
   adb logcat | grep "cn.agrolinking.wmst"
   ```

4. **运行应用并观察日志**
   - 启动应用
   - 操作各个功能
   - 观察日志输出

### 日常调试工作流

```bash
# 开发调试一键脚本
#!/bin/bash
echo "🧹 清理项目..."
./gradlew clean

echo "🏗️ 构建安装..."
./gradlew installDebug

echo "📱 启动应用..."
adb shell am start -n cn.agrolinking.wmst/.MainActivity

echo "📝 开始日志监控..."
adb logcat | grep "万亩数田"
```

---

## 📞 技术支持

如果在调试过程中遇到问题，可以：

1. **查看项目README.md**了解基础配置
2. **搜索相关错误信息**寻找解决方案
3. **提交Issue**描述具体问题和复现步骤

---

**最后更新**: 2025/06/15  
**文档版本**: v1.0  
**适用项目**: 万亩数田 (WMST) v1.0 