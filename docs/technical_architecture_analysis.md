# 技术架构分析与选型

基于业务需求和非功能性需求（NFR）分析，本文档阐述为什么选择Android原生开发而非微信小程序，并定义相应的技术架构。

## 1. 微信小程序 vs Android原生 - 能力对比

### 1.1 弱网络/离线能力对比

| 能力需求 | 微信小程序 | Android原生 | 差距分析 |
|----------|------------|-------------|----------|
| **离线数据缓存** | ≤10MB本地存储限制 | 无限制，可达GB级别 | 🚫 小程序无法满足大量地块、任务数据离线缓存 |
| **离线地图支持** | 依赖网络，无离线瓦片 | 支持离线地图瓦片下载 | 🚫 田间作业必需的离线地图功能缺失 |
| **后台数据同步** | 受限，5分钟后台限制 | 完全控制后台服务 | 🚫 无法实现持续的数据同步策略 |
| **后台GPS追踪** | 不支持后台定位 | 完整后台定位服务 | 🚫 无法实现作业轨迹持续记录 |
| **文件断点续传** | 不支持 | 完全支持 | 🚫 大文件（照片/视频）上传在弱网下体验差 |
| **网络状态监听** | 基础API | 完整网络状态管理 | 🚫 无法精确控制弱网重试策略 |

### 1.2 硬件能力对比

| 硬件需求 | 微信小程序 | Android原生 | 差距分析 |
|----------|------------|-------------|----------|
| **高精度GPS** | 基础定位API | 完整GPS控制 | 🚫 圈地功能需要高精度定位 |
| **相机控制** | 基础拍照 | 完整相机API | 🚫 无法实现专业农业拍照需求 |
| **照片处理** | 基础压缩 | 完整图像处理 | 🚫 无法实现EXIF信息、水印、批量处理 |
| **文件系统访问** | 受限沙盒 | 完整文件系统 | 🚫 无法灵活管理大量照片和数据文件 |
| **传感器访问** | 不支持 | 完整传感器API | 🚫 未来IoT设备集成受限 |
| **蓝牙/NFC** | 受限 | 完整支持 | 🚫 农机设备、RFID标签读取受限 |

### 1.3 性能与用户体验对比

| 性能指标 | 微信小程序 | Android原生 | 差距分析 |
|----------|------------|-------------|----------|
| **启动时间** | 2-4秒（含微信启动） | <2秒 | 🚫 无法满足NFR中≤2秒启动要求 |
| **内存控制** | 受微信限制 | 完全控制 | 🚫 大数据量处理时容易被杀死 |
| **帧率稳定性** | 受WebView限制 | 原生渲染 | 🚫 地图操作无法保证≥55FPS |
| **电量消耗** | 微信+小程序双重消耗 | 精确控制 | 🚫 无法满足8h待机≤3%耗电要求 |

## 2. 基于NFR的Android技术架构设计

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                        │
├─────────────────────────────────────────────────────────────┤
│  UI Components (Jetpack Compose)                            │
│  ├── WorkerApp (作业人员界面)                                │
│  ├── ManagerApp (基地管理员界面)                             │
│  └── SharedComponents (通用组件)                             │
├─────────────────────────────────────────────────────────────┤
│                    Business Layer                            │
├─────────────────────────────────────────────────────────────┤
│  ViewModels & Use Cases                                     │
│  ├── TaskManagement (工单管理)                              │
│  ├── RecordManagement (记录管理)                            │
│  ├── MapDrawing (圈地功能)                                   │
│  └── OfflineSync (离线同步)                                  │
├─────────────────────────────────────────────────────────────┤
│                     Data Layer                               │
├─────────────────────────────────────────────────────────────┤
│  Repositories                                               │
│  ├── Local: Room Database + File Storage                    │
│  ├── Remote: Retrofit + OkHttp                             │
│  └── Sync: WorkManager + Conflict Resolution               │
├─────────────────────────────────────────────────────────────┤
│                 Infrastructure Layer                         │
├─────────────────────────────────────────────────────────────┤
│  ├── GPS Service (高精度定位 + 后台轨迹追踪)                │
│  ├── Camera Service (专业拍照 + 图像处理)                   │
│  ├── File Management (照片管理 + 批量处理)                  │
│  ├── Network Monitor (网络状态监控)                         │
│  ├── Background Sync (后台同步服务)                         │
│  └── Security (加密存储/传输)                               │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心技术栈选型

#### 2.2.1 UI层 - 满足性能要求
```kotlin
// Jetpack Compose - 原生渲染，保证≥55FPS
@Composable
fun MapDrawingScreen() {
    // 高性能地图组件
    MapView(
        modifier = Modifier.fillMaxSize(),
        onDraw = { canvas -> 
            // 原生Canvas绘制，确保流畅度
        }
    )
}
```

#### 2.2.2 离线存储 - 满足大容量缓存需求
```kotlin
// Room Database - 支持GB级别数据存储
@Database(
    entities = [Task::class, Record::class, PlotData::class],
    version = 1
)
abstract class AgriDatabase : RoomDatabase() {
    // 支持复杂查询和大数据量处理
}

// 文件存储 - 离线地图瓦片
class OfflineMapManager {
    fun downloadMapTiles(bounds: LatLngBounds) {
        // 下载并缓存地图瓦片，支持离线使用
    }
}
```

#### 2.2.3 网络层 - 满足弱网重试需求
```kotlin
// OkHttp + Retrofit - 完整网络控制
class NetworkConfig {
    val okHttpClient = OkHttpClient.Builder()
        .addInterceptor(RetryInterceptor()) // 指数退避重试
        .addInterceptor(NetworkMonitorInterceptor()) // 网络状态监控
        .build()
}

// 断点续传实现
class FileUploadService {
    suspend fun uploadWithResume(file: File): Result<String> {
        // 支持大文件分片上传和断点续传
    }
}
```

#### 2.2.4 后台服务 - 满足持续同步需求
```kotlin
// WorkManager - 可靠的后台任务
class DataSyncWorker : CoroutineWorker() {
    override suspend fun doWork(): Result {
        // 后台数据同步，不受应用生命周期限制
        return syncOfflineData()
    }
}

// 后台GPS追踪服务
class LocationTrackingService : Service() {
    private val locationManager by lazy { getSystemService<LocationManager>() }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        startForegroundService() // 前台服务，持续运行
        startLocationTracking()
        return START_STICKY // 系统杀死后自动重启
    }
    
    private fun startLocationTracking() {
        locationManager?.requestLocationUpdates(
            LocationManager.GPS_PROVIDER,
            1000L, // 1秒间隔
            1.0f,  // 1米精度
            locationListener
        )
    }
}
```

#### 2.2.5 照片处理 - 满足农业拍照需求
```kotlin
// 专业相机控制
class AgriCameraManager {
    fun captureWithMetadata(
        location: Location,
        taskId: String,
        plotId: String
    ): CaptureResult {
        return camera2API.capture {
            // 高分辨率拍照
            setImageFormat(ImageFormat.JPEG)
            setJpegQuality(95)
            
            // 嵌入农业元数据
            setExifData {
                gpsLocation = location
                userComment = "TaskID:$taskId,PlotID:$plotId"
                dateTime = System.currentTimeMillis()
            }
        }
    }
}

// 照片批量处理
class PhotoProcessor {
    suspend fun processPhotos(photos: List<Photo>): List<ProcessedPhoto> {
        return photos.map { photo ->
            withContext(Dispatchers.Default) {
                ProcessedPhoto(
                    original = photo,
                    compressed = compressForUpload(photo),
                    thumbnail = generateThumbnail(photo),
                    watermark = addWatermark(photo, getWatermarkInfo())
                )
            }
        }
    }
    
    private fun addWatermark(photo: Photo, info: WatermarkInfo): Photo {
        // 添加时间、位置、作业人员信息水印
        return photo.addOverlay {
            text("${info.timestamp} | ${info.location}")
            text("作业员: ${info.workerName}")
            text("地块: ${info.plotName}")
        }
    }
}
```

### 2.3 关键NFR实现策略

#### 2.3.1 离线模式实现
```kotlin
class OfflineFirstRepository {
    suspend fun getTasks(): Flow<List<Task>> = flow {
        // 1. 先返回本地缓存数据
        emit(localDataSource.getTasks())
        
        try {
            // 2. 尝试从网络更新
            val remoteData = remoteDataSource.getTasks()
            localDataSource.saveTasks(remoteData)
            emit(remoteData)
        } catch (e: NetworkException) {
            // 3. 网络失败时继续使用缓存
            Timber.d("Using cached data due to network error")
        }
    }
}
```

#### 2.3.2 数据冲突解决
```kotlin
class ConflictResolver {
    fun resolveTaskConflict(local: Task, remote: Task): Task {
        return when {
            local.version > remote.version -> local
            local.version < remote.version -> remote
            else -> mergeTaskData(local, remote) // 智能合并
        }
    }
}
```

#### 2.3.3 后台GPS轨迹追踪
```kotlin
class LocationTracker {
    private val trackingRepository = TrackingRepository()
    
    suspend fun startTracking(taskId: String) {
        val trackingSession = TrackingSession(
            taskId = taskId,
            startTime = System.currentTimeMillis(),
            points = mutableListOf()
        )
        
        locationFlow
            .filter { it.accuracy < 10.0 } // 精度过滤
            .sample(1000) // 1秒采样
            .collect { location ->
                trackingSession.points.add(
                    TrackPoint(
                        lat = location.latitude,
                        lng = location.longitude,
                        timestamp = System.currentTimeMillis(),
                        accuracy = location.accuracy
                    )
                )
                
                // 本地存储，定期上传
                trackingRepository.saveTrackPoint(trackingSession.id, location)
            }
    }
}
```

#### 2.3.4 照片管理与处理
```kotlin
class PhotoManager {
    private val photoStorage = PhotoStorage()
    
    suspend fun captureAndProcess(
        taskId: String,
        plotId: String,
        location: Location
    ): ProcessedPhoto {
        // 1. 拍照并嵌入元数据
        val rawPhoto = agriCamera.captureWithMetadata(location, taskId, plotId)
        
        // 2. 生成多种规格
        val processedPhoto = ProcessedPhoto(
            id = UUID.randomUUID().toString(),
            taskId = taskId,
            plotId = plotId,
            location = location,
            timestamp = System.currentTimeMillis(),
            
            // 原图 (高质量存储)
            originalPath = photoStorage.saveOriginal(rawPhoto),
            
            // 压缩图 (上传用)
            compressedPath = photoStorage.saveCompressed(rawPhoto, quality = 70),
            
            // 缩略图 (列表显示)
            thumbnailPath = photoStorage.saveThumbnail(rawPhoto, size = 200),
            
            // 水印图 (报告用)
            watermarkedPath = photoStorage.saveWatermarked(
                rawPhoto, 
                WatermarkInfo(location, taskId, getCurrentUser())
            )
        )
        
        // 3. 离线存储，待网络恢复后上传
        photoRepository.saveForUpload(processedPhoto)
        
        return processedPhoto
    }
    
    suspend fun batchUploadPhotos() {
        val pendingPhotos = photoRepository.getPendingUploads()
        
        pendingPhotos.forEach { photo ->
            try {
                // 断点续传上传
                val uploadResult = fileUploadService.uploadWithResume(
                    file = File(photo.compressedPath),
                    metadata = photo.toMetadata()
                )
                
                if (uploadResult.isSuccess) {
                    photo.uploadStatus = UploadStatus.COMPLETED
                    photo.remoteUrl = uploadResult.url
                    photoRepository.update(photo)
                }
            } catch (e: Exception) {
                // 上传失败，保持待上传状态
                Timber.w(e, "Photo upload failed, will retry later")
            }
        }
    }
}
```

#### 2.3.5 性能监控
```kotlin
class PerformanceMonitor {
    fun trackStartupTime() {
        // 监控启动时间，确保≤2秒
    }
    
    fun trackFrameRate() {
        // 监控帧率，确保≥55FPS
    }
    
    fun trackBatteryUsage() {
        // 监控电量消耗，确保8h≤3%
    }
    
    fun trackBackgroundServices() {
        // 监控后台服务运行状态
        // GPS追踪服务、数据同步服务等
    }
}
```

## 3. 部署与运维架构

### 3.1 应用分发策略
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   企业应用商店   │    │   第三方应用市场  │    │    直接APK分发   │
│                │    │                │    │                │
│ • 内部分发      │    │ • 华为应用市场   │    │ • 现场安装      │
│ • 版本控制      │    │ • 小米应用商店   │    │ • U盘分发       │
│ • 权限管理      │    │ • OPPO软件商店  │    │ • 二维码下载    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 3.2 远程配置与监控
```kotlin
// Firebase Remote Config - 远程开关控制
class FeatureFlags {
    val enableOfflineMode: Boolean by remoteConfig("offline_mode", true)
    val maxCacheSize: Long by remoteConfig("max_cache_size", 300L * 1024 * 1024)
}

// Crashlytics - 崩溃监控
class CrashReporter {
    fun reportCrash(exception: Throwable) {
        FirebaseCrashlytics.getInstance().recordException(exception)
    }
}
```

## 4. 开发与测试策略

### 4.1 模块化架构
```
app/
├── feature-worker/          # 作业人员功能模块
├── feature-manager/         # 管理员功能模块  
├── feature-map/            # 地图圈划功能模块
├── core-data/              # 数据层核心
├── core-network/           # 网络层核心
├── core-ui/                # UI组件库
└── core-testing/           # 测试工具
```

### 4.2 测试策略
```kotlin
// 离线场景测试
@Test
fun testOfflineTaskExecution() {
    // 模拟网络断开
    networkSimulator.disconnect()
    
    // 验证离线功能正常
    val result = taskRepository.executeTask(taskId)
    assertThat(result.isSuccess).isTrue()
}

// 弱网场景测试  
@Test
fun testWeakNetworkRetry() {
    // 模拟弱网环境
    networkSimulator.setLatency(5000)
    networkSimulator.setPacketLoss(0.3)
    
    // 验证重试机制
    val result = dataSync.syncData()
    assertThat(result.retryCount).isGreaterThan(0)
}
```

## 5. 总结

### 5.1 为什么必须选择Android原生

1. **离线能力**：微信小程序10MB存储限制无法满足农业场景大数据量离线需求
2. **后台运行**：小程序无法后台持续运行，无法实现GPS轨迹追踪和数据同步
3. **硬件控制**：高精度GPS、专业相机控制、完整文件系统访问是核心功能基础
4. **照片处理**：需要EXIF元数据嵌入、水印添加、多规格生成等专业图像处理能力
5. **性能要求**：地图操作的流畅度和启动速度要求超出小程序能力
6. **扩展性**：未来IoT设备集成需要完整的硬件访问能力

### 5.2 架构优势

1. **满足所有NFR**：从离线存储到性能监控，全面满足非功能性需求
2. **模块化设计**：支持不同角色的差异化功能开发
3. **可扩展性**：为未来功能扩展预留架构空间
4. **运维友好**：支持远程配置、监控和问题诊断

这个技术架构确保了在农业弱网环境下的稳定运行，同时为未来的功能扩展提供了坚实的技术基础。 