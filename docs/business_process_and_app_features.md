# 农业生产管理业务流程与 App 功能拆分

## 1. 业务背景
本系统面向多租户企业，帮助农业企业对旗下多个基地（农场）进行统一的数字化管理，覆盖从土地规划、生产执行到收获及仓储的全过程。

## 2. 角色与责任
| 层级 | 角色 | 主要职责 |
|------|------|----------|
| 企业层 | 企业管理员 | 创建/维护基地、配置企业数据、分配权限 |
| 企业层 | 农业专家 | 对异常、方案审核、诊断提供支持 |
| 企业层 | 服务站人员 | 提供线下或远程技术服务 |
| 企业层 | 仓库管理员 | 农资库存、领用、采购管理 |
| 基地层 | 基地管理员 | 规划土地、下发工单、审核记录 |
| 基地层 | 作业人员 | 执行工单、上报作业记录 |
| 基地层 | 物联网管理员 | 设备运维、数据接入 |
| 基地层 | 农机管理员 | 农机设备档案、预约排程 |

## 3. 业务流程概览
```mermaid
graph TB
  subgraph 企业层
    tenant["企业"]
    enterprise_admin["企业管理员"]
    service_station["服务站人员"]
    warehouse_admin["仓库管理员"]
    agri_expert["农业专家"]
  end

  subgraph 农场层
    farm_base["基地 / 农场"]
    farm_admin["基地管理员"]
    worker["作业人员"]
    iot_mgr["物联网管理员"]
    machine_mgr["农机管理员"]
  end

  tenant -->|"创建 / 管理"| enterprise_admin
  enterprise_admin -->|"创建基地"| farm_base
  enterprise_admin -->|"分配管理员"| farm_admin
  farm_admin -->|"分配作业人员"| worker
  farm_base -->|"包含"| land_plot["土地地块 (Plot)"]
  farm_admin -->|"下发工单"| work_order["工单 (Task)"]
  worker -->|"提交"| record["作业记录 (Record)"]
  agri_expert -->|"诊断 / 建议"| work_order
  service_station -->|"技术服务"| farm_base
  warehouse_admin -->|"农资领用"| farm_base
  machine_mgr -->|"农机预约"| work_order
  iot_mgr -->|"设备数据对接"| farm_base
```

## 4. App 功能模块拆分
### 4.1 平台通用层
- 账户体系：手机号+密码/验证码，支持企业 SSO。
- 多租户切换：同设备多企业帐号登录。
- 消息中心：推送、任务提醒、系统公告。
- 数据同步与离线缓存：弱网/离线场景自动补传。
- 系统设置：语言、度量单位、权限管理。

### 4.2 企业层功能
- 企业信息与配置
- 基地管理：列表/创建/编辑/禁用，管理员分配
- 用户与角色管理
- 仓库农资：库存台账、领用/归还、采购计划、预警
- 服务站任务
- 专家诊断：异常分派、诊断记录、建议跟踪
- 报表/看板：多维 KPI、成本、产量、异常统计

### 4.3 基地层功能
- 地图 & GIS：圈地、分区、图层管理、颜色标识
- 种植计划：季节/作物、分区方案、版本管理
- 工单管理：模板、下发、进度跟踪、通知
- 作业记录：文字/图片/视频/定位，审核流
- 异常反馈：异常类型、专家协同
- 农机预约：设备档案、排程冲突检测、费用结算
- 收获管理：采收任务、产量录入、质检、入库
- 基地统计：产量、任务完成率、资源消耗

### 4.4 外部数据接入
- 气象：实况、预报、预警推送
- 土壤检测：养分、酸碱度、EC 等
- 物联网：实时/历史传感器数据、告警规则

### 4.5 未来扩展
- 溯源码：一码到底质量追溯
- 第三方电商 & 供应链对接
- 开放 API 平台

## 5. 里程碑建议
| 版本 | 核心特性 | 目标用户 | 说明 |
|------|---------|----------|------|
| v1.0 | 多租户 + 基地/土地 + 工单/记录 | 企业管理员、基地管理员、作业人员 | 建立生产闭环 |
| v1.1 | 种植计划 + 气象 & IoT 接入 | 同上 | 优化作业指导 |
| v1.2 | 仓库农资 + 农机预约 | 仓库 & 农机管理员 | 资源调度 |
| v2.0 | 专家诊断 + 报表看板 + 溯源码 | 企业决策层、专家 | 数据驱动闭环 |

## 6. 终端划分与用户体验
为了最大化贴合使用场景与用户习惯，功能不会简单按角色拆分，而是遵循「场景驱动 + 设备特性」原则：

### 6.1 典型使用场景
| 场景 | 主要使用者 | 使用时机 | 优先终端 | 设计理由 |
|------|-----------|---------|---------|---------|
| 田间作业执行 | 作业人员 | 现场巡田/施肥/采收 | **移动 App** | 需要相机拍照、GPS 定位、离线缓存；操作流程简短，随时记录 |
| 工单下发与跟踪 | 基地管理员 | 巡田途中 or 办公室 | **移动 App** / Web | 在地头可直接创建工单；批量操作或查看统计时使用 Web 大屏 |
| 基地 GIS 圈地 | 基地管理员 | 农场现场 & 办公室 | **移动 App** (勘界拍照) + Web (精细制图) | 地头拍照快速标记，后期在 Web 精细编辑坐标、多图层 |
| 企业配置 & 权限 | 企业管理员 | 办公室 | **Web 管理后台** | 表格/树形批量操作，适合大屏 & 键鼠输入 |
| 专家诊断 | 农业专家 | 随时 | **移动 App** | 实时接收告警，查看多媒体记录并给出意见，移动场景更灵活 |
| 农资库存管理 | 仓库管理员 | 仓库现场 | **移动 App** + Web | 扫码出入库使用 App；盘点、报表使用 Web |
| 农机排程 | 农机管理员 | 车间/办公室 | **Web** | 复杂日历视图、冲突检测需要大屏 |
| 经营分析报表 | 企业决策层 | 会议/办公室 | **Web** + App 看板 | Web 深度钻取；App 查看实时 KPI 简报 |

### 6.2 功能–终端矩阵
| 功能模块 | 移动 App | Web 后台 | 备注 |
|-----------|----------|----------|------|
| 登录/多租户切换 | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | |
| 消息 & 推送 | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#f39c12;">△</span>（仅列表） | 即时性需求主要在 App |
| 基地列表 & 地图总览 | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | |
| 圈地/快速分区 | <span style="color:#f39c12;">△</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | 二者配合，支持草稿同步 |
| 种植计划编辑 | <span style="color:#f39c12;">△</span>（查看 + 简单调整） | <span style="color:#2ecc71;font-weight:bold">✔️</span> | 复杂表格/历史版本依赖 Web |
| 工单创建/派发 | <span style="color:#f39c12;">△</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | |
| 工单进度跟踪 | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | |
| 作业记录上报 | <span style="color:#e74c3c;">✖</span> | <span style="color:#f39c12;">△</span>（代录） | 必须利用拍照/定位离线能力 |
| 作业记录审核 | <span style="color:#f39c12;">△</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | 多维度批量审核更适合 Web |
| 异常上报 | <span style="color:#e74c3c;">✖</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | 即时拍照上传 |
| 专家诊断反馈 | <span style="color:#e74c3c;">✖</span> | <span style="color:#f39c12;">△</span> | 图文/语音留言均可 |
| 气象/IOT 监控 | <span style="color:#2ecc71;font-weight:bold">✔️</span>（简要 + 告警） | <span style="color:#2ecc71;font-weight:bold">✔️</span>（曲线 & 历史） | 大量历史数据在 Web 查看 |

<span style="color:#2ecc71;font-weight:bold">✔️</span>：核心支持；<span style="color:#f39c12;">△</span>：轻量功能；<span style="color:#e74c3c;">✖</span>：不提供/跳转其他终端

### 6.3 设计原则
1. **移动先行，离线为王**：任何需要在田间执行/记录的流程，必须保证离线可用，网络恢复后自动同步。
2. **大屏优先的批量与分析**：涉及批量数据录入、复杂报表、GIS 精细编辑的操作放到 Web。
3. **一致的权限模型**：无论终端，均复用同一后台权限控制，避免角色切换混乱。
4. **任务驱动首页**：App 首页以"待办任务 + 告警"为核心；Web 首页以"关键指标 + 全局概况"为核心。
5. **端间无缝衔接**：例如圈地草稿可在 App 标注后即时出现在 Web 编辑界面；工单派发后实时推送到作业人员 App。

> **下一步**：根据此矩阵梳理 API 需求与前端技术栈差异（React Web / Kotlin Multiplatform 等），并评估离线同步方案（SQLite + CRDT 或 Firestore 等）。 

## 7. App 端角色功能映射
下表细化了各角色在 **移动 App** 中常用或必须具备的功能。✔️ 表示核心必备；△ 表示可选/只读；✖ 表示无权限或无需在 App 中使用。

| 功能模块 | 企业管理员 | 基地管理员 | 作业人员 | 农业专家 | 服务站人员 | 仓库管理员 | 农机管理员 | 物联网管理员 |
|-----------|-----------|-----------|---------|---------|-----------|-----------|-----------|-------------|
| 登录/多租户切换 | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> |
| 消息 & 推送 | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> |
| 基地列表 & 地图总览 | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#f39c12;">△</span> | <span style="color:#f39c12;">△</span> | <span style="color:#f39c12;">△</span> | <span style="color:#f39c12;">△</span> | <span style="color:#f39c12;">△</span> | <span style="color:#f39c12;">△</span> |
| 圈地/快速分区 | <span style="color:#f39c12;">△</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> |
| 种植计划查看 | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#f39c12;">△</span> | <span style="color:#f39c12;">△</span> | <span style="color:#f39c12;">△</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> |
| 工单创建/派发 | <span style="color:#f39c12;">△</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#f39c12;">△</span>（机作任务） | <span style="color:#e74c3c;">✖</span> |
| 工单进度跟踪 | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span>（查看个人任务） | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#f39c12;">△</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#f39c12;">△</span> |
| 作业记录上报 | <span style="color:#e74c3c;">✖</span> | <span style="color:#f39c12;">△</span>（代录） | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> |
| 作业记录审核 | <span style="color:#f39c12;">△</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span>（技术审核） | <span style="color:#f39c12;">△</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> |
| 异常上报 | <span style="color:#e74c3c;">✖</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> |
| 专家诊断反馈 | <span style="color:#e74c3c;">✖</span> | <span style="color:#f39c12;">△</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> |
| 农资扫码出入库 | <span style="color:#e74c3c;">✖</span> | <span style="color:#f39c12;">△</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> |
| 农资库存汇总 | <span style="color:#f39c12;">△</span> | <span style="color:#f39c12;">△</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#e74c3c;">✖</span> |
| 农机预约申请 | ✖ | ✔️ | ✔️（填写需求） | ✖ | ✖ | ✖ | ✔️ | ✖ |
| 农机排程查看 | <span style="color:#e74c3c;">✖</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#f39c12;">△</span> | ✖ | ✖ | ✖ | ✔️ | ✖ |
| 气象/IOT 告警 | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#f39c12;">△</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | ✖ | ✖ | <span style="color:#2ecc71;font-weight:bold">✔️</span> |
| KPI & 报表概览 | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#2ecc71;font-weight:bold">✔️</span> | <span style="color:#e74c3c;">✖</span> | <span style="color:#f39c12;">△</span> | <span style="color:#f39c12;">△</span> | <span style="color:#f39c12;">△</span> | <span style="color:#f39c12;">△</span> | <span style="color:#f39c12;">△</span> |

> 注：矩阵基于常见生产流程设计，可按企业自身权限策略灵活调整。 