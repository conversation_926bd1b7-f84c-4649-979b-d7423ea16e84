# 移动 App 非功能需求（NFR）

补充业务功能之外，移动端必须满足以下跨场景的非功能（Non-Functional）需求，尤其关注弱网络与无网络场景。

## 1. 网络与数据同步
| 需求 | 级别 | 说明 |
|------|------|------|
| 离线模式 | ✅ 必须 | • 关键数据（任务、作业记录、地块信息）离线缓存<br>• 断网可继续浏览/编辑，待在线后自动同步 |
| 弱网重试与断点续传 | ✅ 必须 | • 所有 API 请求内建指数退避重试<br>• 大文件（图片/视频）上传支持分片与续传 |
| 数据冲突解决 | 🟠 可选 | • 采用乐观锁或版本号，自动合并常见字段<br>• 冲突无法自动解决时提示用户人工选择 |
| 实时同步优先级 | ✅ 必须 | • 高优消息/告警采用 WebSocket/Pusher 推送<br>• 低优数据可延迟批量同步，节省流量 |

## 2. 性能与资源消耗
| 需求 | 级别 | 目标 |
|------|------|------|
| 首屏启动时间 | ✅ 必须 | ≤ 2 秒（冷启动） |
| 滑动帧率 | ✅ 必须 | 地图/List 页保持 ≥ 55 FPS |
| 离线磁盘占用 | 🟠 建议 | ≤ 300 MB（可配置清理策略） |
| 电量消耗 | ✅ 必须 | 后台待机 8h 耗电 ≤ 3% |

## 3. 可靠性
| 需求 | 级别 | 目标 |
|------|------|------|
| Crash 率 | ✅ 必须 | ≤ 0.2%（30 天滚动） |
| 日志采集 | ✅ 必须 | 本地持久化 + 弱网批量上传 |

## 4. 安全合规
| 需求 | 级别 | 说明 |
|------|------|------|
| 数据加密 | ✅ 必须 | • HTTPS/TLS 1.2+ 传输<br>• AES-256 本地加密敏感缓存 |
| 身份认证 | ✅ 必须 | • 企业 SSO + OAuth2/JWT<br>• Token 失效前自动刷新 |
| 隐私合规 | ✅ 必须 | • 遵守《个人信息保护法》<br>• 提供数据下载与注销能力 |

## 5. 可运维性
| 需求 | 级别 | 说明 |
|------|------|------|
| 远程配置 & 开关 | 🟠 可选 | • Feature Flag 平滑灰度
| 崩溃/性能监控 | ✅ 必须 | • 集成 Sentry/Firebase Crashlytics
| A/B 测试 | 🟠 可选 | • 关键流程数据驱动优化

> **图例**：✅ 必须 🟠 可选

以上 NFR 将与业务功能需求共同驱动移动 App 的架构与迭代优先级，确保在田间弱网/离线环境下依然提供稳定、高效且安全的使用体验。 